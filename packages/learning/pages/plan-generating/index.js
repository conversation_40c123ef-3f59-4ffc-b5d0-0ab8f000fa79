// 测试数据
const testData = {
  title: '如何提高沟通效率',
  trouble: '在团队讨论中经常无法清晰表达自己的想法，导致沟通低效',
  learningIntensity: 'medium', // easy, medium, hard
  learningDuration: 7 // 7天学习计划
};

const app = getApp();

Page({
  data: {
    // 生成状态
    isGenerating: true,
    generatingFailed: false,
    errorMessage: '',

    // 生成进度
    generatingProgress: 0,
    generatingStage: '正在分析学习需求...',
    generatingTips: '请耐心等待，AI正在设计符合您需求的个性化学习计划',

    // 计划数据
    theme: '人际沟通',
    planData: null,

    // 生成过程的提示文案
    stageTexts: [
      '正在分析学习需求...',
      '正在构建学习框架...',
      '正在设计学习内容...',
      '正在优化学习路径...',
      '正在生成学习计划...'
    ],
    tipTexts: [
      '请耐心等待，AI正在设计符合您需求的个性化学习计划',
      '高质量的学习计划需要精心设计，AI正在努力中',
      '我们正在为您打造专属的学习体验',
      '好的学习计划可以让您的学习更有效率'
    ]
  },

  onLoad: function (options) {
    // 从参数中获取计划基本信息
    const planTitle = options.title || '';
    const troubleContent = options.trouble || '';
    const learningIntensity = options.intensity || 'medium';
    const learningDuration = parseInt(options.duration) || 7;
    const themeId = options.themeId || 1;

    // 保存到页面实例，用于后续API调用
    this.planParams = {
      title: planTitle,
      trouble: troubleContent,
      learningIntensity: learningIntensity,
      learningDuration: learningDuration,
      themeId: themeId
    };

    // 开始模拟生成进度
    this.simulateGeneratingProgress();

    // 调用API生成计划
    this.generatePlan();
  },

  /**
   * 模拟生成进度
   */
  simulateGeneratingProgress: function () {
    const that = this;
    let progress = 0;
    let stageIndex = 0;
    let tipIndex = 0;

    // 随机更新提示文本
    function updateTip() {
      const randomIndex = Math.floor(Math.random() * that.data.tipTexts.length);
      that.setData({
        generatingTips: that.data.tipTexts[randomIndex]
      });
    }

    // 定期更新进度
    this.progressInterval = setInterval(() => {
      // 随机增加进度，但确保不会超过95%
      progress += Math.random() * 5 + 1;
      if (progress > 95) progress = 95;

      // 根据进度更新阶段文本
      if (progress > stageIndex * 20 && stageIndex < that.data.stageTexts.length - 1) {
        stageIndex++;
        that.setData({
          generatingStage: that.data.stageTexts[stageIndex]
        });
      }

      // 每隔一段时间更新提示文本
      tipIndex++;
      if (tipIndex % 5 === 0) {
        updateTip();
      }

      that.setData({
        generatingProgress: Math.floor(progress)
      });
    }, 800);

    // 首次更新提示
    updateTip();
  },

  /**
   * 调用API生成学习计划
   */
  generatePlan: function () {
    const that = this;

    // 是否使用测试模式（开发调试用）
    const useTestMode = true; // 设置为true启用测试模式

    if (useTestMode) {
      // 使用本地测试数据
      clearInterval(that.progressInterval);

      // 模拟API响应延迟
      setTimeout(() => {
        that.setData({
          generatingProgress: 100,
          isGenerating: false,
          planData: {
            enhancedTitle: '高效团队沟通技巧掌握',
            designPrinciple: '本学习计划采用循序渐进的方式，从沟通基础理论出发，逐步深入到实际应用场景。针对学习者在团队讨论中无法清晰表达想法的困扰，设计了系统化的训练流程，包括自我表达能力、倾听技巧、结构化思维和反馈机制等核心内容。',
            contentPlan: [
              {
                day: 1,
                title: '沟通基础与自我认知',
                content: '今天我们将学习沟通的基本模型和要素，理解沟通中常见的障碍，特别是自我表达不清的原因分析。'
              },
              {
                day: 2,
                title: '结构化表达方法',
                content: '今天重点学习PREP（观点-理由-例证-观点）和金字塔结构等表达框架，掌握如何将复杂想法简化并有条理地呈现。'
              },
              {
                day: 3,
                title: '主动倾听与提问技巧',
                content: '今天学习主动倾听的要素和技巧，理解有效提问如何帮助澄清思路和引导讨论。'
              },
              {
                day: 4,
                title: '简明扼要的表达艺术',
                content: '今天重点练习如何在有限时间内表达核心观点，学习使用比喻、类比等方法增强表达的生动性和可理解性。'
              },
              {
                day: 5,
                title: '非语言沟通与表达力增强',
                content: '今天学习肢体语言、语调、眼神接触等非语言沟通要素如何辅助和增强语言表达。'
              },
              {
                day: 6,
                title: '处理紧张和应对困难情境',
                content: '今天学习如何应对即兴表达、被质疑、意见冲突等挑战性沟通场景。'
              },
              {
                day: 7,
                title: '沟通效果评估与持续改进',
                content: '今天学习如何评估沟通效果，建立反馈机制，形成持续改进的习惯。'
              }
            ],
            tags: ['表达力', '倾听', '结构化', '反馈', '提问']
          }
        });
      }, 2000);

      return;
    }

    // 确定API路径
    const apiUrl = `${app.globalData.apiBaseUrl}/api/v1/ai/learning-plans/generate`;
    console.log('AI生成计划API请求地址:', apiUrl);

    wx.request({
      url: apiUrl,
      method: 'POST',
      data: this.planParams,
      header: {
        'content-type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: function (res) {
        if (res.statusCode === 200 && res.data.success) {
          // 清除进度模拟定时器
          clearInterval(that.progressInterval);

          // 将进度设置为100%，表示完成
          that.setData({
            generatingProgress: 100
          });

          // 获取计划内容
          const planContent = res.data.data.planContent;

          // 处理标签数据，确保它是字符串数组格式
          if (planContent && planContent.tags) {
            // 如果标签是对象数组，提取name属性
            if (planContent.tags.length > 0) {
              planContent.tags = planContent.tags.map(tag => (typeof tag === 'object' && tag !== null ? tag.name : tag));
            }
          }

          // 延迟显示结果，给用户一个完成的感觉
          setTimeout(() => {
            that.setData({
              isGenerating: false,
              planData: planContent
            });
          }, 800);
        } else {
          that.handleGenerationError(res.data.message || '生成失败，请稍后重试');
        }
      },
      fail: function (err) {
        console.error('生成计划API请求失败:', err);
        that.handleGenerationError('网络请求失败，请检查网络连接');
      }
    });
  },

  /**
   * 处理生成失败
   */
  handleGenerationError: function (message) {
    clearInterval(this.progressInterval);

    this.setData({
      isGenerating: false,
      generatingFailed: true,
      errorMessage: message
    });
  },

  /**
   * 重试生成
   */
  retryGeneration: function () {
    this.setData({
      isGenerating: true,
      generatingFailed: false,
      errorMessage: '',
      generatingProgress: 0,
      generatingStage: '正在分析学习需求...'
    });

    this.simulateGeneratingProgress();
    this.generatePlan();
  },

  /**
   * 返回修改
   */
  goBack: function () {
    wx.navigateBack();
  },

  /**
   * 修改计划
   */
  modifyPlan: function () {
    wx.navigateBack();
  },

  /**
   * 保存并使用计划
   */
  savePlan: function () {
    const that = this;

    wx.showLoading({
      title: '保存中...'
    });

    // 确保计划数据完整
    if (!this.data.planData) {
      wx.hideLoading();
      wx.showToast({
        title: '计划数据不完整，请重试',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 打印计划数据，方便调试
    console.log('准备保存的计划数据:', this.planParams);
    console.log('计划内容:', this.data.planData);

    // 构建请求数据
    const requestData = {
      ...this.planParams,
      planContent: this.data.planData
    };

    // API地址，确保使用正确的API版本路径
    const apiUrl = `${app.globalData.apiBaseUrl}/api/v1/learning-plans`;
    console.log('保存计划API请求地址:', apiUrl);

    wx.request({
      url: apiUrl,
      method: 'POST',
      data: requestData,
      header: {
        'content-type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: function (res) {
        wx.hideLoading();

        console.log('保存计划响应:', res);

        if (res.statusCode === 201 && res.data.success) {
          // 设置主题更新时间，通知首页刷新标签数据
          wx.setStorageSync('themeUpdateTime', Date.now());
          console.log('设置主题更新时间', Date.now());

          // 保存成功，跳转到计划详情页
          wx.showToast({
            title: '创建成功',
            icon: 'success',
            duration: 2000
          });

          setTimeout(() => {
            wx.redirectTo({
              url: `/pages/plan-detail/index?id=${res.data.data.plan.id}`
            });
          }, 1500);
        } else {
          // 保存失败，显示具体错误信息
          console.error('保存计划失败:', res);
          let errorMessage = '保存失败';

          if (res.data && res.data.message) {
            errorMessage = res.data.message;
          } else if (res.data && res.data.error) {
            errorMessage = res.data.error;
          }

          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: function (err) {
        console.error('请求失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败，请检查网络连接',
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload: function () {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
  }
});