/* pages/create-plan/index.wxss */
page {
  background-color: #f8f9fa;
}

.container {
  padding: 50rpx 30rpx 30rpx; /* 增加顶部内边距 */
  min-height: 100vh;
  box-sizing: border-box;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
  overflow: hidden;
}

/* 背景渐变效果 */
.container::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(55, 117, 245, 0.05) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: -1;
}

/* 背景装饰元素 */
.bg-decoration {
  position: fixed;
  border-radius: 50%;
  z-index: -1;
  opacity: 0.6;
}

.bg-circle-1 {
  width: 400rpx;
  height: 400rpx;
  top: -100rpx;
  right: -100rpx;
  background: radial-gradient(circle, rgba(55, 117, 245, 0.1) 0%, rgba(55, 117, 245, 0) 70%);
}

.bg-circle-2 {
  width: 600rpx;
  height: 600rpx;
  bottom: -200rpx;
  left: -200rpx;
  background: radial-gradient(circle, rgba(55, 117, 245, 0.08) 0%, rgba(55, 117, 245, 0) 70%);
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  background: radial-gradient(circle, rgba(55, 117, 245, 0.05) 0%, rgba(55, 117, 245, 0) 70%);
}

/* 头部样式 */
.header {
  margin-bottom: 50rpx;
  padding: 0 10rpx;
}

.header-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  position: relative;
  display: inline-block;
}

.header-title::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 0;
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #3775F5, #5C9DFF);
  border-radius: 3rpx;
}

.header-subtitle {
  font-size: 30rpx;
  color: #666;
  margin-top: 20rpx;
  letter-spacing: 1rpx;
}

/* 表单容器 */
.form-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 30rpx; /* 增加上边距，弥补移除标题后的空间 */
}

/* 步骤卡片包装器 */
.step-cards-wrapper {
  position: relative;
  width: 100%;
  height: 500rpx; /* 增加高度以适应新的布局 */
  margin-bottom: 30rpx;
  overflow: hidden;
  perspective: 1000rpx; /* 3D视角效果 */
  transform-style: preserve-3d; /* 保持3D效果 */
}

/* 步骤卡片容器 */
.step-card-container {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  transition: transform 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  will-change: transform;
  backface-visibility: hidden; /* 防止3D变换时的闪烁 */
}

/* 卡片内部样式 */
.card-inner {
  padding: 40rpx;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 移动动画类 */
.move-left {
  transform: translateX(-120%) scale(0.95);
  opacity: 0.7;
}

.move-right {
  transform: translateX(120%) scale(0.95);
  opacity: 0;
}

.move-center {
  transform: translateX(0) scale(1);
  opacity: 1;
}

/* 困扰问题卡片 */
.trouble-card {
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.9), rgba(240, 248, 255, 0.8));
  border-left: 6rpx solid #5C9DFF;
}

/* 学习方式卡片 */
.learning-style-card {
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.9), rgba(245, 255, 245, 0.8));
  border-left: 6rpx solid #4CAF50;
  position: relative;
  overflow: hidden;
}

.learning-style-card::before {
  content: "";
  position: absolute;
  top: -50rpx;
  right: -50rpx;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.learning-style-card::after {
  content: "";
  position: absolute;
  bottom: -30rpx;
  left: -30rpx;
  width: 150rpx;
  height: 150rpx;
  background: radial-gradient(circle, rgba(76, 175, 80, 0.08) 0%, rgba(76, 175, 80, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.learning-style-card .card-inner {
  padding: 30rpx 40rpx;
  justify-content: space-evenly;
  position: relative;
  z-index: 1;
}

/* 选项组 */
.option-group {
  margin-bottom: 20rpx; /* 减小底部间距 */
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx; /* 减小下边距 */
  position: relative;
  display: inline-block;
}

.option-label::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #4CAF50;
  border-radius: 2rpx;
}

.option-items {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.intensity-options {
  margin-bottom: 40rpx;
}

.duration-options {
  margin-top: 20rpx;
}

.option-item {
  width: 32%;
  height: 150rpx; /* 增加高度 */
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 10rpx; /* 调整内边距 */
  box-sizing: border-box;
  transition: all 0.3s ease;
  border: 2rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.option-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.option-item:active {
  transform: scale(0.98);
}

.option-item.selected {
  background-color: rgba(76, 175, 80, 0.15);
  border: 3rpx solid #4CAF50;
  box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.25);
  transform: translateY(-4rpx) scale(1.02);
}

.option-item.selected .option-text {
  color: #2E7D32;
}

.option-icon {
  font-size: 60rpx; /* 增大图标 */
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
  transition: transform 0.3s ease;
}

.option-item:active .option-icon {
  transform: scale(1.1);
}

.option-item.selected .option-icon {
  transform: scale(1.15);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1.15);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1.15);
  }
}

.option-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  transition: all 0.3s ease;
}

/* 困扰提示文本 */
.trouble-hint {
  font-size: 26rpx;
  color: #666;
  margin: -10rpx 0 20rpx 0;
  padding-left: 10rpx;
  border-left: 3rpx solid #5C9DFF;
  line-height: 1.4;
}

/* 主题介绍卡片 */
.theme-intro-card {
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
  border-left: 6rpx solid #3775F5;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 8rpx 30rpx rgba(55, 117, 245, 0.08);
}

.theme-intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.theme-intro-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
  background: linear-gradient(135deg, rgba(55, 117, 245, 0.1) 0%, rgba(55, 117, 245, 0.05) 100%);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(55, 117, 245, 0.1);
}

.theme-intro-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  display: inline-block;
}

.theme-intro-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  letter-spacing: 0.5rpx;
  padding-left: 96rpx; /* 与图标对齐 */
  text-align: justify;
  text-justify: inter-word;
}

/* 玻璃卡片基础样式 */
.glass-card {
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
  height: 100%; /* 确保卡片填充容器高度 */
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直排列 */
}

/* 卡片内部装饰 */
.glass-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

/* 表单组 */
.form-group {
  width: 100%;
}

.form-label {
  font-size: 36rpx; /* 增大字体 */
  font-weight: 600; /* 加粗 */
  color: #333;
  margin-bottom: 24rpx; /* 增加下边距 */
  position: relative;
  display: inline-block;
}

.form-label::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 0;
  width: 50rpx; /* 增加宽度 */
  height: 4rpx;
  background-color: #3775F5;
  border-radius: 2rpx;
}

/* 输入框容器 */
.input-container {
  position: relative;
  margin-bottom: 16rpx;
  flex: 1; /* 让输入容器占据剩余空间 */
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直排列 */
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 90rpx;
  background-color: rgba(245, 245, 245, 0.5);
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
  border: 1px solid rgba(55, 117, 245, 0.1);
  transition: all 0.3s ease;
}

.form-input:focus {
  background-color: rgba(255, 255, 255, 0.8);
  border-color: rgba(55, 117, 245, 0.3);
  box-shadow: 0 0 10rpx rgba(55, 117, 245, 0.1);
}

/* 文本域 */
.form-textarea {
  width: 100%;
  height: 200rpx; /* 调整高度 */
  background-color: rgba(245, 245, 245, 0.5);
  border-radius: 16rpx; /* 圆角 */
  padding: 30rpx; /* 内边距 */
  font-size: 32rpx; /* 字体大小 */
  color: #333;
  box-sizing: border-box;
  border: 1px solid rgba(55, 117, 245, 0.1);
  transition: all 0.3s ease;
  line-height: 1.6;
  flex: 1; /* 让文本域占据剩余空间 */
  min-height: 200rpx; /* 最小高度 */
}

.form-textarea:focus {
  background-color: rgba(255, 255, 255, 0.8);
  border-color: rgba(55, 117, 245, 0.3);
  box-shadow: 0 0 20rpx rgba(55, 117, 245, 0.15); /* 阴影效果 */
}

/* 输入计数器 */
.input-counter {
  position: absolute;
  right: 20rpx;
  bottom: 10rpx;
  font-size: 24rpx;
  color: #999;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}

/* 错误消息 */
.error-message {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 12rpx;
  padding-left: 10rpx;
  border-left: 4rpx solid #ff4d4f;
}

/* 主题列表 */
.theme-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

/* 主题项 */
.theme-item {
  width: calc(33.33% - 16rpx);
  height: 180rpx;
  background-color: rgba(245, 245, 245, 0.5);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.theme-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.theme-item.selected {
  background-color: rgba(230, 247, 255, 0.7);
  border: 2rpx solid #3775F5;
  box-shadow: 0 4rpx 20rpx rgba(55, 117, 245, 0.15);
  transform: translateY(-2rpx);
}

.theme-item:active {
  transform: scale(0.98);
}

.theme-icon {
  font-size: 56rpx;
  margin-bottom: 16rpx;
}

.theme-name {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  width: 100%;
  padding: 40rpx 0 60rpx;
}

/* 按钮组 */
.action-buttons-group {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.action-button {
  width: 45%;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.cancel-button, .back-button {
  background-color: rgba(245, 245, 245, 0.7);
  color: #666;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.cancel-button:active, .back-button:active {
  background-color: rgba(235, 235, 235, 0.8);
  transform: translateY(2rpx);
}

.submit-button {
  background: linear-gradient(135deg, #3775F5, #5C9DFF);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(55, 117, 245, 0.3);
}

.submit-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(55, 117, 245, 0.2);
}

.submit-button[disabled] {
  background: linear-gradient(135deg, #a0cfff, #c8e1ff);
  color: white;
  box-shadow: none;
}

/* 加载中状态 */
.loading-container {
  width: 100%;
  height: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(55, 117, 245, 0.1);
  border-top: 4rpx solid #3775F5;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  box-shadow: 0 0 20rpx rgba(55, 117, 245, 0.1);
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  letter-spacing: 1rpx;
}

/* 未登录状态过渡页面 */
.login-transition-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0 40rpx;
  height: 100vh; /* 使用视口高度 */
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: rgba(240, 248, 255, 0.95); /* 与页面背景色相近 */
}

.login-icon {
  font-size: 100rpx;
  margin-bottom: 60rpx;
  animation: pulse 2s infinite;
}

.login-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  text-align: center;
}

.login-subtitle {
  font-size: 34rpx;
  color: #666;
  margin-bottom: 80rpx;
  text-align: center;
}

.login-progress {
  width: 80%;
  height: 12rpx;
  background-color: rgba(55, 117, 245, 0.15);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}

.login-progress-bar {
  height: 100%;
  width: 30%;
  background: linear-gradient(90deg, #3775F5, #5C9DFF);
  border-radius: 6rpx;
  animation: progress 3s linear forwards;
  box-shadow: 0 0 10rpx rgba(55, 117, 245, 0.5);
}

.login-hint {
  font-size: 30rpx;
  color: #888;
  text-align: center;
  letter-spacing: 1rpx;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.9; }
  50% { transform: scale(1.15); opacity: 1; }
  100% { transform: scale(1); opacity: 0.9; }
}

@keyframes progress {
  0% { width: 0%; }
  10% { width: 15%; }
  30% { width: 40%; }
  60% { width: 65%; }
  80% { width: 85%; }
  100% { width: 100%; }
}

/* 错误状态 */
.error-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
  padding: 0 40rpx;
  height: 400rpx;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #ff7875, #ff4d4f);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(255, 77, 79, 0.2);
  position: relative;
  overflow: hidden;
}

.error-icon::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.error-subtext {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
  max-width: 80%;
}

.retry-button {
  width: 240rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #3775F5, #5C9DFF);
  color: white;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 20rpx rgba(55, 117, 245, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.retry-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.retry-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(55, 117, 245, 0.2);
}
