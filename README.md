# AIBUBB (AI互动泡泡) - 前端项目

## 项目概述

AIBUBB 是一个基于微信小程序的 AI 辅助学习平台前端项目，专注于提升用户的人际沟通能力。其核心理念是围绕用户个性化的**学习计划**，通过 AI 分析生成**标签**，并关联到**练习、观点、笔记**三种形式的学习内容。内容通过首页的**互动泡泡**和**广场社区**呈现，结合**学习统计**功能，旨在提供一个沉浸式、个性化、数据驱动的学习体验。

## 主要功能

*   创建和管理个性化学习计划界面
*   互动式泡泡学习界面（Canvas动画）
*   社区广场内容发现与分享界面
*   学习进度跟踪与统计分析可视化
*   用户注册、登录与个人中心界面
*   响应式设计与主题切换（亮色/暗色模式）

## 技术栈

本前端项目主要采用以下技术栈：

*   **前端框架:** 微信小程序原生开发
*   **UI组件:** 自研NebulaLearn UI设计系统
*   **动画引擎:** Canvas 2D API + 自研泡泡动画系统
*   **状态管理:** 微信小程序原生状态管理
*   **工具链:** Babel, Jest, ESLint, Prettier
*   **性能优化:** 虚拟列表、图片懒加载、离屏渲染

> **注意:** 本项目为纯前端项目。后端相关的技术架构、API设计等文档已移动到 `归档文档/` 目录中。

## 项目结构

```
.
├── pages/              # 微信小程序页面
│   ├── index/          # 首页 (泡泡交互界面)
│   ├── learn/          # 学习页面
│   ├── square/         # 广场页面
│   ├── profile/        # 个人中心页面
│   ├── plans/          # 学习计划页面
│   ├── create-plan/    # 创建学习计划页面
│   ├── plan-detail/    # 学习计划详情页面
│   ├── note-detail/    # 笔记详情页面
│   ├── statistics/     # 统计页面
│   └── test/           # 测试页面
├── components/         # 微信小程序自定义组件
│   ├── base/           # 基础UI组件 (NebulaLearn UI)
│   │   ├── button/     # 按钮组件
│   │   ├── card/       # 卡片组件
│   │   ├── icon/       # 图标组件
│   │   └── ...         # 其他基础组件
│   ├── business/       # 业务组件
│   │   ├── learning-plan-card/    # 学习计划卡片
│   │   ├── content-modal/         # 内容模态弹窗
│   │   ├── user-profile/          # 用户信息展示
│   │   └── ...                    # 其他业务组件
│   ├── bubble-canvas/  # 泡泡Canvas组件
│   ├── form/           # 表单组件
│   └── ...             # 其他组件
├── utils/              # 前端工具函数
│   ├── api/            # API请求模块
│   ├── api-client/     # API客户端
│   ├── performance/    # 性能监控工具
│   ├── models/         # 数据模型
│   └── ...             # 其他工具函数
├── assets/             # 静态资源
│   ├── icons/          # 图标资源
│   └── tabbar-icons/   # TabBar图标
├── styles/             # 样式文件
│   ├── design-system.js # 设计系统配置
│   ├── global.wxss     # 全局样式
│   └── variables.wxss  # 样式变量
├── behaviors/          # 微信小程序行为
├── docs/               # 前端文档
├── __tests__/          # 测试文件
├── mock-data/          # 模拟数据
├── scripts/            # 前端构建脚本
├── 归档文档/           # 归档的非前端文档
├── app.js              # 小程序应用入口
├── app.json            # 小程序全局配置
├── app.wxss            # 小程序全局样式
├── project.config.json # 微信开发者工具配置
└── *.md                # 前端相关文档
```

## 环境要求

*   **微信开发者工具** - 用于小程序开发和调试
*   **Node.js 16+** - 用于运行构建工具和测试
*   **Git** - 版本控制

## 快速启动

1.  **克隆仓库:**
    ```bash
    git clone [你的仓库地址]
    cd AIBUBB
    ```

2.  **安装依赖:**
    ```bash
    npm install
    ```

3.  **配置API环境:**
    *   在 `utils/api-client/config.js` 中配置API服务器地址
    *   开发环境默认连接到 `http://localhost:9090/api/v1`
    *   生产环境需要配置实际的API服务器地址

4.  **启动开发:**
    *   使用微信开发者工具导入项目根目录
    *   在开发者工具中编译并运行小程序
    *   支持热重载和实时预览

5.  **运行测试:**
    ```bash
    # 运行单元测试
    npm test

    # 运行性能测试
    npm run test:performance

    # 生成测试覆盖率报告
    npm run test:coverage
    ```

## 开发指南

### 组件开发
- 基础组件位于 `components/base/`，遵循NebulaLearn UI设计规范
- 业务组件位于 `components/business/`，封装具体业务逻辑
- 所有组件支持亮色/暗色主题切换

### 页面开发
- 页面文件位于 `pages/` 目录
- 遵循微信小程序页面生命周期
- 使用统一的API客户端进行数据请求

### 样式开发
- 使用 `styles/design-system.js` 中定义的设计令牌
- 支持响应式设计和主题切换
- 遵循BEM命名规范

## 文档索引

## 📚 前端文档分类索引

### 🎯 **核心文档** (项目概览与入门)

| 文档 | 描述 | 状态 |
|------|------|------|
| [README.md](./README.md) | 项目入口文档，快速启动指南 | ✅ 最新 |
| [AIBUBB视觉设计文档.md](./AIBUBB视觉设计文档.md) | 视觉设计规范和UI组件设计指南 | ✅ 最新 |
| [首页Canvas组件说明.md](./首页Canvas组件说明.md) | 首页核心泡泡交互组件详细说明 | ✅ 最新 |
| [README-TABBAR.md](./README-TABBAR.md) | TabBar图标处理方式和配置说明 | ✅ 最新 |
| [AIBUBB软件前端功能文档.md](./AIBUBB软件前端功能文档.md) | 前端功能详细文档 | ✅ 最新 |

### 📋 **开发计划与规划** (项目管理)

| 文档 | 描述 | 阶段 | 状态 |
|------|------|------|------|
| [docs/项目管理/AIBUBB前端2.0阶段优先级工作计划.md](./docs/项目管理/AIBUBB前端2.0阶段优先级工作计划.md) | 前端2.0阶段工作计划 | 2.0 | ✅ 已完成 |
| [docs/项目管理/前端开发3.0工作计划.md](./docs/项目管理/前端开发3.0工作计划.md) | 前端开发3.0计划 | 3.0 | 📋 规划中 |
| [docs/项目管理/前端系统升级2.0阶段指导文档.md](./docs/项目管理/前端系统升级2.0阶段指导文档.md) | 前端系统升级指导 | 2.0 | ✅ 已完成 |
| [docs/项目管理/前端系统升级综合规划.md](./docs/项目管理/前端系统升级综合规划.md) | 前端系统升级综合规划 | 全阶段 | ✅ 最新 |
| [docs/项目管理/基于外部顾问反馈的前端工作清单.md](./docs/项目管理/基于外部顾问反馈的前端工作清单.md) | 基于外部反馈的工作清单 | 改进 | ✅ 最新 |

### 📊 **工作报告与评估** (项目跟踪)

| 文档 | 描述 | 类型 | 状态 |
|------|------|------|------|
| [docs/工作报告/AIBUBB项目前端工作完成度评估报告.md](./docs/工作报告/AIBUBB项目前端工作完成度评估报告.md) | 前端工作完成度评估 | 评估报告 | ✅ 最新 |
| [docs/工作报告/前端2.0阶段工作完成总结报告.md](./docs/工作报告/前端2.0阶段工作完成总结报告.md) | 前端2.0阶段总结 | 总结报告 | ✅ 已完成 |
| [docs/工作报告/前端工作完成度独立调查8阶段.md](./docs/工作报告/前端工作完成度独立调查8阶段.md) | 前端工作独立调查 | 调查报告 | ✅ 最新 |
| [docs/工作报告/前端项目独立调查-阶段性汇总1.md](./docs/工作报告/前端项目独立调查-阶段性汇总1.md) | 前端项目调查汇总 | 汇总报告 | ✅ 最新 |
| [docs/工作报告/前端工作检查框架.md](./docs/工作报告/前端工作检查框架.md) | 前端工作检查框架 | 质量控制 | ✅ 最新 |

### ⚡ **性能优化** (技术优化)

| 文档 | 描述 | 重点领域 | 状态 |
|------|------|----------|------|
| [docs/性能优化/PERFORMANCE-OPTIMIZATION.md](./docs/性能优化/PERFORMANCE-OPTIMIZATION.md) | 前端性能优化指南 | 全面优化 | ✅ 已更新 |
| [docs/性能优化/bubble-performance-optimization.md](./docs/性能优化/bubble-performance-optimization.md) | 泡泡性能优化 | Canvas动画 | ✅ 最新 |
| [docs/性能优化/performance-optimization-plan.md](./docs/性能优化/performance-optimization-plan.md) | 性能优化计划 | 优化策略 | ✅ 最新 |
| [docs/性能优化/performance-test-plan.md](./docs/性能优化/performance-test-plan.md) | 性能测试计划 | 测试策略 | ✅ 最新 |

### 🎨 **设计与实现** (UI/UX)

| 文档 | 描述 | 重点内容 | 状态 |
|------|------|----------|------|
| [docs/设计实现/DESIGN_CONCEPT_2.0.md](./docs/设计实现/DESIGN_CONCEPT_2.0.md) | 设计概念2.0 | 设计理念 | ✅ 最新 |
| [docs/设计实现/visual-design-implementation-plan.md](./docs/设计实现/visual-design-implementation-plan.md) | 视觉设计实施计划 | 设计落地 | ✅ 最新 |
| [docs/设计实现/business-components-update-plan.md](./docs/设计实现/business-components-update-plan.md) | 业务组件更新计划 | 组件升级 | ✅ 已完成 |
| [docs/设计实现/implementation-plan.md](./docs/设计实现/implementation-plan.md) | 实施计划 | 具体实施 | ✅ 最新 |
| [docs/设计实现/overall-implementation-plan.md](./docs/设计实现/overall-implementation-plan.md) | 总体实施计划 | 整体规划 | ✅ 最新 |

### 📖 **技术文档** (开发指南)

| 文档 | 描述 | 目标用户 | 状态 |
|------|------|----------|------|
| [docs/base-components-guide.md](./docs/base-components-guide.md) | 基础组件使用指南 | 开发者 | ✅ 最新 |
| [docs/business-components.md](./docs/business-components.md) | 业务组件文档 | 开发者 | ✅ 最新 |
| [docs/design-system-implementation-guide.md](./docs/design-system-implementation-guide.md) | 设计系统实施指南 | 开发者 | ✅ 最新 |
| [docs/performance-optimization-best-practices.md](./docs/performance-optimization-best-practices.md) | 性能优化最佳实践 | 开发者 | ✅ 最新 |
| [docs/testing-guide.md](./docs/testing-guide.md) | 测试指南 | 开发者 | ✅ 最新 |
| [docs/frontend-engineering.md](./docs/frontend-engineering.md) | 前端工程化 | 开发者 | ✅ 最新 |
| [docs/icon-guidelines.md](./docs/icon-guidelines.md) | 图标使用指南 | 设计师/开发者 | ✅ 最新 |
| [docs/storage-service-guide.md](./docs/storage-service-guide.md) | 存储服务指南 | 开发者 | ✅ 最新 |
| [docs/TypeScript代码规范.md](./docs/TypeScript代码规范.md) | TypeScript代码规范 | 开发者 | ✅ 最新 |
| [docs/component-style-update-plan.md](./docs/component-style-update-plan.md) | 组件样式更新计划 | 开发者 | ✅ 最新 |

### 📋 **其他文档**

| 文档 | 描述 | 状态 |
|------|------|------|
| [docs/历史文档/TARO-MIGRATION-GUIDE.md](./docs/历史文档/TARO-MIGRATION-GUIDE.md) | Taro迁移指南（仅供参考） | ⚠️ 历史文档 |
| [文档索引.md](./文档索引.md) | 完整文档索引 | ✅ 需更新 |
| [前端文档分类管理方案.md](./前端文档分类管理方案.md) | 文档分类管理方案 | ✅ 最新 |

### 📁 **文档目录结构**

```
docs/
├── 项目管理/          # 开发计划与规划文档
├── 工作报告/          # 工作报告与评估文档
├── 性能优化/          # 性能优化相关文档
├── 设计实现/          # 设计与实现文档
├── 历史文档/          # 历史参考文档
├── base-components-guide.md           # 基础组件使用指南
├── business-components.md             # 业务组件文档
├── design-system-implementation-guide.md  # 设计系统实施指南
├── performance-optimization-best-practices.md  # 性能优化最佳实践
├── testing-guide.md                  # 测试指南
├── frontend-engineering.md           # 前端工程化
├── icon-guidelines.md                # 图标使用指南
├── storage-service-guide.md          # 存储服务指南
├── TypeScript代码规范.md             # TypeScript代码规范
└── component-style-update-plan.md    # 组件样式更新计划
```

### 🗂️ 归档文档

*   **[归档文档/](./归档文档/)** - 已归档的非前端文档
    *   包含后端、API、数据库、架构等相关文档
    *   详细索引请查看 `归档文档/README.md`

### 📋 其他文档

*   **[TARO-MIGRATION-GUIDE.md](./TARO-MIGRATION-GUIDE.md)** - Taro迁移指南（仅供参考）
*   **[文档索引.md](./文档索引.md)** - 完整的文档索引

## 贡献指南

### 代码风格

*   **微信小程序:** 请遵循微信官方推荐的编码规范，并使用 Prettier 进行代码格式化
*   **JavaScript:** 遵循 [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
*   **CSS/WXSS:** 遵循BEM命名规范，使用设计系统中定义的变量

### 组件开发规范

*   **基础组件:** 位于 `components/base/`，必须支持主题切换，遵循NebulaLearn UI设计规范
*   **业务组件:** 位于 `components/business/`，封装具体业务逻辑，可复用性优先
*   **组件文档:** 每个组件都需要包含详细的属性说明和使用示例

### 提交规范

*   请使用清晰、简洁的提交消息，遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范
*   示例:
    *   `feat: 添加泡泡动画性能优化`
    *   `fix: 修复主题切换时的样式问题`
    *   `docs: 更新组件使用文档`
    *   `perf: 优化Canvas渲染性能`

### 分支策略

*   `main`: 生产分支，保持稳定
*   `develop`: 开发分支，用于集成新功能
*   `feature/<feature-name>`: 功能开发分支，从 `develop` 创建
*   `hotfix/<issue-name>`: 紧急修复分支，从 `main` 创建

### 测试要求

*   新增功能必须包含相应的单元测试
*   性能敏感的组件需要包含性能测试
*   提交前运行 `npm test` 确保所有测试通过

## 文档维护建议

### 前端文档维护原则

**1. 组件文档同步更新:**
*   组件变更时必须同步更新文档
*   包含属性说明、事件说明和使用示例
*   维护组件的设计规范文档

**2. 性能文档持续更新:**
*   记录性能优化措施和效果
*   更新性能测试结果
*   维护性能最佳实践文档

**3. 设计系统文档:**
*   保持设计令牌和组件库文档的一致性
*   及时更新视觉设计规范
*   维护主题切换相关文档

**4. 归档管理:**
*   非前端相关文档已移至 `归档文档/` 目录
*   定期清理过时的前端文档
*   保持文档索引的准确性

---

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

*   **Issues:** 在GitHub上提交Issue
*   **文档问题:** 直接修改相关文档并提交PR
*   **技术讨论:** 参与项目讨论区

---

*最后更新：2025年1月*
*项目状态：纯前端项目，专注于微信小程序开发*