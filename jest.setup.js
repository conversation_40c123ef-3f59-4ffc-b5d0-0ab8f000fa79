// 全局测试设置

// 扩展Jest匹配器
expect.extend({
  // 自定义匹配器：检查值是否在指定范围内
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false
      };
    }
  }
});

// 模拟微信小程序环境
global.wx = {
  getSystemInfoSync: () => ({
    platform: 'devtools',
    windowWidth: 375,
    windowHeight: 667,
    pixelRatio: 2
  }),
  showToast: jest.fn(),
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
  showModal: jest.fn(() => Promise.resolve({ confirm: true })),
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  request: jest.fn(),
  navigateTo: jest.fn(),
  redirectTo: jest.fn(),
  switchTab: jest.fn(),
  navigateBack: jest.fn(),
  createSelectorQuery: jest.fn(() => ({
    select: jest.fn(() => ({
      boundingClientRect: jest.fn(callback => {
        callback({ width: 100, height: 100, top: 0, left: 0 });
        return { exec: jest.fn() };
      }),
      exec: jest.fn()
    })),
    exec: jest.fn()
  }))
};

// 模拟小程序全局函数
global.getCurrentPages = jest.fn(() => [
  {
    route: 'pages/index/index',
    options: {},
    setData: jest.fn()
  }
]);

global.getApp = jest.fn(() => ({
  globalData: {
    userInfo: null,
    api: {
      theme: {
        getThemes: jest.fn(() => Promise.resolve({ data: [] }))
      },
      tag: {
        getTags: jest.fn(() => Promise.resolve({ data: [] }))
      }
    }
  }
}));

global.Page = jest.fn();
global.Component = jest.fn();
global.App = jest.fn();
global.Behavior = jest.fn(() => ({}));

// 设置测试超时
jest.setTimeout(10000);

// 清除所有模拟函数的调用记录
beforeEach(() => {
  jest.clearAllMocks();
});
