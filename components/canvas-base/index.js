// components/canvas-base/index.js
// 基础画布组件

// 微信小程序组件定义
Component({
  properties: {
    // 组件属性
    canvasId: {
      type: String,
      value: 'canvas-base'
    },
    width: {
      type: Number,
      value: 375
    },
    height: {
      type: Number,
      value: 600
    }
  },

  data: {
    // 组件数据
    canvasContext: null,
    elements: []
  },

  methods: {
    // 初始化画布
    initCanvas() {
      const query = this.createSelectorQuery();
      query.select('#' + this.properties.canvasId)
        .fields({ node: true, size: true })
        .exec(res => {
          if (res[0]) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            const dpr = wx.getSystemInfoSync().pixelRatio;
            canvas.width = res[0].width * dpr;
            canvas.height = res[0].height * dpr;
            ctx.scale(dpr, dpr);

            this.setData({
              canvasContext: ctx
            });
          }
        });
    },

    // 清空画布
    clearCanvas() {
      const ctx = this.data.canvasContext;
      if (ctx) {
        ctx.clearRect(0, 0, this.properties.width, this.properties.height);
      }
    },

    // 绘制元素
    drawElements() {
      this.clearCanvas();
      // 子类可以重写此方法
    }
  },

  lifetimes: {
    attached() {
      this.initCanvas();
    }
  }
});