<!-- components/form/textarea/index.wxml -->
<view class="nl-textarea {{disabled ? 'disabled' : ''}} {{readonly ? 'readonly' : ''}} {{error ? 'error' : ''}} {{isFocused ? 'focused' : ''}}">
  <!-- 标签 -->
  <view class="textarea-label" wx:if="{{label}}">
    <text class="label-text">{{label}}</text>
    <text class="required-mark" wx:if="{{required}}">*</text>
  </view>
  
  <!-- 输入框容器 -->
  <view class="textarea-container">
    <textarea
      class="textarea-input"
      style="min-height: {{autoHeight ? minHeight : currentHeight}}rpx; max-height: {{maxHeight}}rpx;"
      value="{{currentValue}}"
      placeholder="{{placeholder}}"
      maxlength="{{maxlength}}"
      disabled="{{disabled}}"
      focus="{{focus}}"
      auto-height="{{autoHeight}}"
      cursor-spacing="{{cursorSpacing}}"
      show-confirm-bar="{{showConfirmBar}}"
      adjust-position="{{adjustPosition}}"
      hold-keyboard="{{holdKeyboard}}"
      bindinput="handleInput"
      bindfocus="handleFocus"
      bindblur="handleBlur"
      bindconfirm="handleConfirm"
      bindkeyboardheightchange="handleKeyboardHeightChange"
      bindlinechange="handleLineChange"
    />
    
    <!-- 清空按钮 -->
    <view class="clear-button" wx:if="{{currentValue && !disabled && !readonly}}" bindtap="clear">
      <text class="clear-icon">×</text>
    </view>
  </view>
  
  <!-- 底部信息 -->
  <view class="textarea-footer" wx:if="{{showCount || error}}">
    <!-- 字数统计 -->
    <view class="char-count" wx:if="{{showCount}}">
      <text class="current-count">{{currentValue.length}}</text>
      <text class="max-count" wx:if="{{maxlength > 0}}">/{{maxlength}}</text>
    </view>
    
    <!-- 错误信息 -->
    <view class="error-message" wx:if="{{error}}">
      <text class="error-text">{{error}}</text>
    </view>
  </view>
</view>