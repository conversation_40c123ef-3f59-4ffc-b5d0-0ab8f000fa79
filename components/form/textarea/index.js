// components/form/textarea/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 输入框的值
    value: {
      type: String,
      value: ''
    },
    // 占位符
    placeholder: {
      type: String,
      value: '请输入内容'
    },
    // 最大长度
    maxlength: {
      type: Number,
      value: -1
    },
    // 最小高度
    minHeight: {
      type: Number,
      value: 120
    },
    // 最大高度
    maxHeight: {
      type: Number,
      value: 300
    },
    // 是否自动调整高度
    autoHeight: {
      type: Boolean,
      value: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否只读
    readonly: {
      type: Boolean,
      value: false
    },
    // 是否显示字数统计
    showCount: {
      type: Boolean,
      value: false
    },
    // 是否必填
    required: {
      type: Boolean,
      value: false
    },
    // 错误信息
    error: {
      type: String,
      value: ''
    },
    // 标签文本
    label: {
      type: String,
      value: ''
    },
    // 是否聚焦
    focus: {
      type: Boolean,
      value: false
    },
    // 光标起始位置
    cursorSpacing: {
      type: Number,
      value: 0
    },
    // 是否显示确认栏
    showConfirmBar: {
      type: Boolean,
      value: true
    },
    // 选择键盘的类型
    adjustPosition: {
      type: Boolean,
      value: true
    },
    // 键盘弹起时，是否自动上推页面
    holdKeyboard: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentValue: '',
    isFocused: false,
    currentHeight: 120
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 输入事件处理
     */
    handleInput(e) {
      const value = e.detail.value;
      this.setData({
        currentValue: value
      });

      // 触发外部输入事件
      this.triggerEvent('input', {
        value: value,
        cursor: e.detail.cursor
      });

      // 触发change事件（兼容）
      this.triggerEvent('change', {
        value: value
      });
    },

    /**
     * 聚焦事件处理
     */
    handleFocus(e) {
      this.setData({
        isFocused: true
      });

      this.triggerEvent('focus', {
        value: e.detail.value,
        height: e.detail.height
      });
    },

    /**
     * 失焦事件处理
     */
    handleBlur(e) {
      this.setData({
        isFocused: false
      });

      this.triggerEvent('blur', {
        value: e.detail.value,
        cursor: e.detail.cursor
      });
    },

    /**
     * 确认事件处理
     */
    handleConfirm(e) {
      this.triggerEvent('confirm', {
        value: e.detail.value
      });
    },

    /**
     * 键盘高度变化事件处理
     */
    handleKeyboardHeightChange(e) {
      this.triggerEvent('keyboardheightchange', {
        height: e.detail.height,
        duration: e.detail.duration
      });
    },

    /**
     * 行数变化事件处理
     */
    handleLineChange(e) {
      if (this.data.autoHeight) {
        const lineHeight = 24; // 行高
        const padding = 24; // 内边距
        const newHeight = Math.max(
          this.data.minHeight,
          Math.min(
            this.data.maxHeight,
            e.detail.lineCount * lineHeight + padding
          )
        );

        this.setData({
          currentHeight: newHeight
        });
      }

      this.triggerEvent('linechange', {
        height: e.detail.height,
        heightRpx: e.detail.heightRpx,
        lineCount: e.detail.lineCount
      });
    },

    /**
     * 清空内容
     */
    clear() {
      this.setData({
        currentValue: ''
      });

      this.triggerEvent('input', {
        value: '',
        cursor: 0
      });

      this.triggerEvent('change', {
        value: ''
      });
    },

    /**
     * 设置焦点
     */
    setFocus() {
      this.setData({
        focus: true
      });
    },

    /**
     * 移除焦点
     */
    setBlur() {
      this.setData({
        focus: false
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化当前值
      this.setData({
        currentValue: this.data.value,
        currentHeight: this.data.autoHeight ? this.data.minHeight : this.data.minHeight
      });
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'value': function (newVal) {
      if (newVal !== this.data.currentValue) {
        this.setData({
          currentValue: newVal
        });
      }
    }
  }
});