/* components/form/textarea/index.wxss */

.nl-textarea {
  width: 100%;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 标签样式 */
.textarea-label {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.required-mark {
  color: #ff4d4f;
  font-size: 28rpx;
  margin-left: 4rpx;
}

/* 输入框容器 */
.textarea-container {
  position: relative;
  background: #ffffff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}

.textarea-container:hover {
  border-color: #3B82F6;
}

.nl-textarea.focused .textarea-container {
  border-color: #3B82F6;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}

.nl-textarea.error .textarea-container {
  border-color: #ff4d4f;
}

.nl-textarea.error.focused .textarea-container {
  box-shadow: 0 0 0 4rpx rgba(255, 77, 79, 0.1);
}

.nl-textarea.disabled .textarea-container {
  background: #f5f5f5;
  border-color: #d9d9d9;
  cursor: not-allowed;
}

.nl-textarea.readonly .textarea-container {
  background: #fafafa;
  border-color: #d9d9d9;
}

/* 输入框样式 */
.textarea-input {
  width: 100%;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;
}

.textarea-input::placeholder {
  color: #999999;
  font-size: 28rpx;
}

.nl-textarea.disabled .textarea-input {
  color: #999999;
  cursor: not-allowed;
}

.nl-textarea.readonly .textarea-input {
  color: #666666;
  cursor: default;
}

/* 清空按钮 */
.clear-button {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 32rpx;
  height: 32rpx;
  background: #cccccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.clear-button:hover {
  background: #999999;
}

.clear-button:active {
  transform: scale(0.9);
}

.clear-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}

/* 底部信息 */
.textarea-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8rpx;
  min-height: 32rpx;
}

/* 字数统计 */
.char-count {
  font-size: 24rpx;
  color: #999999;
  margin-left: auto;
}

.current-count {
  color: #666666;
}

.max-count {
  color: #999999;
}

.nl-textarea.error .current-count {
  color: #ff4d4f;
}

/* 错误信息 */
.error-message {
  flex: 1;
}

.error-text {
  font-size: 24rpx;
  color: #ff4d4f;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .textarea-input {
    font-size: 26rpx;
    padding: 16rpx 20rpx;
  }
  
  .label-text {
    font-size: 26rpx;
  }
  
  .char-count,
  .error-text {
    font-size: 22rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .label-text {
    color: #ffffff;
  }
  
  .textarea-container {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .textarea-container:hover {
    border-color: #3B82F6;
  }
  
  .textarea-input {
    color: #ffffff;
  }
  
  .textarea-input::placeholder {
    color: #888888;
  }
  
  .nl-textarea.disabled .textarea-container {
    background: #1a1a1a;
    border-color: #333333;
  }
  
  .nl-textarea.disabled .textarea-input {
    color: #666666;
  }
  
  .nl-textarea.readonly .textarea-container {
    background: #1f1f1f;
    border-color: #333333;
  }
  
  .nl-textarea.readonly .textarea-input {
    color: #cccccc;
  }
  
  .char-count {
    color: #888888;
  }
  
  .current-count {
    color: #cccccc;
  }
}