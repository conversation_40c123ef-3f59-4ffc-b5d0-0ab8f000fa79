<!-- 虚拟列表组件 -->
<view class="virtual-list">
  <scroll-view
    class="virtual-list-container"
    scroll-y
    bindscroll="handleScroll"
    style="height: {{containerHeight}}px;"
    scroll-top="{{scrollTop}}"
    scroll-with-animation="{{false}}"
    enhanced="{{true}}"
    show-scrollbar="{{showScrollbar}}"
    bounces="{{bounces}}"
  >
    <!-- 占位元素，用于撑开滚动区域 -->
    <view 
      class="virtual-list-phantom"
      style="height: {{totalHeight}}px;"
    ></view>
    
    <!-- 实际渲染的内容 -->
    <view
      class="virtual-list-content"
      style="transform: translateY({{startOffset}}px);"
    >
      <block wx:for="{{visibleItems}}" wx:key="{{itemKey}}" wx:for-item="listItem" wx:for-index="listIndex">
        <view
          class="virtual-list-item"
          style="height: {{itemHeightFunc ? 'auto' : itemHeight}}px;"
          data-index="{{startIndex + listIndex}}"
          data-item="{{listItem}}"
          bindtap="handleItemTap"
        >
          <slot name="item"></slot>
        </view>
      </block>
    </view>
    
    <!-- 加载更多提示 -->
    <view class="virtual-list-loading" wx:if="{{loading}}">
      <view class="loading-indicator"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 无更多数据提示 -->
    <view class="virtual-list-no-more" wx:if="{{noMore && !loading}}">
      <text class="no-more-text">{{noMoreText}}</text>
    </view>
  </scroll-view>
</view>
