/**
 * 优化图片组件
 * 支持图片懒加载、WebP格式、CDN尺寸调整等优化功能
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 图片源URL
     */
    src: {
      type: String,
      value: '',
      observer: function (newVal) {
        if (newVal) {
          this._updateOptimizedSrc();
        }
      }
    },

    /**
     * 图片宽度（rpx）
     */
    width: {
      type: Number,
      value: 200
    },

    /**
     * 图片高度（rpx）
     */
    height: {
      type: Number,
      value: 200
    },

    /**
     * 图片裁剪、缩放模式
     */
    mode: {
      type: String,
      value: 'aspectFill'
    },

    /**
     * 是否开启懒加载
     */
    lazyLoad: {
      type: Boolean,
      value: true
    },

    /**
     * 是否使用WebP格式（如果支持）
     */
    webp: {
      type: Boolean,
      value: true
    },

    /**
     * 是否开启CDN尺寸调整
     */
    useCdn: {
      type: Boolean,
      value: true
    },

    /**
     * CDN域名列表（用于识别可优化的图片）
     */
    cdnDomains: {
      type: Array,
      value: ['cdn.example.com', 'img.example.com']
    },

    /**
     * 是否显示加载中占位
     */
    showPlaceholder: {
      type: Boolean,
      value: true
    },

    /**
     * 占位背景颜色
     */
    placeholderColor: {
      type: String,
      value: '#f5f5f5'
    },

    /**
     * 是否显示加载失败占位
     */
    showError: {
      type: Boolean,
      value: true
    },

    /**
     * 是否显示加载失败文本
     */
    showErrorText: {
      type: Boolean,
      value: true
    },

    /**
     * 是否使用圆形样式
     */
    round: {
      type: Boolean,
      value: false
    },

    /**
     * 是否使用淡入动画
     */
    fadeIn: {
      type: Boolean,
      value: true
    },

    /**
     * 是否显示长按菜单
     */
    showMenuByLongpress: {
      type: Boolean,
      value: false
    },

    /**
     * 自定义样式
     */
    customStyle: {
      type: String,
      value: ''
    },

    /**
     * 自定义类名
     */
    className: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    loading: true,
    loaded: false,
    loadError: false,
    optimizedSrc: '',
    devicePixelRatio: 2,
    webpSupported: false
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function () {
      // 获取设备像素比
      this._getDeviceInfo();

      // 检查WebP支持
      this._checkWebpSupport();

      // 更新优化后的图片URL
      this._updateOptimizedSrc();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 获取设备信息
     */
    _getDeviceInfo: function () {
      try {
        const windowInfo = wx.getWindowInfo();
        this.setData({
          devicePixelRatio: windowInfo.pixelRatio || 2
        });
      } catch (err) {
        console.error('获取设备信息失败:', err);
      }
    },

    /**
     * 检查WebP支持
     */
    _checkWebpSupport: function () {
      try {
        this.setData({
          webpSupported: wx.canIUse('image.webp')
        });
      } catch (err) {
        console.error('检查WebP支持失败:', err);
      }
    },

    /**
     * 更新优化后的图片URL
     */
    _updateOptimizedSrc: function () {
      const { src, useCdn, width, height, webp } = this.properties;
      const { devicePixelRatio, webpSupported } = this.data;

      if (!src) {
        this.setData({
          optimizedSrc: '',
          loading: false,
          loaded: false,
          loadError: false
        });
        return;
      }

      // 重置加载状态
      this.setData({
        loading: true,
        loaded: false,
        loadError: false
      });

      // 如果不使用CDN优化，直接使用原始URL
      if (!useCdn) {
        this.setData({
          optimizedSrc: src
        });
        return;
      }

      // 检查是否是可优化的CDN图片
      const isCdnImage = this._isCdnImage(src);

      if (!isCdnImage) {
        this.setData({
          optimizedSrc: src
        });
        return;
      }

      // 计算实际需要的尺寸（考虑设备像素比）
      const realWidth = Math.round(width * devicePixelRatio / 2);
      const realHeight = Math.round(height * devicePixelRatio / 2);

      // 构建优化后的URL
      let optimizedSrc = src;

      // 添加尺寸参数
      if (optimizedSrc.includes('?')) {
        optimizedSrc += '&';
      } else {
        optimizedSrc += '?';
      }

      optimizedSrc += `w=${realWidth}&h=${realHeight}`;

      // 添加WebP格式参数（如果支持）
      if (webp && webpSupported) {
        optimizedSrc += '&format=webp';
      }

      this.setData({
        optimizedSrc
      });
    },

    /**
     * 检查是否是可优化的CDN图片
     * @param {string} url - 图片URL
     * @returns {boolean} 是否是可优化的CDN图片
     */
    _isCdnImage: function (url) {
      if (!url) return false;

      const { cdnDomains } = this.properties;

      // 检查URL是否包含任一CDN域名
      return cdnDomains.some(domain => url.includes(domain));
    },

    /**
     * 处理图片加载完成
     */
    handleLoad: function () {
      this.setData({
        loading: false,
        loaded: true,
        loadError: false
      });

      this.triggerEvent('load');
    },

    /**
     * 处理图片加载失败
     */
    handleError: function () {
      this.setData({
        loading: false,
        loaded: false,
        loadError: true
      });

      this.triggerEvent('error');
    },

    /**
     * 处理图片点击
     */
    handleTap: function () {
      this.triggerEvent('tap');
    }
  }
});
