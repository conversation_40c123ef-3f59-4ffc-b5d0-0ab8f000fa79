// components/bubble-canvas/adaptive-performance.js
// 自适应性能管理器

/**
 * 自适应性能管理器
 * 用于根据设备性能自动调整动画复杂度
 */
class AdaptivePerformance {
  /**
   * 构造函数
   * @param {Object} options - 初始化选项
   * @param {Object} options.performanceMonitor - 性能监控对象
   * @param {Function} options.onModeChange - 模式变更回调函数
   * @param {number} options.targetFps - 目标帧率
   * @param {number} options.minFps - 最低可接受帧率
   * @param {number} options.adaptationInterval - 适应间隔（毫秒）
   * @param {number} options.stabilityThreshold - 稳定阈值（帧数）
   * @param {number} options.fpsHistorySize - 帧率历史大小
   * @param {boolean} options.isEnabled - 是否启用自适应性能
   * @param {boolean} options.debugMode - 是否启用调试模式
   * @param {string} options.initialMode - 初始性能模式
   */
  constructor(options = {}) {
    this.performanceMonitor = options.performanceMonitor;
    this.onModeChange = options.onModeChange || (() => {});
    this.currentMode = options.initialMode || 'auto'; // 当前性能模式：'high', 'medium', 'low', 'auto'
    this.targetFps = options.targetFps || 60; // 目标帧率
    this.minFps = options.minFps || 30; // 最低可接受帧率
    this.adaptationInterval = options.adaptationInterval || 2000; // 适应间隔（毫秒）
    this.lastAdaptationTime = 0; // 上次适应时间
    this.stabilityThreshold = options.stabilityThreshold || 5; // 稳定阈值（帧数）
    this.fpsHistory = []; // 帧率历史
    this.fpsHistorySize = options.fpsHistorySize || 10; // 帧率历史大小
    this.devicePerformance = null; // 设备性能评级
    this.isEnabled = options.isEnabled !== false; // 是否启用自适应性能
    this.debugMode = options.debugMode || false; // 是否启用调试模式

    // 新增属性
    this.cpuUsageHistory = []; // CPU使用率历史
    this.memoryUsageHistory = []; // 内存使用率历史
    this.frameTimeHistory = []; // 帧时间历史
    this.adaptationStrategy = options.adaptationStrategy || 'balanced'; // 适应策略：'aggressive', 'balanced', 'conservative'
    this.lastModeChangeTime = 0; // 上次模式变更时间
    this.modeChangeCooldown = options.modeChangeCooldown || 5000; // 模式变更冷却时间（毫秒）
    this.performanceScore = 100; // 性能得分（0-100）
    this.performanceScoreHistory = []; // 性能得分历史
    this.performanceScoreHistorySize = 5; // 性能得分历史大小
    this.deviceInfo = null; // 设备信息
  }

  /**
   * 初始化自适应性能管理器
   * @returns {Promise} 初始化完成的Promise
   */
  init() {
    return new Promise(resolve => {
      // 检测设备性能
      this._detectDevicePerformance()
        .then(() => {
          // 根据设备性能设置初始模式
          this._setInitialMode();

          // 初始化性能得分
          this._initPerformanceScore();

          // 预热性能监控
          if (this.performanceMonitor) {
            this.performanceMonitor.warmup();
          }

          console.log(`自适应性能管理器初始化完成，设备性能: ${this.devicePerformance}, 初始模式: ${this.currentMode}, 性能得分: ${this.performanceScore}`);

          resolve({
            devicePerformance: this.devicePerformance,
            currentMode: this.currentMode,
            performanceScore: this.performanceScore,
            deviceInfo: this.deviceInfo
          });
        })
        .catch(err => {
          console.error('自适应性能管理器初始化失败', err);
          // 设置默认值
          this.devicePerformance = 'medium';
          this._setInitialMode();
          resolve({
            devicePerformance: this.devicePerformance,
            currentMode: this.currentMode,
            error: err.message
          });
        });
    });
  }

  /**
   * 检测设备性能
   * @private
   * @returns {Promise} 检测完成的Promise
   */
  _detectDevicePerformance() {
    return new Promise((resolve, reject) => {
      try {
        // 获取系统信息
        const windowInfo = wx.getWindowInfo();
        const deviceInfo = wx.getDeviceInfo();
        const appBaseInfo = wx.getAppBaseInfo();

        // 保存设备信息
        this.deviceInfo = {
          platform: deviceInfo.platform,
          model: deviceInfo.model,
          system: deviceInfo.system,
          brand: deviceInfo.brand,
          benchmarkLevel: appBaseInfo.benchmarkLevel,
          windowWidth: windowInfo.windowWidth,
          windowHeight: windowInfo.windowHeight,
          pixelRatio: windowInfo.pixelRatio
        };

        // 获取设备型号和平台
        const { platform, model, brand, system } = this.deviceInfo;
        const benchmarkLevel = this.deviceInfo.benchmarkLevel;

        // 如果有基准级别，直接使用
        if (benchmarkLevel) {
          if (benchmarkLevel >= 50) {
            this.devicePerformance = 'high';
            this.performanceScore = 90 + Math.min(10, (benchmarkLevel - 50) / 5);
          } else if (benchmarkLevel >= 30) {
            this.devicePerformance = 'medium';
            this.performanceScore = 70 + Math.min(20, (benchmarkLevel - 30) / 1);
          } else {
            this.devicePerformance = 'low';
            this.performanceScore = Math.max(30, benchmarkLevel);
          }
          resolve();
          return;
        }

        // 根据平台和型号评估性能
        if (platform === 'ios') {
          // iOS设备性能评估
          const iosVersionMatch = system.match(/iOS (\d+)/);
          const iosVersion = iosVersionMatch ? parseInt(iosVersionMatch[1]) : 0;

          // 根据iOS版本和设备型号评估性能
          if (iosVersion >= 15) {
            if (model.includes('iPhone 13') || model.includes('iPhone 14') ||
                model.includes('iPhone 15') || model.includes('iPad Pro')) {
              this.devicePerformance = 'high';
              this.performanceScore = 95;
            } else if (model.includes('iPhone 11') || model.includes('iPhone 12') ||
                      model.includes('iPad Air')) {
              this.devicePerformance = 'high';
              this.performanceScore = 85;
            } else if (model.includes('iPhone X') || model.includes('iPhone 8') ||
                      model.includes('iPhone 9') || model.includes('iPhone 10')) {
              this.devicePerformance = 'medium';
              this.performanceScore = 75;
            } else {
              this.devicePerformance = 'medium';
              this.performanceScore = 65;
            }
          } else if (iosVersion >= 13) {
            if (model.includes('iPhone 11') || model.includes('iPhone 12') ||
                model.includes('iPad Pro')) {
              this.devicePerformance = 'high';
              this.performanceScore = 80;
            } else if (model.includes('iPhone X') || model.includes('iPhone 8') ||
                      model.includes('iPad Air')) {
              this.devicePerformance = 'medium';
              this.performanceScore = 70;
            } else {
              this.devicePerformance = 'low';
              this.performanceScore = 50;
            }
          } else {
            this.devicePerformance = 'low';
            this.performanceScore = 40;
          }
        } else if (platform === 'android') {
          // Android设备性能评估
          // 使用更复杂的评估逻辑，考虑品牌、型号和系统版本
          const androidVersionMatch = system.match(/Android (\d+)/);
          const androidVersion = androidVersionMatch ? parseInt(androidVersionMatch[1]) : 0;

          // 高端设备列表
          const highEndBrands = ['Google', 'OnePlus', 'Samsung', 'Huawei', 'Xiaomi', 'OPPO', 'vivo'];
          const highEndModels = ['Pixel', 'OnePlus', 'SM-G9', 'SM-N9', 'P40', 'P50', 'Mate', 'Mi 10', 'Mi 11', 'Find X', 'NEX'];

          // 中端设备列表
          const midEndModels = ['SM-A', 'Redmi', 'Nova', 'Honor', 'Reno', 'iQOO'];

          // 检查是否为高端设备
          const isHighEndBrand = highEndBrands.some(b => brand && brand.includes(b));
          const isHighEndModel = highEndModels.some(m => model && model.includes(m));
          const isMidEndModel = midEndModels.some(m => model && model.includes(m));

          if (androidVersion >= 10) {
            if (isHighEndBrand && isHighEndModel) {
              this.devicePerformance = 'high';
              this.performanceScore = 85;
            } else if (isHighEndBrand || isHighEndModel) {
              this.devicePerformance = 'medium';
              this.performanceScore = 75;
            } else if (isMidEndModel) {
              this.devicePerformance = 'medium';
              this.performanceScore = 65;
            } else {
              this.devicePerformance = 'low';
              this.performanceScore = 55;
            }
          } else if (androidVersion >= 8) {
            if (isHighEndBrand && isHighEndModel) {
              this.devicePerformance = 'medium';
              this.performanceScore = 70;
            } else if (isMidEndModel) {
              this.devicePerformance = 'medium';
              this.performanceScore = 60;
            } else {
              this.devicePerformance = 'low';
              this.performanceScore = 45;
            }
          } else {
            this.devicePerformance = 'low';
            this.performanceScore = 35;
          }
        } else {
          // 默认为中等性能
          this.devicePerformance = 'medium';
          this.performanceScore = 60;
        }

        resolve();
      } catch (err) {
        console.error('检测设备性能失败', err);
        // 默认为中等性能
        this.devicePerformance = 'medium';
        this.performanceScore = 60;
        reject(err);
      }
    });
  }

  /**
   * 初始化性能得分
   * @private
   */
  _initPerformanceScore() {
    // 初始化性能得分历史
    for (let i = 0; i < this.performanceScoreHistorySize; i++) {
      this.performanceScoreHistory.push(this.performanceScore);
    }
  }

  /**
   * 设置初始模式
   * @private
   */
  _setInitialMode() {
    if (this.currentMode !== 'auto') return;

    // 根据设备性能设置初始模式
    switch (this.devicePerformance) {
      case 'high':
        this.setMode('high');
        break;
      case 'medium':
        this.setMode('medium');
        break;
      case 'low':
        this.setMode('low');
        break;
      default:
        this.setMode('medium');
        break;
    }
  }

  /**
   * 设置性能模式
   * @param {string} mode - 性能模式：'high', 'medium', 'low', 'auto'
   * @param {boolean} force - 是否强制设置，忽略冷却时间
   */
  setMode(mode, force = false) {
    // 如果模式相同，不做任何操作
    if (this.currentMode === mode) return;

    const now = Date.now();

    // 如果不是强制设置且距离上次模式变更时间不足冷却时间，跳过
    if (!force && now - this.lastModeChangeTime < this.modeChangeCooldown) {
      if (this.debugMode) {
        console.log(`性能模式变更被冷却时间限制，当前模式: ${this.currentMode}, 请求模式: ${mode}, 剩余冷却时间: ${(this.modeChangeCooldown - (now - this.lastModeChangeTime)) / 1000}秒`);
      }
      return;
    }

    // 更新当前模式
    this.currentMode = mode;

    // 更新上次模式变更时间
    this.lastModeChangeTime = now;

    // 如果模式为auto，根据设备性能设置初始模式
    if (mode === 'auto') {
      this._setInitialMode();
      return;
    }

    // 调用模式变更回调函数
    this.onModeChange(mode);

    // 重置性能历史
    this.fpsHistory = [];
    this.frameTimeHistory = [];
    this.cpuUsageHistory = [];
    this.memoryUsageHistory = [];

    // 输出调试信息
    if (this.debugMode) {
      console.log(`性能模式已切换为: ${mode}`, {
        devicePerformance: this.devicePerformance,
        performanceScore: this.performanceScore,
        config: this.getModeConfig(mode)
      });
    }
  }

  /**
   * 更新帧率历史
   * @param {number} fps - 当前帧率
   */
  updateFpsHistory(fps) {
    // 添加当前帧率到历史
    this.fpsHistory.push(fps);

    // 如果历史超过最大大小，移除最旧的帧率
    if (this.fpsHistory.length > this.fpsHistorySize) {
      this.fpsHistory.shift();
    }
  }

  /**
   * 获取平均帧率
   * @returns {number} 平均帧率
   */
  getAverageFps() {
    if (this.fpsHistory.length === 0) return 0;

    // 计算平均帧率
    const sum = this.fpsHistory.reduce((a, b) => a + b, 0);
    return sum / this.fpsHistory.length;
  }

  /**
   * 适应性能
   */
  adapt() {
    if (!this.isEnabled || this.currentMode === 'auto') return;

    // 如果没有性能监控对象，不做任何操作
    if (!this.performanceMonitor) return;

    const now = Date.now();

    // 如果距离上次适应时间不足适应间隔，跳过
    if (now - this.lastAdaptationTime < this.adaptationInterval) return;

    // 如果距离上次模式变更时间不足冷却时间，跳过
    if (now - this.lastModeChangeTime < this.modeChangeCooldown) return;

    // 获取当前性能指标
    const metrics = this.performanceMonitor.getMetrics();
    const currentFps = metrics.fps.current;
    const currentFrameTime = metrics.frameTime.current;
    const cpuUsage = metrics.cpu ? metrics.cpu.current : 0;
    const memoryUsage = metrics.memory ? metrics.memory.current : 0;

    // 更新性能历史
    this.updateFpsHistory(currentFps);
    this.updateFrameTimeHistory(currentFrameTime);
    if (cpuUsage > 0) this.updateCpuUsageHistory(cpuUsage);
    if (memoryUsage > 0) this.updateMemoryUsageHistory(memoryUsage);

    // 计算性能得分
    const performanceScore = this._calculatePerformanceScore(metrics);
    this.updatePerformanceScoreHistory(performanceScore);

    // 获取平均性能得分
    const averagePerformanceScore = this.getAveragePerformanceScore();

    // 如果性能得分历史不足稳定阈值，跳过
    if (this.performanceScoreHistory.length < this.stabilityThreshold) return;

    // 根据适应策略获取阈值
    const thresholds = this._getAdaptationThresholds();

    // 根据平均性能得分调整性能模式
    if (averagePerformanceScore < thresholds.lowPerformanceThreshold) {
      // 如果平均性能得分低于低性能阈值，降低性能模式
      if (this.currentMode === 'high') {
        this.setMode('medium');
      } else if (this.currentMode === 'medium') {
        this.setMode('low');
      }
    } else if (averagePerformanceScore > thresholds.highPerformanceThreshold) {
      // 如果平均性能得分高于高性能阈值，提高性能模式
      if (this.currentMode === 'low') {
        this.setMode('medium');
      } else if (this.currentMode === 'medium') {
        this.setMode('high');
      }
    }

    // 更新上次适应时间
    this.lastAdaptationTime = now;

    // 输出调试信息
    if (this.debugMode) {
      console.log('性能适应:', {
        currentMode: this.currentMode,
        performanceScore: performanceScore.toFixed(2),
        averagePerformanceScore: averagePerformanceScore.toFixed(2),
        fps: currentFps.toFixed(2),
        frameTime: currentFrameTime.toFixed(2),
        cpuUsage: cpuUsage.toFixed(2),
        memoryUsage: memoryUsage.toFixed(2),
        thresholds
      });
    }
  }

  /**
   * 计算性能得分
   * @param {Object} metrics - 性能指标
   * @returns {number} 性能得分（0-100）
   * @private
   */
  _calculatePerformanceScore(metrics) {
    // 权重
    const weights = {
      fps: 0.5,
      frameTime: 0.3,
      cpu: 0.1,
      memory: 0.1
    };

    // 计算帧率得分（0-100）
    const fpsScore = Math.min(100, (metrics.fps.current / this.targetFps) * 100);

    // 计算帧时间得分（0-100）
    // 目标帧时间为 1000 / targetFps 毫秒
    const targetFrameTime = 1000 / this.targetFps;
    const frameTimeScore = Math.max(0, 100 - (metrics.frameTime.current - targetFrameTime) * 5);

    // 计算CPU使用率得分（0-100）
    // CPU使用率越低越好
    const cpuScore = metrics.cpu ? Math.max(0, 100 - metrics.cpu.current) : 100;

    // 计算内存使用率得分（0-100）
    // 内存使用率越低越好
    const memoryScore = metrics.memory ? Math.max(0, 100 - metrics.memory.current) : 100;

    // 计算加权得分
    const weightedScore =
      fpsScore * weights.fps +
      frameTimeScore * weights.frameTime +
      cpuScore * weights.cpu +
      memoryScore * weights.memory;

    // 应用设备性能因子
    // 低性能设备的得分会被适当降低，高性能设备的得分会被适当提高
    let deviceFactor = 1.0;
    switch (this.devicePerformance) {
      case 'high':
        deviceFactor = 1.1;
        break;
      case 'medium':
        deviceFactor = 1.0;
        break;
      case 'low':
        deviceFactor = 0.9;
        break;
    }

    // 计算最终得分
    const finalScore = Math.min(100, weightedScore * deviceFactor);

    return finalScore;
  }

  /**
   * 获取适应阈值
   * @returns {Object} 适应阈值
   * @private
   */
  _getAdaptationThresholds() {
    // 根据适应策略设置阈值
    switch (this.adaptationStrategy) {
      case 'aggressive':
        // 激进策略：更快地调整性能模式
        return {
          lowPerformanceThreshold: 60,
          highPerformanceThreshold: 80
        };
      case 'conservative':
        // 保守策略：更慢地调整性能模式
        return {
          lowPerformanceThreshold: 40,
          highPerformanceThreshold: 90
        };
      case 'balanced':
      default:
        // 平衡策略：适中地调整性能模式
        return {
          lowPerformanceThreshold: 50,
          highPerformanceThreshold: 85
        };
    }
  }

  /**
   * 更新CPU使用率历史
   * @param {number} cpuUsage - CPU使用率
   */
  updateCpuUsageHistory(cpuUsage) {
    this.cpuUsageHistory.push(cpuUsage);
    if (this.cpuUsageHistory.length > this.fpsHistorySize) {
      this.cpuUsageHistory.shift();
    }
  }

  /**
   * 更新内存使用率历史
   * @param {number} memoryUsage - 内存使用率
   */
  updateMemoryUsageHistory(memoryUsage) {
    this.memoryUsageHistory.push(memoryUsage);
    if (this.memoryUsageHistory.length > this.fpsHistorySize) {
      this.memoryUsageHistory.shift();
    }
  }

  /**
   * 更新帧时间历史
   * @param {number} frameTime - 帧时间
   */
  updateFrameTimeHistory(frameTime) {
    this.frameTimeHistory.push(frameTime);
    if (this.frameTimeHistory.length > this.fpsHistorySize) {
      this.frameTimeHistory.shift();
    }
  }

  /**
   * 更新性能得分历史
   * @param {number} score - 性能得分
   */
  updatePerformanceScoreHistory(score) {
    this.performanceScoreHistory.push(score);
    if (this.performanceScoreHistory.length > this.performanceScoreHistorySize) {
      this.performanceScoreHistory.shift();
    }
    this.performanceScore = score;
  }

  /**
   * 获取平均性能得分
   * @returns {number} 平均性能得分
   */
  getAveragePerformanceScore() {
    if (this.performanceScoreHistory.length === 0) return 0;
    const sum = this.performanceScoreHistory.reduce((a, b) => a + b, 0);
    return sum / this.performanceScoreHistory.length;
  }

  /**
   * 获取性能模式配置
   * @param {string} mode - 性能模式：'high', 'medium', 'low'
   * @returns {Object} 性能模式配置
   */
  getModeConfig(mode) {
    // 基础配置
    const baseConfig = {
      // 动画相关
      animationSpeedMultiplier: 0.064,
      baseSpeed: 0.64,

      // 渲染相关
      shadowBlur: 2,
      enableGlow: false,
      useOffscreenCanvas: true,

      // 物理模拟相关
      applyDrag: true,
      applyGravity: true,
      checkStuck: true,
      applyRandomness: true,
      checkSpeed: true,

      // 碰撞检测相关
      applyFullPhysics: true,
      simplifiedSeparation: false,

      // 粒子系统相关
      maxParticles: 10,

      // 空间分区相关
      cellSize: 100,

      // 渲染优化相关
      skipFrames: 0,
      useVisibleElementsOnly: false,

      // 缓存相关
      maxCacheSize: 50,
      adaptiveCaching: true,

      // 对象池相关
      initialPoolSize: 50,
      maxPoolSize: 200
    };

    // 根据设备性能调整配置
    const deviceFactor = {
      high: {
        animationSpeedMultiplier: 1.0,
        baseSpeed: 1.0,
        shadowBlur: 1.0,
        maxParticles: 1.0,
        maxCacheSize: 1.0,
        initialPoolSize: 1.0,
        maxPoolSize: 1.0,
        cellSize: 1.0
      },
      medium: {
        animationSpeedMultiplier: 1.0,
        baseSpeed: 1.0,
        shadowBlur: 0.5,
        maxParticles: 0.8,
        maxCacheSize: 0.8,
        initialPoolSize: 0.8,
        maxPoolSize: 0.8,
        cellSize: 1.0
      },
      low: {
        animationSpeedMultiplier: 0.75,
        baseSpeed: 0.75,
        shadowBlur: 0.0,
        maxParticles: 0.5,
        maxCacheSize: 0.6,
        initialPoolSize: 0.6,
        maxPoolSize: 0.6,
        cellSize: 1.5
      }
    }[this.devicePerformance || 'medium'];

    // 根据性能模式调整配置
    switch (mode) {
      case 'high':
        return {
          ...baseConfig,
          animationSpeedMultiplier: 0.064 * deviceFactor.animationSpeedMultiplier,
          baseSpeed: 0.64 * deviceFactor.baseSpeed,
          shadowBlur: 4 * deviceFactor.shadowBlur,
          enableGlow: true,
          maxParticles: 20 * deviceFactor.maxParticles,
          useOffscreenCanvas: true,
          applyDrag: true,
          applyGravity: true,
          checkStuck: true,
          applyRandomness: true,
          checkSpeed: true,
          applyFullPhysics: true,
          simplifiedSeparation: false,
          cellSize: 100 * deviceFactor.cellSize,
          skipFrames: 0,
          useVisibleElementsOnly: false,
          maxCacheSize: 80 * deviceFactor.maxCacheSize,
          adaptiveCaching: true,
          initialPoolSize: 50 * deviceFactor.initialPoolSize,
          maxPoolSize: 200 * deviceFactor.maxPoolSize
        };
      case 'medium':
        return {
          ...baseConfig,
          animationSpeedMultiplier: 0.064 * deviceFactor.animationSpeedMultiplier,
          baseSpeed: 0.64 * deviceFactor.baseSpeed,
          shadowBlur: 2 * deviceFactor.shadowBlur,
          enableGlow: false,
          maxParticles: 10 * deviceFactor.maxParticles,
          useOffscreenCanvas: true,
          applyDrag: true,
          applyGravity: true,
          checkStuck: this._frameCounter % 2 === 0, // 每2帧检查一次
          applyRandomness: true,
          checkSpeed: this._frameCounter % 2 === 0, // 每2帧检查一次
          applyFullPhysics: true,
          simplifiedSeparation: false,
          cellSize: 100 * deviceFactor.cellSize,
          skipFrames: 0,
          useVisibleElementsOnly: false,
          maxCacheSize: 50 * deviceFactor.maxCacheSize,
          adaptiveCaching: true,
          initialPoolSize: 40 * deviceFactor.initialPoolSize,
          maxPoolSize: 150 * deviceFactor.maxPoolSize
        };
      case 'low':
        return {
          ...baseConfig,
          animationSpeedMultiplier: 0.048 * deviceFactor.animationSpeedMultiplier,
          baseSpeed: 0.48 * deviceFactor.baseSpeed,
          shadowBlur: 0,
          enableGlow: false,
          maxParticles: 5 * deviceFactor.maxParticles,
          useOffscreenCanvas: this.devicePerformance !== 'low', // 低性能设备不使用离屏Canvas
          applyDrag: true,
          applyGravity: false,
          checkStuck: this._frameCounter % 3 === 0, // 每3帧检查一次
          applyRandomness: this._frameCounter % 3 === 0, // 每3帧检查一次
          checkSpeed: this._frameCounter % 3 === 0, // 每3帧检查一次
          applyFullPhysics: false,
          simplifiedSeparation: true,
          cellSize: 150 * deviceFactor.cellSize,
          skipFrames: 1,
          useVisibleElementsOnly: true,
          maxCacheSize: 30 * deviceFactor.maxCacheSize,
          adaptiveCaching: true,
          initialPoolSize: 30 * deviceFactor.initialPoolSize,
          maxPoolSize: 100 * deviceFactor.maxPoolSize
        };
      default:
        return this.getModeConfig('medium');
    }
  }

  /**
   * 获取当前性能模式配置
   * @returns {Object} 当前性能模式配置
   */
  getCurrentModeConfig() {
    return this.getModeConfig(this.currentMode);
  }

  /**
   * 获取自适应性能统计信息
   * @param {boolean} detailed - 是否返回详细信息
   * @returns {Object} 统计信息
   */
  getStats(detailed = false) {
    // 基础统计信息
    const stats = {
      mode: this.currentMode,
      devicePerformance: this.devicePerformance,
      performanceScore: this.performanceScore.toFixed(2),
      averagePerformanceScore: this.getAveragePerformanceScore().toFixed(2),
      averageFps: this.getAverageFps().toFixed(2),
      isEnabled: this.isEnabled,
      adaptationStrategy: this.adaptationStrategy,
      lastModeChangeTime: this.lastModeChangeTime,
      lastAdaptationTime: this.lastAdaptationTime
    };

    // 如果需要详细信息，添加更多统计数据
    if (detailed) {
      stats.detailed = {
        fpsHistory: this.fpsHistory,
        frameTimeHistory: this.frameTimeHistory,
        cpuUsageHistory: this.cpuUsageHistory,
        memoryUsageHistory: this.memoryUsageHistory,
        performanceScoreHistory: this.performanceScoreHistory,
        deviceInfo: this.deviceInfo,
        config: this.getCurrentModeConfig(),
        thresholds: this._getAdaptationThresholds()
      };
    }

    return stats;
  }

  /**
   * 获取性能报告
   * @returns {Object} 性能报告
   */
  getPerformanceReport() {
    // 获取当前性能指标
    const metrics = this.performanceMonitor ? this.performanceMonitor.getMetrics() : {};

    // 计算性能得分
    const performanceScore = this.performanceScore;

    // 获取平均性能得分
    const averagePerformanceScore = this.getAveragePerformanceScore();

    // 获取当前模式配置
    const config = this.getCurrentModeConfig();

    // 生成性能报告
    return {
      timestamp: Date.now(),
      deviceInfo: this.deviceInfo,
      performanceScore,
      averagePerformanceScore,
      currentMode: this.currentMode,
      metrics: {
        fps: metrics.fps ? {
          current: metrics.fps.current,
          avg: metrics.fps.avg,
          min: metrics.fps.min,
          max: metrics.fps.max
        } : null,
        frameTime: metrics.frameTime ? {
          current: metrics.frameTime.current,
          avg: metrics.frameTime.avg,
          min: metrics.frameTime.min,
          max: metrics.frameTime.max
        } : null,
        cpu: metrics.cpu ? {
          current: metrics.cpu.current,
          avg: metrics.cpu.avg
        } : null,
        memory: metrics.memory ? {
          current: metrics.memory.current,
          avg: metrics.memory.avg
        } : null
      },
      config: {
        animationSpeedMultiplier: config.animationSpeedMultiplier,
        baseSpeed: config.baseSpeed,
        shadowBlur: config.shadowBlur,
        enableGlow: config.enableGlow,
        maxParticles: config.maxParticles,
        useOffscreenCanvas: config.useOffscreenCanvas,
        skipFrames: config.skipFrames,
        useVisibleElementsOnly: config.useVisibleElementsOnly
      },
      recommendations: this._generateRecommendations(performanceScore, metrics)
    };
  }

  /**
   * 生成性能优化建议
   * @param {number} performanceScore - 性能得分
   * @param {Object} metrics - 性能指标
   * @returns {Array} 优化建议
   * @private
   */
  _generateRecommendations(performanceScore, metrics) {
    const recommendations = [];

    // 根据性能得分生成建议
    if (performanceScore < 50) {
      recommendations.push({
        priority: 'high',
        message: '性能较低，建议降低性能模式',
        action: '将性能模式设置为low'
      });

      if (this.currentMode !== 'low') {
        recommendations.push({
          priority: 'high',
          message: '当前性能模式过高，建议降低',
          action: '将性能模式从' + this.currentMode + '降低为low'
        });
      }
    } else if (performanceScore < 70) {
      if (this.currentMode === 'high') {
        recommendations.push({
          priority: 'medium',
          message: '当前性能模式可能过高，建议降低',
          action: '将性能模式从high降低为medium'
        });
      }
    }

    // 根据帧率生成建议
    if (metrics.fps && metrics.fps.avg < this.minFps) {
      recommendations.push({
        priority: 'high',
        message: '帧率过低，影响用户体验',
        action: '降低动画复杂度或减少元素数量'
      });
    }

    // 根据帧时间生成建议
    if (metrics.frameTime && metrics.frameTime.avg > 1000 / this.minFps) {
      recommendations.push({
        priority: 'medium',
        message: '帧时间过长，可能导致卡顿',
        action: '优化渲染和计算逻辑'
      });
    }

    // 根据CPU使用率生成建议
    if (metrics.cpu && metrics.cpu.avg > 80) {
      recommendations.push({
        priority: 'high',
        message: 'CPU使用率过高，可能导致设备发热和耗电',
        action: '减少计算量或降低更新频率'
      });
    }

    // 根据内存使用率生成建议
    if (metrics.memory && metrics.memory.avg > 80) {
      recommendations.push({
        priority: 'medium',
        message: '内存使用率过高，可能导致内存不足',
        action: '减少缓存大小或对象池大小'
      });
    }

    return recommendations;
  }

  /**
   * 启用自适应性能
   */
  enable() {
    this.isEnabled = true;
  }

  /**
   * 禁用自适应性能
   */
  disable() {
    this.isEnabled = false;
  }

  /**
   * 启用调试模式
   */
  enableDebug() {
    this.debugMode = true;
  }

  /**
   * 禁用调试模式
   */
  disableDebug() {
    this.debugMode = false;
  }

  /**
   * 销毁自适应性能管理器
   */
  destroy() {
    this.performanceMonitor = null;
    this.onModeChange = null;
    this.fpsHistory = [];
  }
}

module.exports = AdaptivePerformance;
