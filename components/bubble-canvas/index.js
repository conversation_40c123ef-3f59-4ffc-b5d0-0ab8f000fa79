// components/bubble-canvas/index.js
// 泡泡画布组件

// 微信小程序组件定义
Component({
  properties: {
    // 组件属性
    canvasId: {
      type: String,
      value: 'bubble-canvas'
    },
    width: {
      type: Number,
      value: 375
    },
    height: {
      type: Number,
      value: 600
    },
    themes: {
      type: Array,
      value: []
    }
  },

  data: {
    // 组件数据
    bubbles: [],
    canvasContext: null
  },

  methods: {
    // 初始化画布
    initCanvas() {
      const query = this.createSelectorQuery();
      query.select('#' + this.properties.canvasId)
        .fields({ node: true, size: true })
        .exec(res => {
          if (res[0]) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            const dpr = wx.getSystemInfoSync().pixelRatio;
            canvas.width = res[0].width * dpr;
            canvas.height = res[0].height * dpr;
            ctx.scale(dpr, dpr);

            this.setData({
              canvasContext: ctx
            });

            // 创建泡泡
            this.createBubbles();
          }
        });
    },

    // 创建泡泡
    createBubbles() {
      const themes = this.properties.themes || [];
      if (themes.length === 0) return;

      const bubbles = themes.map((theme, index) => ({
        id: `bubble-${index}`,
        text: theme.name,
        color: theme.color || '#3B82F6',
        x: Math.random() * this.properties.width,
        y: Math.random() * this.properties.height,
        radius: 30 + Math.random() * 20,
        velocityX: (Math.random() - 0.5) * 2,
        velocityY: (Math.random() - 0.5) * 2
      }));

      this.setData({ bubbles });
    },

    // 绘制泡泡
    drawBubbles() {
      const ctx = this.data.canvasContext;
      if (!ctx) return;

      // 清空画布
      ctx.clearRect(0, 0, this.properties.width, this.properties.height);

      // 绘制每个泡泡
      this.data.bubbles.forEach(bubble => {
        this.drawBubble(ctx, bubble);
      });
    },

    // 绘制单个泡泡
    drawBubble(ctx, bubble) {
      // 创建渐变
      const gradient = ctx.createRadialGradient(
        bubble.x - bubble.radius * 0.3,
        bubble.y - bubble.radius * 0.3,
        0,
        bubble.x,
        bubble.y,
        bubble.radius
      );

      gradient.addColorStop(0, this.lightenColor(bubble.color, 40));
      gradient.addColorStop(1, bubble.color);

      // 绘制泡泡
      ctx.beginPath();
      ctx.arc(bubble.x, bubble.y, bubble.radius, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.fill();

      // 绘制文字
      ctx.fillStyle = '#FFFFFF';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.font = `${bubble.radius * 0.3}px Arial`;
      ctx.fillText(bubble.text, bubble.x, bubble.y);
    },

    // 颜色变亮
    lightenColor(hex, percent) {
      const num = parseInt(hex.replace('#', ''), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) + amt;
      const G = (num >> 8 & 0x00FF) + amt;
      const B = (num & 0x0000FF) + amt;
      return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
  },

  lifetimes: {
    attached() {
      this.initCanvas();
    }
  }
});