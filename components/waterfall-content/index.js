// components/waterfall-content/index.js
// 瀑布流内容区组件

const dataLoadingBehavior = require('../../behaviors/data-loading-behavior');

Component({
  behaviors: [dataLoadingBehavior],

  properties: {
    // 当前选中的分类 (Data from Page)
    currentCategory: {
      type: String,
      value: 'all',
      observer: function (newVal, oldVal) {
        // When the category changes from the parent page,
        // reload the initial posts for the new category.
        if (newVal !== oldVal && this._isAttached) { // Check if component is attached
          console.log(`[waterfall-content] Observed currentCategory change: ${oldVal} -> ${newVal}`);
          // Call the behavior method to load initial posts
          this.loadInitialPosts(newVal, true);
        }
      }
    },
    // 页面大小 (Optional, can use behavior's default)
    pageSize: {
      type: Number,
      value: 8 // Or rely on behavior's default
    }
  },

  data: {
    // Component-specific data if any
    // posts, leftPosts, rightPosts, isLoading, etc., are managed by the behavior
  },

  lifetimes: {
    created() {
      console.log('[waterfall-content] Component created');
      this._componentId = Date.now(); // 生成唯一ID用于调试
    },
    attached() {
      this._isAttached = true;
      // Component initialization
      // Initial data load for the default category ('all')
      console.log('[waterfall-content] Attached. Component ID:', this._componentId, 'Loading initial posts for:', this.data.currentCategory);
      this.loadInitialPosts();

      // 确保按钮可见性
      this._checkButtonVisibility();
    },
    ready() {
      console.log('[waterfall-content] Component ready. Component ID:', this._componentId);
      // 组件完全准备好后，再次确保按钮可见性
      this._checkButtonVisibility();

      // 设置定时器，定期检查按钮可见性
      this._visibilityTimer = setInterval(() => {
        this._checkButtonVisibility();
      }, 2000); // 每2秒检查一次
    },
    detached() {
      this._isAttached = false;
      console.log('[waterfall-content] Component detached. Component ID:', this._componentId);

      // 清理定时器
      if (this._visibilityTimer) {
        clearInterval(this._visibilityTimer);
        this._visibilityTimer = null;
      }
    }
  },

  // 页面生命周期
  pageLifetimes: {
    show() {
      console.log('[waterfall-content] Page show. Component ID:', this._componentId);
      // 页面显示时，确保按钮可见
      this._checkButtonVisibility();
    },
    hide() {
      console.log('[waterfall-content] Page hide. Component ID:', this._componentId);
    }
  },

  methods: {
    // --- Proxy methods to call behavior methods from WXML ---
    // These allow the component's WXML to trigger behavior actions

    /**
     * Proxy for behavior's likePost
     */
    handleLike(e) {
      this.likePost(e);
    },

    /**
     * Proxy for behavior's viewPostDetail
     */
    handleViewDetail(e) {
      this.viewPostDetail(e);
    },

    /**
     * Proxy for behavior's onRefresh
     * Typically triggered by a custom pull-down refresh component or page event
     */
    handleRefresh() {
      console.log('[waterfall-content] Handling refresh trigger.');
      this.onRefresh();
    },

    /**
     * Proxy for behavior's loadMorePosts
     * Typically triggered by scroll-view reaching bottom or page event
     */
    handleLoadMore() {
      console.log('[waterfall-content] Handling load more trigger.');
      this.loadMorePosts();
    },

    // --- Component specific methods (if any) ---
    /**
     * 处理创建笔记按钮点击事件
     * 跳转到笔记创建/编辑页面
     */
    handleCreatePost() {
      console.log('[waterfall-content] Create post button clicked. Component ID:', this._componentId);

      // 添加轻微振动反馈
      wx.vibrateShort({ type: 'light' });

      // 检查用户是否登录
      const token = wx.getStorageSync('token');
      if (!token) {
        // 未登录用户显示登录提示
        wx.showToast({
          title: '请先登录后再创建内容',
          icon: 'none',
          duration: 2000
        });

        // 触发登录事件，但不自动跳转
        this.triggerEvent('login');
        return;
      }

      // 获取当前选中的标签
      const currentCategory = this.properties.currentCategory;

      // 构建跳转 URL，如果有选中标签且不是“推荐”，则传递标签 ID
      let url = '/pages/note-edit/index';
      if (currentCategory && currentCategory !== 'all') {
        url += `?tagId=${currentCategory}`;
      }

      // 跳转到笔记创建/编辑页面
      wx.navigateTo({
        url: url,
        success: () => {
          console.log('[waterfall-content] 成功跳转到笔记创建页面');
        },
        fail: err => {
          console.error('[waterfall-content] 跳转到笔记创建页面失败:', err);

          // 如果跳转失败，可能是页面不存在，尝试跳转到笔记管理页面
          wx.navigateTo({
            url: '/pages/note-management/index',
            success: () => {
              console.log('[waterfall-content] 跳转到笔记管理页面成功');
            },
            fail: err2 => {
              console.error('[waterfall-content] 跳转到笔记管理页面也失败:', err2);

              // 如果两个页面都不存在，显示提示
              wx.showToast({
                title: '笔记创建功能即将推出',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      });

      // 触发事件通知页面
      this.triggerEvent('create');

      // 确保按钮可见性
      this._checkButtonVisibility();
    },

    /**
     * 检查并确保创建按钮的可见性
     * 这个方法会尝试将当前组件的按钮置于最顶层
     */
    _checkButtonVisibility() {
      // 减少日志输出频率，只在调试时打开
      // console.log('[waterfall-content] Checking button visibility. Component ID:', this._componentId);

      try {
        // 获取当前页面栈
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];

        // 检查当前页面是否是广场页面
        if (currentPage && currentPage.route && currentPage.route.includes('square')) {
          // 使用选择器获取按钮元素
          const query = this.createSelectorQuery();
          query.select('.create-button').boundingClientRect(rect => {
            if (rect) {
              // 按钮存在，检查是否可见
              if (rect.width === 0 || rect.height === 0 || rect.top < 0 || rect.left < 0) {
                console.warn('[waterfall-content] Button exists but may not be visible:', rect);
                this._forceButtonVisibility();
              }
            } else {
              console.warn('[waterfall-content] Button not found in DOM');
              this._forceButtonVisibility();
            }
          }).exec();
        }
      } catch (err) {
        console.error('[waterfall-content] Error checking button visibility:', err);
      }
    },

    /**
     * 强制按钮可见
     * 通过操作DOM确保按钮可见
     */
    _forceButtonVisibility() {
      try {
        // 尝试通过更新样式强制按钮可见
        const buttonStyle = `
          position: fixed !important;
          right: 40rpx !important;
          bottom: 120rpx !important;
          z-index: 99999 !important;
          opacity: 1 !important;
          visibility: visible !important;
          pointer-events: auto !important;
        `;

        // 使用微信小程序的API动态添加样式
        wx.createSelectorQuery()
          .selectAll('.create-button')
          .fields({ node: true, size: true, rect: true }, function (res) {
            console.log('[waterfall-content] Found buttons:', res ? res.length : 0);
          })
          .exec();

        console.log('[waterfall-content] Forced button visibility');
      } catch (err) {
        console.error('[waterfall-content] Error forcing button visibility:', err);
      }
    }
  }
});
