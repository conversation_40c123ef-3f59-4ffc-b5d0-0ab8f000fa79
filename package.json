{"name": "nebula-learn", "version": "1.0.0", "description": "NebulaLearn - 个人软技能全方位提升平台", "scripts": {"dev": "echo '请使用微信开发者工具打开项目进行开发'", "lint": "eslint --ext .js,.jsx,.ts,.tsx .", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "node scripts/run-tests.js all --coverage", "test:check-coverage": "node scripts/check-coverage.js", "test:check-coverage:strict": "node scripts/check-coverage.js --strict", "test:report": "node scripts/generate-coverage-report.js", "prepare": "husky install", "build": "echo \"使用微信开发者工具构建小程序\"", "deploy": "echo \"使用微信开发者工具上传小程序\"", "analyze": "echo \"分析小程序包体积\"", "clean": "rm -rf dist/ && rm -rf node_modules/.cache"}, "dependencies": {"dayjs": "^1.10.6", "lodash": "^4.17.21"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/preset-env": "^7.15.0", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-jest": "^27.0.6", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.0", "eslint-plugin-prettier": "^3.4.0", "husky": "^7.0.1", "jest": "^27.0.6", "lint-staged": "^11.1.2", "miniprogram-simulate": "^1.4.0", "prettier": "^2.3.2", "typescript": "^5.8.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "engines": {"node": ">=14.0.0"}}