/**
 * 生成自定义覆盖率报告
 * 基于Jest生成的覆盖率数据，创建更友好的HTML报告
 */
const fs = require('fs');
const path = require('path');

// 覆盖率目标
const COVERAGE_TARGETS = {
  statements: 70,
  branches: 60,
  functions: 70,
  lines: 70
};

// 颜色配置
const COLORS = {
  good: '#27ae60',
  warning: '#f39c12',
  bad: '#e74c3c'
};

// 获取覆盖率等级和颜色
function getCoverageClass(value, target) {
  if (value >= target) {
    return { class: 'good', color: COLORS.good };
  } else if (value >= target * 0.8) {
    return { class: 'warning', color: COLORS.warning };
  } else {
    return { class: 'bad', color: COLORS.bad };
  }
}

// 生成文件行
function generateFileRow(file, coverage) {
  const statementsClass = getCoverageClass(coverage.statements.pct, COVERAGE_TARGETS.statements);
  const branchesClass = getCoverageClass(coverage.branches.pct, COVERAGE_TARGETS.branches);
  const functionsClass = getCoverageClass(coverage.functions.pct, COVERAGE_TARGETS.functions);
  const linesClass = getCoverageClass(coverage.lines.pct, COVERAGE_TARGETS.lines);

  return `
    <tr>
      <td><a href="lcov-report/${file.replace(/\\/g, '/')}.html" class="file-link">${file}</a></td>
      <td>
        <div class="${statementsClass.class}">${coverage.statements.pct}%</div>
        <div class="progress-bar">
          <div class="progress" style="width: ${coverage.statements.pct}%; background-color: ${statementsClass.color};"></div>
        </div>
      </td>
      <td>
        <div class="${branchesClass.class}">${coverage.branches.pct}%</div>
        <div class="progress-bar">
          <div class="progress" style="width: ${coverage.branches.pct}%; background-color: ${branchesClass.color};"></div>
        </div>
      </td>
      <td>
        <div class="${functionsClass.class}">${coverage.functions.pct}%</div>
        <div class="progress-bar">
          <div class="progress" style="width: ${coverage.functions.pct}%; background-color: ${functionsClass.color};"></div>
        </div>
      </td>
      <td>
        <div class="${linesClass.class}">${coverage.lines.pct}%</div>
        <div class="progress-bar">
          <div class="progress" style="width: ${coverage.lines.pct}%; background-color: ${linesClass.color};"></div>
        </div>
      </td>
    </tr>
  `;
}

// 主函数
function generateReport() {
  try {
    // 读取覆盖率数据
    const coveragePath = path.join(__dirname, '../coverage/coverage-summary.json');
    if (!fs.existsSync(coveragePath)) {
      console.error('覆盖率数据文件不存在!');
      process.exit(1);
    }

    const coverageData = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
    const totalCoverage = coverageData.total;

    // 读取模板
    const templatePath = path.join(__dirname, './coverage-report-template.html');
    let template = fs.readFileSync(templatePath, 'utf8');

    // 替换总体覆盖率数据
    const statementsClass = getCoverageClass(totalCoverage.statements.pct, COVERAGE_TARGETS.statements);
    const branchesClass = getCoverageClass(totalCoverage.branches.pct, COVERAGE_TARGETS.branches);
    const functionsClass = getCoverageClass(totalCoverage.functions.pct, COVERAGE_TARGETS.functions);
    const linesClass = getCoverageClass(totalCoverage.lines.pct, COVERAGE_TARGETS.lines);

    template = template.replace('{{DATE}}', new Date().toLocaleString());

    template = template.replace('{{STATEMENTS_PCT}}', totalCoverage.statements.pct.toFixed(2));
    template = template.replace('{{STATEMENTS_CLASS}}', statementsClass.class);
    template = template.replace('{{STATEMENTS_COLOR}}', statementsClass.color);
    template = template.replace('{{STATEMENTS_TARGET}}', COVERAGE_TARGETS.statements);

    template = template.replace('{{BRANCHES_PCT}}', totalCoverage.branches.pct.toFixed(2));
    template = template.replace('{{BRANCHES_CLASS}}', branchesClass.class);
    template = template.replace('{{BRANCHES_COLOR}}', branchesClass.color);
    template = template.replace('{{BRANCHES_TARGET}}', COVERAGE_TARGETS.branches);

    template = template.replace('{{FUNCTIONS_PCT}}', totalCoverage.functions.pct.toFixed(2));
    template = template.replace('{{FUNCTIONS_CLASS}}', functionsClass.class);
    template = template.replace('{{FUNCTIONS_COLOR}}', functionsClass.color);
    template = template.replace('{{FUNCTIONS_TARGET}}', COVERAGE_TARGETS.functions);

    template = template.replace('{{LINES_PCT}}', totalCoverage.lines.pct.toFixed(2));
    template = template.replace('{{LINES_CLASS}}', linesClass.class);
    template = template.replace('{{LINES_COLOR}}', linesClass.color);
    template = template.replace('{{LINES_TARGET}}', COVERAGE_TARGETS.lines);

    // 生成文件行
    let fileRows = '';
    Object.keys(coverageData).forEach(file => {
      if (file !== 'total') {
        fileRows += generateFileRow(file, coverageData[file]);
      }
    });
    template = template.replace('{{FILE_ROWS}}', fileRows);

    // 写入报告文件
    const reportPath = path.join(__dirname, '../coverage/custom-report.html');
    fs.writeFileSync(reportPath, template);

    console.log(`自定义覆盖率报告已生成: ${reportPath}`);
  } catch (error) {
    console.error(`生成报告失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行
generateReport();
