{"description": "项目配置文件", "packOptions": {"ignore": [{"type": "folder", "value": "归档文档"}, {"type": "folder", "value": "docs"}, {"type": "folder", "value": "scripts"}, {"type": "file", "value": "jest.config.js"}, {"type": "file", "value": "jest.setup.js"}, {"type": "file", "value": "babel.config.js"}, {"type": "file", "value": ".eslintrc.js"}, {"type": "file", "value": "tsconfig.json"}], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": false, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false, "filterUnusedFiles": false, "disableUseStrict": false, "useCompilerPlugins": false, "useStaticServer": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.8.6", "appid": "wx74fd21954edfab48", "projectname": "AIBubble", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}}