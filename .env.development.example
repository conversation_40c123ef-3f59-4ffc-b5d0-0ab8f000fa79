# 开发环境配置模板
# 使用方法：复制此文件为.env.development，并填入实际值

# 服务器配置
PORT=9090
NODE_ENV=development
API_PREFIX=/api/v1
CORS_ORIGIN=*
BASE_URL=http://localhost:9090

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=aibubb_db
DB_USER=aibubb_user
DB_PASSWORD=aibubb_password
DB_DIALECT=mysql
DB_LOGGING=true
DB_POOL_MAX=10
DB_POOL_MIN=0

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=dev_jwt_secret_key
JWT_EXPIRES_IN=86400
JWT_REFRESH_EXPIRES_IN=604800

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# AI提供商配置
# 可选值: bytedance, aliyun, hunyuan
# 如果设置为aliyun，则使用DASHSCOPE_API_KEY环境变量
# 如果设置为hunyuan，则使用HUNYUAN_API_KEY环境变量
# 如果设置为bytedance，则使用ARK_API_KEY环境变量
AI_PROVIDER=bytedance

# 字节大模型API配置
# 使用OpenAI SDK调用字节大模型
ARK_API_KEY=your_ark_api_key_here
# 字节大模型ID
ARK_API_MODEL=deepseek-r1-250120

# 阿里云百炼API配置
# 使用OpenAI SDK调用阿里云百炼API
# 请在此处填写您的阿里云百炼API密钥
DASHSCOPE_API_KEY=sk-your-aliyun-api-key-here
# 阿里云百炼模型ID
DASHSCOPE_API_MODEL=qwen-plus

# 腾讯混元大模型API配置
# 使用OpenAI SDK调用腾讯混元大模型
# 请在此处填写您的腾讯混元API密钥
HUNYUAN_API_KEY=your_hunyuan_api_key_here
# 腾讯混元模型ID
HUNYUAN_API_MODEL=hunyuan-turbos-latest

# 日志配置
LOG_LEVEL=debug
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_LOGIN_WINDOW_MS=3600000
RATE_LIMIT_LOGIN_MAX=20
