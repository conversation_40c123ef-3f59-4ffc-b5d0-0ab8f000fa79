// bubble-canvas.js
// 气泡画布管理

// 动画和渲染常量
const ANIMATION_SPEED_MULTIPLIER = 0.064; // 动画速度乘数，从0.08减少20%
const ANIMATION_BASE_SPEED = 0.64; // 基础速度，从0.8减少20%
const SHADOW_BLUR = 4; // 阴影模糊度

// 不再定义本地泡泡主题配置，完全从index.js获取

/**
 * 气泡画布管理类
 */
class BubbleCanvas {
  constructor(page) {
    this.page = page;
    this.canvas = null;
    this.ctx = null;
    this.canvasWidth = 0;
    this.canvasHeight = 0;
    this.bubbles = [];
    this.lastTimestamp = 0;
    this.animationTimer = null;
    this.navHeight = 0;

    // 拖动相关属性
    this.draggedBubble = null;
    this.dragStartX = 0;
    this.dragStartY = 0;
    this.bubbleStartX = 0;
    this.bubbleStartY = 0;
    this.isDragging = false;
    this.dragStartTime = 0;
    this.dragDistance = 0;
  }

  /**
   * 初始化画布
   * @returns {boolean} 是否初始化成功
   */
  init() {
    try {
      const query = wx.createSelectorQuery().in(this.page);

      query.select('#bubble-canvas')
        .fields({ node: true, size: true })
        .exec(res => {
          if (!res || !res[0] || !res[0].node) {
            console.error('获取画布节点失败', res);
            return false;
          }

          try {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            if (!ctx) {
              console.error('获取2d上下文失败');
              return false;
            }

            this.canvas = canvas;
            this.ctx = ctx;

            // 设置画布尺寸，适配不同设备像素比
            const dpr = wx.getSystemInfoSync().pixelRatio;
            canvas.width = res[0].width * dpr;
            canvas.height = res[0].height * dpr;
            ctx.scale(dpr, dpr);

            this.canvasWidth = res[0].width;
            this.canvasHeight = res[0].height;

            // 获取状态栏高度和导航栏高度
            const windowInfo = wx.getWindowInfo();
            const statusBarHeight = windowInfo.statusBarHeight || 20;
            this.navHeight = statusBarHeight + 45; // 状态栏 + 导航栏高度(90rpx = 45px)

            console.log('画布初始化完成', {
              width: this.canvasWidth,
              height: this.canvasHeight,
              navHeight: this.navHeight,
              dpr: dpr
            });

            // 初始化泡泡
            this.initBubbles();

            // 开始动画循环
            this.startAnimation();

            // 初始化触摸事件
            this.initTouchEvents();

            this.page.setData({ initialized: true });

            return true;
          } catch (err) {
            console.error('初始化Canvas上下文时出错', err);
            return false;
          }
        });

      return true; // 返回true表示查询已启动
    } catch (err) {
      console.error('创建选择器时出错', err);
      return false;
    }
  }

  /**
   * 初始化泡泡
   */
  initBubbles() {
    // 从页面获取泡泡主题 - 确保使用最新数据
    let bubbleThemes = this.page.getBubbleThemes ? this.page.getBubbleThemes() : [];

    // 如果页面方法不可用，尝试从themeManager获取
    if ((!bubbleThemes || bubbleThemes.length === 0) && this.page.themeManager) {
      bubbleThemes = this.page.themeManager.getCurrentThemes();
    }

    // 如果还是没有数据，使用默认主题
    if (!bubbleThemes || bubbleThemes.length === 0) {
      console.warn('泡泡主题数据为空，使用默认主题');
      bubbleThemes = this.getDefaultThemes();
    }

    const bubbles = [];
    const count = bubbleThemes.length; // 泡泡数量与主题数量一致

    // 计算底部菜单的上边界位置和顶部导航的下边界位置
    const tabBarHeight = 50; // 微信小程序默认tabBar高度约为50px
    const tabbarTop = this.canvasHeight - tabBarHeight; // 底部菜单上边界，减去tabBar高度
    // 顶部边界应该是画布的真正顶部，即0，而不是navHeight
    // 因为画布坐标系从(0,0)开始，navHeight是用于布局计算的，不是碰撞检测边界
    const navBottom = 0; // 顶部边界设为画布顶部

    // 为了确保泡泡均匀分布，使用网格布局
    const gridColumns = 3; // 列数
    const gridRows = Math.ceil(count / gridColumns); // 行数

    const cellWidth = this.canvasWidth / gridColumns;
    const cellHeight = (tabbarTop - navBottom) / gridRows;

    for (let i = 0; i < count; i++) {
      const theme = bubbleThemes[i % bubbleThemes.length];

      // 基础半径范围为40-60
      let baseRadius = 40 + Math.random() * 20;

      // 根据文字长度动态调整泡泡大小
      const textLength = theme.name.length;
      if (textLength > 3) {
        // 当文字长度超过3个字符时，增加泡泡半径
        // 每多一个字符，增加基础半径的5%
        baseRadius += baseRadius * (textLength - 3) * 0.05;

        // 如果文字需要换行显示，额外增加泡泡大小以提供足够的垂直空间
        if (textLength >= 4) {
          const lineCount = Math.ceil(textLength / 2);
          // 当有多行时，额外增加半径
          if (lineCount > 1) {
            baseRadius += baseRadius * 0.1 * (lineCount - 1);
          }
        }
      }

      const radius = baseRadius; // 使用调整后的半径

      // 计算在网格中的位置
      const col = i % gridColumns;
      const row = Math.floor(i / gridColumns);

      // 计算泡泡的初始位置，偏移网格中心位置以获得随机效果
      const offsetX = (Math.random() - 0.5) * cellWidth * 0.6;
      const offsetY = (Math.random() - 0.5) * cellHeight * 0.6;

      const x = cellWidth * (col + 0.5) + offsetX;
      const y = navBottom + cellHeight * (row + 0.5) + offsetY;

      bubbles.push({
        id: `bubble-${i}`,
        text: theme.name, // 只保留主标签
        color: theme.color,
        x,
        y,
        radius,
        velocityX: (Math.random() - 0.5) * ANIMATION_BASE_SPEED,
        velocityY: (Math.random() - 0.5) * ANIMATION_BASE_SPEED,
        isHovered: false,
        isClicked: false,
        pulsePhase: Math.random() * Math.PI * 2,
        pulseSpeed: 0.001 + Math.random() * 0.002,
        rotationSpeed: (Math.random() - 0.5) * 0.001,
        lastX: x,
        lastY: y,
        stuckFrames: 0,
        // 新增属性
        collisionCount: 0, // 碰撞计数
        isAccelerated: false, // 是否处于加速状态
        originalVelocityX: 0, // 原始X速度
        originalVelocityY: 0, // 原始Y速度
        isDragged: false // 是否被拖动
      });
    }

    this.bubbles = bubbles;
    console.log('泡泡初始化完成', bubbles);
  }

  /**
   * 开始动画循环
   */
  startAnimation() {
    // 避免重复启动动画
    if (this.animationTimer) {
      try {
        this.canvas.cancelAnimationFrame(this.animationTimer);
      } catch (e) {
        console.error('取消动画帧失败', e);
      }
      this.animationTimer = null;
    }

    // 检查canvas和ctx是否已初始化
    if (!this.canvas || !this.ctx) {
      console.error('Canvas或Context未初始化，无法启动动画');
      // 延迟重试
      setTimeout(() => {
        if (this.canvas && this.ctx) {
          console.log('延迟后尝试启动动画');
          this.startAnimation();
        } else {
          console.error('Canvas仍未初始化，请检查初始化过程');
          this.page.setData({ initialized: false });
        }
      }, 1000);
      return;
    }

    // 初始化时间戳
    this.lastTimestamp = Date.now();

    // 定义动画循环函数
    const animate = () => {
      try {
        // 确保canvas和ctx仍然有效
        if (!this.canvas || !this.ctx) {
          console.error('Canvas或Context已失效，停止动画');
          return;
        }

        // 获取当前时间戳并计算时间增量
        const now = Date.now();
        const deltaTime = now - this.lastTimestamp;
        this.lastTimestamp = now;

        // 更新和绘制泡泡
        this.updateBubbles(deltaTime);
        this.drawBubbles();

        // 继续动画循环
        try {
          this.animationTimer = this.canvas.requestAnimationFrame(animate);
        } catch (err) {
          console.error('请求动画帧失败', err);
          // 使用setTimeout作为备选方案
          this.animationTimer = setTimeout(() => {
            animate();
          }, 16); // 约60fps
        }
      } catch (err) {
        console.error('动画循环出错', err);
        // 如果出错，延迟后重试
        setTimeout(() => {
          this.startAnimation();
        }, 1000);
      }
    };

    // 启动动画循环
    try {
      this.animationTimer = this.canvas.requestAnimationFrame(animate);
      console.log('动画循环已启动');
    } catch (err) {
      console.error('启动动画失败，使用setTimeout替代', err);
      // 使用setTimeout作为备选方案
      this.animationTimer = setTimeout(animate, 16);
    }
  }

  /**
   * 更新泡泡位置
   * @param {number} deltaTime - 时间增量
   */
  updateBubbles(deltaTime) {
    // 限制最大时间增量，避免大的时间跳跃
    const maxDeltaTime = 50;
    const clampedDeltaTime = Math.min(deltaTime, maxDeltaTime);

    // 计算底部菜单的上边界位置和顶部导航的下边界位置
    // 使用固定的tabBar高度，避免频繁调用系统API
    const tabBarHeight = 50; // 微信小程序默认tabBar高度约为50px

    // 底部边界应该是画布高度减去tabBar高度，这样泡泡才会在真正碰到tabBar时反弹
    const tabbarTop = this.canvasHeight - tabBarHeight;
    // 顶部边界应该是画布的真正顶部，即0，而不是navHeight
    // 因为画布坐标系从(0,0)开始，navHeight是用于布局计算的，不是碰撞检测边界
    const navBottom = 0; // 顶部边界设为画布顶部

    // 更新泡泡位置
    this.bubbles.forEach(bubble => {
      // 如果泡泡被点击或正在被拖动，则不更新位置
      if (bubble.isClicked || bubble.isDragged) return;

      // 应用速度
      bubble.x += bubble.velocityX * clampedDeltaTime * ANIMATION_SPEED_MULTIPLIER;
      bubble.y += bubble.velocityY * clampedDeltaTime * ANIMATION_SPEED_MULTIPLIER;

      // 脉动效果
      bubble.pulsePhase += bubble.pulseSpeed * clampedDeltaTime;
      if (bubble.pulsePhase > Math.PI * 2) {
        bubble.pulsePhase -= Math.PI * 2;
      }

      // 碰撞检测 - 左右边界
      let hasCollided = false;

      if (bubble.x - bubble.radius < 0) {
        bubble.x = bubble.radius;
        bubble.velocityX = Math.abs(bubble.velocityX) * 0.8; // 减少反弹速度
        hasCollided = true;
      } else if (bubble.x + bubble.radius > this.canvasWidth) {
        bubble.x = this.canvasWidth - bubble.radius;
        bubble.velocityX = -Math.abs(bubble.velocityX) * 0.8; // 减少反弹速度
        hasCollided = true;
      }

      // 碰撞检测 - 顶部导航下边界和底部菜单上边界
      if (bubble.y - bubble.radius < navBottom) {
        bubble.y = navBottom + bubble.radius;
        bubble.velocityY = Math.abs(bubble.velocityY) * 0.8; // 减少反弹速度
        hasCollided = true;
      } else if (bubble.y + bubble.radius > tabbarTop) {
        bubble.y = tabbarTop - bubble.radius;
        bubble.velocityY = -Math.abs(bubble.velocityY) * 0.8; // 减少反弹速度
        hasCollided = true;
      }

      // 如果发生碰撞且泡泡处于加速状态，增加碰撞计数
      if (hasCollided && bubble.isAccelerated) {
        bubble.collisionCount++;

        // 如果碰撞次数达到3次，恢复正常速度
        if (bubble.collisionCount >= 3) {
          // 恢复到原始速度
          if (bubble.originalVelocityX !== 0 || bubble.originalVelocityY !== 0) {
            bubble.velocityX = bubble.originalVelocityX;
            bubble.velocityY = bubble.originalVelocityY;
          } else {
            // 如果没有原始速度记录，生成一个新的随机速度
            const angle = Math.random() * Math.PI * 2;
            bubble.velocityX = Math.cos(angle) * ANIMATION_BASE_SPEED * 0.8;
            bubble.velocityY = Math.sin(angle) * ANIMATION_BASE_SPEED * 0.8;
          }

          // 重置加速状态和碰撞计数
          bubble.isAccelerated = false;
          bubble.collisionCount = 0;
          bubble.originalVelocityX = 0;
          bubble.originalVelocityY = 0;
        }
      }

      // 检测泡泡是否卡住
      if (Math.abs(bubble.x - bubble.lastX) < 0.1 && Math.abs(bubble.y - bubble.lastY) < 0.1) {
        bubble.stuckFrames++;
        if (bubble.stuckFrames > 5) {
          // 随机改变速度方向，防止卡住
          const angle = Math.random() * Math.PI * 2;
          const speed = ANIMATION_BASE_SPEED * (0.5 + Math.random() * 0.5);
          bubble.velocityX = Math.cos(angle) * speed;
          bubble.velocityY = Math.sin(angle) * speed;
          bubble.stuckFrames = 0;
        }
      } else {
        bubble.stuckFrames = 0;
      }

      // 随机添加一些微小的加速度变化，使运动更自然
      if (Math.random() < 0.05) {
        bubble.velocityX += (Math.random() - 0.5) * 0.01;
        bubble.velocityY += (Math.random() - 0.5) * 0.01;
      }

      // 限制最大速度
      const maxSpeed = ANIMATION_BASE_SPEED * 1.5;
      const currentSpeed = Math.sqrt(bubble.velocityX * bubble.velocityX + bubble.velocityY * bubble.velocityY);
      if (currentSpeed > maxSpeed) {
        bubble.velocityX = (bubble.velocityX / currentSpeed) * maxSpeed;
        bubble.velocityY = (bubble.velocityY / currentSpeed) * maxSpeed;
      }

      // 记录上一帧位置
      bubble.lastX = bubble.x;
      bubble.lastY = bubble.y;
    });
  }

  /**
   * 处理泡泡之间的碰撞 (已弃用 - 泡泡之间将不再碰撞)
   */
  handleBubblesCollision() {
    // 该方法被保留但不再使用
    // 泡泡之间可以自由穿过彼此
  }

  /**
   * 绘制泡泡
   */
  drawBubbles() {
    const ctx = this.ctx;
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

    // 按照大小排序，小的泡泡先绘制
    const sortedBubbles = [...this.bubbles].sort((a, b) => a.radius - b.radius);

    // 绘制每个泡泡
    sortedBubbles.forEach(bubble => {
      // 呼吸效果 - 脉动半径变化
      const pulseScale = 1 + Math.sin(bubble.pulsePhase) * 0.03;
      const scaleMultiplier = bubble.isHovered ? 1.15 : pulseScale;
      const actualRadius = bubble.radius * scaleMultiplier;

      // 保存当前状态
      ctx.save();

      // 创建径向渐变 - 从泡泡中心往外渐变，显得更立体
      // 光源位置在左上角，偏移中心点的15%
      const gradientX = bubble.x - actualRadius * 0.15;
      const gradientY = bubble.y - actualRadius * 0.15;

      // 创建径向渐变
      const gradient = ctx.createRadialGradient(
        gradientX, gradientY, 0,
        bubble.x, bubble.y, actualRadius
      );

      // 设置渐变颜色
      const baseColor = bubble.color;

      // 获取基础颜色的亮色和暗色版本
      const lighterColor = this.lightenColor(baseColor, 15);
      const darkerColor = this.darkenColor(baseColor, 15);

      // 设置渐变色标
      gradient.addColorStop(0, lighterColor); // 内侧亮色
      gradient.addColorStop(0.7, baseColor); // 中间原色
      gradient.addColorStop(1, darkerColor); // 外侧暗色

      // 绘制泡泡主体
      ctx.beginPath();
      ctx.arc(bubble.x, bubble.y, actualRadius, 0, Math.PI * 2);
      ctx.fillStyle = gradient;

      // 添加阴影
      ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
      ctx.shadowBlur = SHADOW_BLUR;
      ctx.shadowOffsetX = 2;
      ctx.shadowOffsetY = 2;

      ctx.fill();

      // 如果泡泡被点击，绘制一个高亮边框
      if (bubble.isClicked) {
        ctx.beginPath();
        ctx.arc(bubble.x, bubble.y, actualRadius + 3, 0, Math.PI * 2);
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 2;
        ctx.stroke();
      }

      // 如果泡泡正在被拖动，绘制科技感光圈
      if (bubble.isDragged) {
        // 计算脉动效果 - 使用时间来创建呼吸效果
        const now = Date.now();
        const pulseValue = Math.sin(now * 0.005) * 0.2 + 0.8; // 0.6-1.0之间脉动

        // 创建外部光圈渐变 - 减小30%
        const outerRadius = actualRadius * (1.05 + pulseValue * 0.14); // 光圈大小随脉动变化
        const glowGradient = ctx.createRadialGradient(
          bubble.x, bubble.y, actualRadius,
          bubble.x, bubble.y, outerRadius
        );

        // 设置光圈颜色（使用泡泡颜色但更加透明）
        const baseColor = bubble.color;
        const alphaBase = 0.5 * pulseValue; // 透明度随脉动变化
        glowGradient.addColorStop(0, this.hexToRgba(baseColor, alphaBase));
        glowGradient.addColorStop(0.5, this.hexToRgba(baseColor, alphaBase * 0.4));
        glowGradient.addColorStop(1, this.hexToRgba(baseColor, 0));

        // 绘制光圈
        ctx.beginPath();
        ctx.arc(bubble.x, bubble.y, outerRadius, 0, Math.PI * 2);
        ctx.fillStyle = glowGradient;
        ctx.fill();

        // 绘制内部脉动光环
        const innerGlowGradient = ctx.createRadialGradient(
          bubble.x, bubble.y, actualRadius * 0.8,
          bubble.x, bubble.y, actualRadius
        );

        // 设置内部光环颜色
        const innerAlpha = 0.6 * pulseValue; // 内部光环透明度也随脉动变化
        innerGlowGradient.addColorStop(0, this.hexToRgba('#FFFFFF', 0));
        innerGlowGradient.addColorStop(0.8, this.hexToRgba('#FFFFFF', innerAlpha * 0.3));
        innerGlowGradient.addColorStop(1, this.hexToRgba('#FFFFFF', innerAlpha));

        // 绘制内部光环
        ctx.beginPath();
        ctx.arc(bubble.x, bubble.y, actualRadius, 0, Math.PI * 2);
        ctx.fillStyle = innerGlowGradient;
        ctx.fill();

        // 添加外部光点装饰
        this.drawGlowParticles(ctx, bubble.x, bubble.y, actualRadius, outerRadius, baseColor, pulseValue);
      }

      // 重置阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      // 添加高光效果 - 在泡泡左上方
      const highlightRadius = actualRadius * 0.5;
      const highlightX = bubble.x - actualRadius * 0.3;
      const highlightY = bubble.y - actualRadius * 0.3;

      const highlightGradient = ctx.createRadialGradient(
        highlightX, highlightY, 0,
        highlightX, highlightY, highlightRadius
      );

      // 调整高光不透明度
      highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.5)');
      highlightGradient.addColorStop(0.6, 'rgba(255, 255, 255, 0.2)');
      highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

      // 绘制高光
      ctx.beginPath();
      ctx.arc(highlightX, highlightY, highlightRadius, 0, Math.PI * 2);
      ctx.fillStyle = highlightGradient;
      ctx.fill();

      // 恢复上下文状态
      ctx.restore();

      // 绘制泡泡文字
      ctx.save();

      // 设置字体大小基于泡泡大小
      // 根据文字长度动态调整字体大小，文字越长，字体越小
      const textLength = bubble.text.length;
      let fontSizeMultiplier = 1;

      // 当文字长度超过3个字符时，开始缩小字体
      if (textLength > 3) {
        fontSizeMultiplier = 1 - (textLength - 3) * 0.1; // 每多1个字符，字体缩小10%
        fontSizeMultiplier = Math.max(fontSizeMultiplier, 0.7); // 最小缩小到70%
      }

      const fontSizeMain = actualRadius * 0.45 * fontSizeMultiplier;

      // 设置文字阴影
      ctx.shadowColor = 'rgba(0,0,0,0.3)';
      ctx.shadowBlur = 2;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;

      // 绘制中文名称
      ctx.font = `bold ${fontSizeMain}px sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 根据背景颜色的亮度选择文字颜色
      const isLight = this.isLightColor(bubble.color);
      ctx.fillStyle = isLight ? '#000000' : '#FFFFFF';

      // 检查文字长度，四个字及以上时考虑换行处理（原来是超过4个字才换行）
      if (textLength >= 4) {
        // 文字换行处理 - 按照2个字一行进行均匀分割
        const lines = [];
        for (let i = 0; i < textLength; i += 2) {
          lines.push(bubble.text.substr(i, 2));
        }

        // 绘制多行文字
        lines.forEach((line, index) => {
          const lineCount = lines.length;
          // 调整行间距系数为1.1，保持适中的间距
          const lineSpacing = fontSizeMain * 1.1;
          // 计算每行的垂直位置，使整体垂直居中
          const lineY = bubble.y - ((lineCount - 1) * lineSpacing / 2) + index * lineSpacing;
          ctx.fillText(line, bubble.x, lineY);
        });
      } else {
        // 对于3个字及以下的情况，保持原来的单行显示
        ctx.fillText(bubble.text, bubble.x, bubble.y);
      }

      ctx.restore();
    });
  }

  /**
   * 使颜色变亮
   * @param {string} color - 十六进制颜色
   * @param {number} percent - 变亮百分比
   * @returns {string} - 新颜色
   */
  lightenColor(color, percent) {
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;

    return '#' + (
      0x1000000 +
      (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
      (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
      (B < 255 ? (B < 1 ? 0 : B) : 255)
    ).toString(16).slice(1);
  }

  /**
   * 使颜色变暗
   * @param {string} color - 十六进制颜色
   * @param {number} percent - 变暗百分比
   * @returns {string} - 新颜色
   */
  darkenColor(color, percent) {
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) - amt;
    const G = (num >> 8 & 0x00FF) - amt;
    const B = (num & 0x0000FF) - amt;

    return '#' + (
      0x1000000 +
      (R > 0 ? R : 0) * 0x10000 +
      (G > 0 ? G : 0) * 0x100 +
      (B > 0 ? B : 0)
    ).toString(16).slice(1);
  }

  /**
   * 检测是否为浅色
   * @param {string} color - 十六进制颜色
   * @returns {boolean} - 是否为浅色
   */
  isLightColor(color) {
    // 预定义的浅色名称列表
    const lightColorNames = ['yellow', 'amber', 'lime', 'brightYellow', 'green', 'cyan'];
    if (typeof color === 'string' && lightColorNames.includes(color.toLowerCase())) {
      return true;
    }

    try {
      // 确保颜色是十六进制格式
      if (!color || typeof color !== 'string') {
        return false;
      }

      // 如果颜色不是十六进制格式，无法计算亮度
      if (!color.startsWith('#')) {
        return false;
      }

      // 移除#前缀
      const hexColor = color.replace('#', '');

      // 解析RGB值
      const r = parseInt(hexColor.substr(0, 2), 16);
      const g = parseInt(hexColor.substr(2, 2), 16);
      const b = parseInt(hexColor.substr(4, 2), 16);

      // 计算亮度 (基于人眼对不同颜色的感知)
      // 公式: (0.299*R + 0.587*G + 0.114*B)
      const brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      // 亮度大于0.6认为是浅色
      return brightness > 0.6;
    } catch (err) {
      console.error('颜色亮度计算失败', err);
      return false;
    }
  }

  /**
   * 初始化触摸事件
   */
  initTouchEvents() {
    // 触摸事件通过页面的事件处理函数处理
  }

  /**
   * 处理触摸开始事件
   * @param {object} e - 触摸事件对象
   * @returns {object|null} - 被触摸的泡泡对象或null
   */
  handleTouchStart(e) {
    const touch = e.touches[0];
    const { x, y } = touch;

    // 记录触摸开始时间
    this.dragStartTime = Date.now();
    this.dragStartX = x;
    this.dragStartY = y;
    this.isDragging = false;
    this.dragDistance = 0;

    // 检测点击的是哪个泡泡
    for (const bubble of this.bubbles) {
      const distance = Math.sqrt(
        Math.pow(x - bubble.x, 2) + Math.pow(y - bubble.y, 2)
      );

      if (distance <= bubble.radius) {
        // 记录拖动开始的泡泡和位置
        this.draggedBubble = bubble;
        this.bubbleStartX = bubble.x;
        this.bubbleStartY = bubble.y;

        // 标记泡泡为拖动状态
        bubble.isDragged = true;

        // 记录原始速度
        bubble.originalVelocityX = bubble.velocityX;
        bubble.originalVelocityY = bubble.velocityY;

        // 不立即返回主题，等待触摸结束时判断是点击还是拖动
        return null;
      }
    }

    return null;
  }

  /**
   * 处理触摸移动事件
   * @param {object} e - 触摸事件对象
   * @returns {string} - 鼠标样式
   */
  handleTouchMove(e) {
    const touch = e.touches[0];
    const { x, y } = touch;

    // 如果有正在拖动的泡泡
    if (this.draggedBubble) {
      // 计算拖动距离
      const dragX = x - this.dragStartX;
      const dragY = y - this.dragStartY;
      this.dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

      // 如果拖动距离超过阈值，标记为拖动状态（增加阈值减少误判）
      if (this.dragDistance > 15) {
        this.isDragging = true;
      }

      // 限制拖动距离不超过泡泡半径
      const maxDragDistance = this.draggedBubble.radius;
      if (this.dragDistance > maxDragDistance) {
        // 按比例缩放拖动距离
        const scale = maxDragDistance / this.dragDistance;
        const limitedDragX = dragX * scale;
        const limitedDragY = dragY * scale;

        // 更新泡泡位置
        this.draggedBubble.x = this.bubbleStartX + limitedDragX;
        this.draggedBubble.y = this.bubbleStartY + limitedDragY;
      } else {
        // 直接更新泡泡位置
        this.draggedBubble.x = this.bubbleStartX + dragX;
        this.draggedBubble.y = this.bubbleStartY + dragY;
      }

      return 'grabbing';
    }

    // 如果没有拖动的泡泡，更新悬停状态
    let hasHoveredBubble = false;
    for (const bubble of this.bubbles) {
      if (bubble.isDragged) continue; // 跳过正在拖动的泡泡

      const distance = Math.sqrt(
        Math.pow(x - bubble.x, 2) + Math.pow(y - bubble.y, 2)
      );

      if (distance <= bubble.radius) {
        bubble.isHovered = true;
        hasHoveredBubble = true;
      } else {
        bubble.isHovered = false;
      }
    }

    // 返回鼠标指针样式
    return hasHoveredBubble ? 'pointer' : 'default';
  }

  /**
   * 处理触摸结束事件
   * @returns {object|null} - 如果是点击操作，返回泡泡主题；如果是拖动操作，返回null
   */
  handleTouchEnd() {
    // 如果有拖动的泡泡
    if (this.draggedBubble) {
      const bubble = this.draggedBubble;

      // 如果是拖动操作
      if (this.isDragging) {
        // 计算拖动向量
        const dragX = bubble.x - this.bubbleStartX;
        const dragY = bubble.y - this.bubbleStartY;
        const dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

        // 计算拖动距离与最大拖动距离(半径)的比例，用于调整加速度
        const dragRatio = Math.min(1.0, dragDistance / bubble.radius);

        if (dragDistance > 0) {
          // 计算反方向的单位向量
          const directionX = -dragX / dragDistance;
          const directionY = -dragY / dragDistance;

          // 设置加速度为正常速度的2倍
          // 计算当前速度的大小
          const currentSpeed = Math.sqrt(
            bubble.originalVelocityX * bubble.originalVelocityX +
            bubble.originalVelocityY * bubble.originalVelocityY
          );

          // 如果原始速度接近于0，使用基础速度
          const baseSpeed = currentSpeed > 0.01 ? currentSpeed : ANIMATION_BASE_SPEED;

          // 加速速度为正常速度的3倍，并根据拖动距离比例调整
          // 拖动距离越接近最大值(半径)，加速效果越明显
          const acceleratedSpeed = baseSpeed * 3.0 * dragRatio;

          // 应用新速度
          bubble.velocityX = directionX * acceleratedSpeed;
          bubble.velocityY = directionY * acceleratedSpeed;

          // 标记为加速状态
          bubble.isAccelerated = true;
          bubble.collisionCount = 0;
        }

        // 重置拖动状态
        bubble.isDragged = false;
        this.draggedBubble = null;
        this.isDragging = false;

        // 拖动操作不触发点击事件
        return null;
      } else {
        // 如果是点击操作（没有明显拖动）
        // 获取泡泡主题配置（从页面获取）
        const bubbleThemes = this.page.getBubbleThemes ? this.page.getBubbleThemes() : [];

        // 找到匹配的主题 - 使用更智能的匹配逻辑
        let theme = bubbleThemes.find(t => t.name === bubble.text);

        // 如果精确匹配失败，尝试模糊匹配
        if (!theme) {
          theme = bubbleThemes.find(t =>
            t.name.includes(bubble.text) ||
            bubble.text.includes(t.name) ||
            t.englishName === bubble.text ||
            t.id === bubble.text
          );
        }

        if (theme) {
          // 标记泡泡为点击状态
          bubble.isClicked = true;

          // 重置拖动状态
          bubble.isDragged = false;
          this.draggedBubble = null;

          // 返回主题和交互信息
          return {
            theme: theme,
            tagId: theme.id,
            interactionType: 'click',
            duration: Date.now() - this.dragStartTime,
            positionX: bubble.x,
            positionY: bubble.y
          };
        } else {
          // 临时解决方案：如果没有找到匹配的主题，使用第一个可用主题
          if (bubbleThemes.length > 0) {
            const fallbackTheme = bubbleThemes[0];
            bubble.isClicked = true;
            bubble.isDragged = false;
            this.draggedBubble = null;

            return {
              theme: fallbackTheme,
              tagId: fallbackTheme.id,
              interactionType: 'click',
              duration: Date.now() - this.dragStartTime,
              positionX: bubble.x,
              positionY: bubble.y
            };
          }
        }
      }
    }

    // 重置所有泡泡的悬停状态
    for (const bubble of this.bubbles) {
      bubble.isHovered = false;
    }

    // 重置拖动状态
    this.draggedBubble = null;
    this.isDragging = false;

    return null;
  }

  /**
   * 重置点击状态
   */
  resetClickState() {
    for (const bubble of this.bubbles) {
      if (bubble.isClicked) {
        // 为被点击的泡泡重新生成随机速度，保持与整体速度一致
        const angle = Math.random() * Math.PI * 2;
        bubble.velocityX = Math.cos(angle) * ANIMATION_BASE_SPEED * 0.8;
        bubble.velocityY = Math.sin(angle) * ANIMATION_BASE_SPEED * 0.8;
        bubble.isClicked = false;
      }
    }
  }

  /**
   * 停止动画
   */
  stopAnimation() {
    if (this.animationTimer) {
      this.canvas.cancelAnimationFrame(this.animationTimer);
      this.animationTimer = null;
    }
  }

  /**
   * 更新泡泡主题
   * @param {Array} newThemes - 新的泡泡主题配置
   */
  updateBubbleThemes(newThemes) {
    if (!newThemes || newThemes.length === 0) {
      console.warn('尝试更新空的泡泡主题');
      return;
    }

    console.log('更新泡泡主题', newThemes);

    // 保存当前泡泡的位置和速度信息
    const bubbleStates = {};
    this.bubbles.forEach(bubble => {
      const id = bubble.id;
      bubbleStates[id] = {
        x: bubble.x,
        y: bubble.y,
        velocityX: bubble.velocityX,
        velocityY: bubble.velocityY,
        radius: bubble.radius
      };
    });

    // 重新初始化泡泡，但保持原有的位置和速度
    this.initBubbles();

    // 恢复泡泡的位置和速度
    this.bubbles.forEach((bubble, index) => {
      const oldId = `bubble-${index}`;
      if (bubbleStates[oldId]) {
        bubble.x = bubbleStates[oldId].x;
        bubble.y = bubbleStates[oldId].y;
        bubble.velocityX = bubbleStates[oldId].velocityX;
        bubble.velocityY = bubbleStates[oldId].velocityY;
        bubble.radius = bubbleStates[oldId].radius;
      }
    });
  }

  /**
   * 将十六进制颜色转换为RGBA格式
   * @param {string} hex - 十六进制颜色
   * @param {number} alpha - 透明度
   * @returns {string} - RGBA格式的颜色
   */
  hexToRgba(hex, alpha) {
    // 移除可能的 # 前缀
    hex = hex.replace('#', '');

    // 解析RGB值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // 返回RGBA格式
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  /**
   * 绘制光点装饰
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} innerRadius - 内圆半径
   * @param {number} outerRadius - 外圆半径
   * @param {string} color - 基础颜色
   * @param {number} pulseValue - 脉动值
   */
  drawGlowParticles(ctx, x, y, innerRadius, outerRadius, color, pulseValue) {
    // 生成一些随机位置的光点
    const particleCount = 6; // 减少光点数量
    const radiusRange = outerRadius - innerRadius;

    // 保存当前状态
    ctx.save();

    // 设置混合模式为叠加，使光点更亮
    ctx.globalCompositeOperation = 'lighter';

    // 绘制光点
    for (let i = 0; i < particleCount; i++) {
      // 计算光点位置 - 在内外圆之间随机分布，但更靠近泡泡
      const angle = (i / particleCount) * Math.PI * 2 + (Date.now() * 0.001) % (Math.PI * 2);
      const distance = innerRadius + radiusRange * 0.5 * Math.random(); // 减小分布范围
      const particleX = x + Math.cos(angle) * distance;
      const particleY = y + Math.sin(angle) * distance;

      // 光点大小随脉动变化，整体减小
      const particleSize = 1.5 + 2 * pulseValue * Math.random();

      // 光点透明度随脉动变化
      const particleAlpha = 0.3 * pulseValue * (1 - distance / outerRadius);

      // 创建光点渐变
      const particleGradient = ctx.createRadialGradient(
        particleX, particleY, 0,
        particleX, particleY, particleSize
      );

      // 设置光点颜色
      particleGradient.addColorStop(0, this.hexToRgba('#FFFFFF', particleAlpha));
      particleGradient.addColorStop(0.5, this.hexToRgba(color, particleAlpha * 0.5));
      particleGradient.addColorStop(1, this.hexToRgba(color, 0));

      // 绘制光点
      ctx.beginPath();
      ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
      ctx.fillStyle = particleGradient;
      ctx.fill();
    }

    // 恢复状态
    ctx.restore();
  }

  /**
   * 获取默认主题数据
   * @returns {Array} 默认主题数组
   */
  getDefaultThemes() {
    return [
      {
        id: '101',
        name: '平台介绍',
        englishName: 'Intro',
        description: '了解AIBUBB平台的核心功能和使用技巧',
        color: '#3B82F6'
      },
      {
        id: '102',
        name: '泡泡功能',
        englishName: 'Bubbles',
        description: '探索交互式泡泡的使用方法',
        color: '#84CC16'
      },
      {
        id: '103',
        name: '广场探索',
        englishName: 'Square',
        description: '发现社区中的精彩内容和讨论',
        color: '#0EA5E9'
      },
      {
        id: '104',
        name: '学习计划',
        englishName: 'Plans',
        description: '制定和管理个性化学习计划',
        color: '#F59E0B'
      },
      {
        id: '105',
        name: '笔记技巧',
        englishName: 'Notes',
        description: '掌握高效记录和组织学习笔记的方法',
        color: '#8B5CF6'
      }
    ];
  }

  /**
   * 重置交互状态
   */
  resetInteractionState() {
    this.resetClickState();
  }
}

module.exports = BubbleCanvas;