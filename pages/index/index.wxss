/**index.wxss**/
/* 全局CSS变量 */
page {
  --tabbar-height: 100rpx; /* 底部标签栏高度 */
}

/* 整体容器 */
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  padding-bottom: 0; /* 移除底部padding，使用系统tabBar */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  /* 根据模式设置背景色 */
  background-color: transparent;
}

/* 星星模式下的容器背景 */
page {
  background-color: transparent; /* 默认透明背景 */
}

/* 深色模式下的页面背景 */
page[data-theme="dark"] {
  background-color: #0f0921; /* 深紫色背景 */
}

/* 星星模式下的容器背景 */
.container.dark-mode {
  background-color: transparent; /* 透明背景，使用页面的背景色 */
}

/* 鼠标指针样式 */
.cursor-pointer {
  cursor: pointer;
}

/* 气泡画布样式 */
.bubble-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: block;
  background-color: transparent;
}

/* 星星画布样式 */
.star-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1; /* 降低z-index，确保在导航栏之下 */
  display: block;
  background-color: var(--star-bg-color); /* 使用CSS变量 */
}

/* 星星界面的深色模式 */
.star-canvas.dark-mode {
  background-color: var(--star-bg-color); /* 使用CSS变量 */
}

/* 加载指示器 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
  background-color: transparent;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.1);
  border-top-color: #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 30rpx;
  color: #333;
  transition: color 0.3s;
  margin-bottom: 20rpx;
}

/* 深色模式下的加载文字颜色 */
.loading-container.dark-loading .loading-text {
  color: #ffffff;
}

/* 进度条样式 */
.progress-bar-container {
  width: 300rpx;
  height: 10rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 5rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #2563eb;
  border-radius: 5rpx;
  transition: width 0.3s ease;
}

/* 加载失败样式 */
.error-container {
  background-color: transparent;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 50rpx;
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.error-text {
  color: #ef4444;
}

.retry-button {
  margin-top: 20rpx;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 30rpx;
  font-size: 28rpx;
}

/* 主题模态窗口 */
.theme-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 20%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  background-color: rgba(0, 0, 0, 0.7);
}

.theme-modal.visible {
  opacity: 1;
  visibility: visible;
}

.theme-modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.theme-header {
  padding: 30rpx;
  position: relative;
  background-color: #3b82f6;
  color: white;
}

.theme-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10rpx;
}

.theme-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 40rpx;
  color: white;
  cursor: pointer;
}

.theme-description {
  padding: 30rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.theme-button {
  margin: 0 30rpx 30rpx 30rpx;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
}

/* 引导覆盖层 */
.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.guide-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.guide-content {
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.guide-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  color: #333;
}

.guide-text {
  font-size: 28rpx;
  line-height: 1.6;
  text-align: center;
  margin-bottom: 40rpx;
  color: #666;
}

.guide-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  width: 60%;
}

/* 动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 页面标题 */
.title-container {
  margin-top: 120px;
  text-align: center;
}

.title {
  font-size: 36px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.subtitle {
  margin-top: 10px;
  font-size: 18px;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}



/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.modal-overlay.show {
  opacity: 1;
  pointer-events: auto;
}

.modal-overlay.hide {
  opacity: 0;
  pointer-events: none;
}

.modal-content {
  width: 80%;
  max-width: 320px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.modal-header {
  padding: 20px;
  background-color: #4e7ae6;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  margin-bottom: 4px;
}

.modal-subtitle {
  font-size: 14px;
  color: rgba(255,255,255,0.8);
}

.modal-body {
  padding: 20px;
}

.modal-description {
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
}

.action-button {
  flex: 1;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 500;
  margin: 0 5px;
}

.action-button.practice {
  background-color: #4e7ae6;
  color: white;
}

.action-button.close {
  background-color: #f1f2f6;
  color: #666;
}



/* 底部菜单栏样式已移除 */