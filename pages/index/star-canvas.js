// star-canvas.js
// 星星画布管理（暗黑模式）

// 动画和渲染常量
const ANIMATION_SPEED_MULTIPLIER = 0.064; // 动画速度乘数
const ANIMATION_BASE_SPEED = 0.64; // 基础速度
const SHADOW_BLUR = 4; // 阴影模糊度
const STAR_POINTS = 5; // 星星的角数
const STAR_INNER_RADIUS_RATIO = 0.6; // 内半径与外半径的比例（调大为0.6，让星星更胖）
const CORNER_RADIUS = 15; // 星星角的圆角半径

/**
 * 星星画布管理类
 */
class StarCanvas {
  constructor(page) {
    this.page = page;
    this.canvas = null;
    this.ctx = null;
    this.canvasWidth = 0;
    this.canvasHeight = 0;
    this.stars = [];
    this.lastTimestamp = 0;
    this.animationTimer = null;
    this.navHeight = 0;

    // 拖动相关属性
    this.draggedStar = null;
    this.dragStartX = 0;
    this.dragStartY = 0;
    this.starStartX = 0;
    this.starStartY = 0;
    this.isDragging = false;
    this.dragStartTime = 0;
    this.dragDistance = 0;

    // 动画相关属性
    this.isAnimating = false;
    this.animationRetryCount = 0;
    this.animationRetryTimer = null;
  }

  /**
   * 初始化画布
   * @returns {boolean} 是否初始化成功
   */
  init() {
    try {
      // 防止重复初始化
      if (this.ctx) {
        console.log('画布已初始化，跳过重复初始化');
        return true;
      }

      const query = wx.createSelectorQuery().in(this.page);

      // 添加查询超时处理
      const queryTimeout = setTimeout(() => {
        console.error('获取画布节点超时');
        return false;
      }, 5000);

      query.select('#star-canvas')
        .fields({ node: true, size: true })
        .exec(res => {
          clearTimeout(queryTimeout);

          if (!res || !res[0] || !res[0].node) {
            console.error('获取画布节点失败', res);
            this.page.setData({ initialized: false, loadingFailed: true });
            return false;
          }

          try {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            if (!ctx) {
              console.error('获取2d上下文失败');
              this.page.setData({ initialized: false, loadingFailed: true });
              return false;
            }

            this.canvas = canvas;
            this.ctx = ctx;

            // 设置画布尺寸，适配不同设备像素比
            const dpr = this.getDevicePixelRatio();
            canvas.width = res[0].width * dpr;
            canvas.height = res[0].height * dpr;
            ctx.scale(dpr, dpr);

            this.canvasWidth = res[0].width;
            this.canvasHeight = res[0].height;

            // 使用系统导航栏，不需要额外计算导航栏高度
            this.navHeight = 0; // 设置为0，让星星从顶部开始显示

            console.log('星星画布初始化完成', {
              width: this.canvasWidth,
              height: this.canvasHeight,
              navHeight: this.navHeight,
              dpr: dpr
            });

            // 初始化星星
            this.initStars();

            // 开始动画循环
            this.startAnimation();

            // 初始化触摸事件
            this.initTouchEvents();

            this.page.setData({ initialized: true });

            return true;
          } catch (err) {
            console.error('初始化Canvas上下文时出错', err);
            this.page.setData({ initialized: false, loadingFailed: true });
            return false;
          }
        });

      return true; // 返回true表示查询已启动
    } catch (err) {
      console.error('创建选择器时出错', err);
      this.page.setData({ initialized: false, loadingFailed: true });
      return false;
    }
  }

  /**
   * 获取设备像素比，带错误处理
   * @returns {number} 设备像素比
   */
  getDevicePixelRatio() {
    try {
      return wx.getSystemInfoSync().pixelRatio || 1;
    } catch (err) {
      console.error('获取设备像素比失败', err);
      return 1; // 默认值
    }
  }

  /**
   * 获取状态栏高度，带错误处理
   * @returns {number} 状态栏高度
   */
  getStatusBarHeight() {
    try {
      const windowInfo = wx.getWindowInfo();
      return windowInfo.statusBarHeight || 20;
    } catch (err) {
      console.error('获取状态栏高度失败', err);
      return 20; // 默认值
    }
  }

  /**
   * 初始化星星
   */
  initStars() {
    // 清空现有星星
    this.stars = [];

    // 尝试多种方式获取主题数据
    let starThemes = [];

    // 1. 首先尝试从页面获取主题
    if (this.page.getBubbleThemes && typeof this.page.getBubbleThemes === 'function') {
      starThemes = this.page.getBubbleThemes();
    }

    // 2. 如果页面方法没有返回数据，尝试从ThemeManager获取
    if ((!Array.isArray(starThemes) || starThemes.length === 0) &&
        this.page.themeManager && this.page.themeManager.getCurrentThemes) {
      starThemes = this.page.themeManager.getCurrentThemes();
    }

    // 3. 如果仍然没有数据，使用默认主题
    if (!Array.isArray(starThemes) || starThemes.length === 0) {
      starThemes = this.getDefaultThemes();
    }

    console.log('StarCanvas - 初始化星星，获取到的主题数量:', starThemes.length);

    if (Array.isArray(starThemes) && starThemes.length > 0) {
      console.log('StarCanvas - 星星主题内容:', JSON.stringify(starThemes));
      this.createStarsFromThemes(starThemes);
    } else {
      console.error('StarCanvas - 无法获取有效的主题数据');
    }
  }

  /**
   * 根据主题创建星星
   * @param {Array} themes - 主题数组
   */
  createStarsFromThemes(themes) {
    const stars = [];
    const count = themes.length; // 星星数量与主题数量一致

    if (count === 0) {
      console.warn('StarCanvas - 警告: 没有星星主题数据，将不会创建星星');
      this.stars = stars;
      return;
    }

    // 计算底部菜单的上边界位置和顶部导航的下边界位置
    const tabBarHeight = 50; // 微信小程序默认tabBar高度约为50px
    const tabbarTop = this.canvasHeight - tabBarHeight; // 底部菜单上边界，减去tabBar高度
    const navBottom = 0; // 顶部导航下边界设为0，让星星从顶部开始显示

    // 为了确保星星均匀分布，使用网格布局
    const gridColumns = 3; // 列数
    const gridRows = Math.ceil(count / gridColumns); // 行数

    const cellWidth = this.canvasWidth / gridColumns;
    const cellHeight = (tabbarTop - navBottom) / gridRows;

    for (let i = 0; i < count; i++) {
      const theme = themes[i % themes.length];

      // 基础半径范围为40-60（更大一些，接近x.js中的效果）
      let baseRadius = 40 + Math.random() * 20;

      // 根据文字长度动态调整星星大小
      const textLength = theme.name.length;
      if (textLength > 3) {
        // 当文字长度超过3个字符时，增加星星半径
        baseRadius += baseRadius * (textLength - 3) * 0.05;

        // 如果文字需要换行显示，额外增加星星大小
        if (textLength >= 4) {
          const lineCount = Math.ceil(textLength / 2);
          if (lineCount > 1) {
            baseRadius += baseRadius * 0.1 * (lineCount - 1);
          }
        }
      }

      const radius = baseRadius; // 使用调整后的半径

      // 计算在网格中的位置
      const col = i % gridColumns;
      const row = Math.floor(i / gridColumns);

      // 计算星星的初始位置，偏移网格中心位置以获得随机效果
      const offsetX = (Math.random() - 0.5) * cellWidth * 0.6;
      const offsetY = (Math.random() - 0.5) * cellHeight * 0.6;

      const x = cellWidth * (col + 0.5) + offsetX;
      const y = navBottom + cellHeight * (row + 0.5) + offsetY;

      // 获取随机的柔和色彩
      const hue = Math.floor(Math.random() * 360);
      const pastelColor = theme.color || `hsl(${hue}, 100%, 75%)`;

      stars.push({
        id: `star-${i}`,
        text: theme.name, // 只保留主标签
        color: pastelColor,
        x,
        y,
        radius,
        velocityX: (Math.random() - 0.5) * ANIMATION_BASE_SPEED,
        velocityY: (Math.random() - 0.5) * ANIMATION_BASE_SPEED,
        isHovered: false,
        isClicked: false,
        pulsePhase: Math.random() * Math.PI * 2,
        pulseSpeed: 0.001 + Math.random() * 0.002,
        rotationAngle: 0, // 固定旋转角度为0
        rotationSpeed: 0, // 旋转速度设为0，取消旋转
        twinklePhase: Math.random() * Math.PI * 2, // 闪烁相位
        twinkleSpeed: 0.002 + Math.random() * 0.002, // 闪烁速度
        lastX: x,
        lastY: y,
        stuckFrames: 0,
        cornerRadius: Math.random() * 10 + 15, // 圆角半径，更大的圆角
        // 相同的其他属性
        collisionCount: 0,
        isAccelerated: false,
        originalVelocityX: 0,
        originalVelocityY: 0,
        isDragged: false
      });
    }

    this.stars = stars;
    console.log('星星初始化完成，数量:', stars.length);
  }

  /**
   * 开始动画循环
   */
  startAnimation() {
    // 先停止可能存在的动画
    this.stopAnimation();

    // 检查canvas和ctx是否已初始化
    if (!this.canvas || !this.ctx) {
      console.error('Canvas或Context未初始化，无法启动动画');
      // 延迟重试
      this.animationRetryTimer = setTimeout(() => {
        if (this.canvas && this.ctx) {
          console.log('延迟后尝试启动动画');
          this.startAnimation();
        } else {
          console.error('Canvas仍未初始化，请检查初始化过程');
          this.page.setData({ initialized: false });
        }
      }, 1000);
      return;
    }

    // 初始化时间戳
    this.lastTimestamp = Date.now();
    this.isAnimating = true;

    // 定义动画循环函数
    const animate = () => {
      // 如果已经停止动画，则不继续
      if (!this.isAnimating) {
        return;
      }

      try {
        // 确保canvas和ctx仍然有效
        if (!this.canvas || !this.ctx) {
          console.error('Canvas或Context已失效，停止动画');
          this.stopAnimation();
          return;
        }

        // 获取当前时间戳并计算时间增量
        const now = Date.now();
        const deltaTime = now - this.lastTimestamp;
        this.lastTimestamp = now;

        // 更新和绘制星星
        this.updateStars(deltaTime);
        this.drawStars();

        // 继续动画循环
        try {
          this.animationTimer = this.canvas.requestAnimationFrame(animate);
        } catch (err) {
          console.error('请求动画帧失败，使用setTimeout替代', err);
          // 使用setTimeout作为备选方案
          this.animationTimer = setTimeout(() => {
            animate();
          }, 16); // 约60fps
        }
      } catch (err) {
        console.error('动画循环出错', err);
        // 如果出错，延迟后重试，但最多重试3次
        if (!this.animationRetryCount) {
          this.animationRetryCount = 0;
        }
        this.animationRetryCount++;

        if (this.animationRetryCount <= 3) {
          this.animationRetryTimer = setTimeout(() => {
            console.log(`动画循环第${this.animationRetryCount}次重试`);
            this.startAnimation();
          }, 1000);
        } else {
          console.error('动画循环多次失败，停止重试');
          this.page.setData({ initialized: false, loadingFailed: true });
        }
      }
    };

    // 启动动画循环
    try {
      this.animationTimer = this.canvas.requestAnimationFrame(animate);
      this.animationRetryCount = 0; // 重置重试计数
      console.log('动画循环已启动');
    } catch (err) {
      console.error('启动动画失败，使用setTimeout替代', err);
      // 使用setTimeout作为备选方案
      this.animationTimer = setTimeout(animate, 16);
    }
  }

  /**
   * 更新星星位置
   * @param {number} deltaTime - 时间增量
   */
  updateStars(deltaTime) {
    // 参数验证
    if (!deltaTime || isNaN(deltaTime) || deltaTime < 0) {
      deltaTime = 16; // 默认约60fps
    }

    // 限制最大时间增量，避免大的时间跳跃
    const maxDeltaTime = 50;
    const clampedDeltaTime = Math.min(deltaTime, maxDeltaTime);

    // 性能优化：预先计算常量
    const speedMultiplier = clampedDeltaTime * ANIMATION_SPEED_MULTIPLIER;

    // 计算底部菜单的上边界位置和顶部导航的下边界位置
    const tabBarHeight = 50; // 微信小程序默认tabBar高度约为50px
    const tabbarTop = this.canvasHeight - tabBarHeight; // 底部菜单上边界，减去tabBar高度
    const navBottom = 0; // 顶部导航下边界设为0，让星星从顶部开始显示
    const canvasWidth = this.canvasWidth;

    // 更新星星位置
    for (let i = 0; i < this.stars.length; i++) {
      const star = this.stars[i];

      // 如果星星被点击或正在被拖动，则不更新位置
      if (star.isClicked || star.isDragged) continue;

      // 应用速度
      star.x += star.velocityX * speedMultiplier;
      star.y += star.velocityY * speedMultiplier;

      // 脉动效果
      star.pulsePhase += star.pulseSpeed * clampedDeltaTime;
      if (star.pulsePhase > Math.PI * 2) {
        star.pulsePhase -= Math.PI * 2;
      }

      // 碰撞检测 - 左右边界
      let hasCollided = false;
      const starRadius = star.radius;

      if (star.x - starRadius < 0) {
        star.x = starRadius;
        star.velocityX = Math.abs(star.velocityX) * 0.8; // 减少反弹速度
        hasCollided = true;
      } else if (star.x + starRadius > canvasWidth) {
        star.x = canvasWidth - starRadius;
        star.velocityX = -Math.abs(star.velocityX) * 0.8; // 减少反弹速度
        hasCollided = true;
      }

      // 碰撞检测 - 顶部导航下边界和底部菜单上边界
      if (star.y - starRadius < navBottom) {
        star.y = navBottom + starRadius;
        star.velocityY = Math.abs(star.velocityY) * 0.8; // 减少反弹速度
        hasCollided = true;
      } else if (star.y + starRadius > tabbarTop) {
        star.y = tabbarTop - starRadius;
        star.velocityY = -Math.abs(star.velocityY) * 0.8; // 减少反弹速度
        hasCollided = true;
      }

      // 如果发生碰撞且星星处于加速状态，增加碰撞计数
      if (hasCollided && star.isAccelerated) {
        star.collisionCount++;

        // 如果碰撞次数达到3次，恢复正常速度
        if (star.collisionCount >= 3) {
          // 恢复到原始速度
          if (star.originalVelocityX !== 0 || star.originalVelocityY !== 0) {
            star.velocityX = star.originalVelocityX;
            star.velocityY = star.originalVelocityY;
          } else {
            // 如果没有原始速度记录，生成一个新的随机速度
            const angle = Math.random() * Math.PI * 2;
            star.velocityX = Math.cos(angle) * ANIMATION_BASE_SPEED * 0.8;
            star.velocityY = Math.sin(angle) * ANIMATION_BASE_SPEED * 0.8;
          }

          // 重置加速状态和碰撞计数
          star.isAccelerated = false;
          star.collisionCount = 0;
          star.originalVelocityX = 0;
          star.originalVelocityY = 0;
        }
      }

      // 检测星星是否卡住
      if (Math.abs(star.x - star.lastX) < 0.1 && Math.abs(star.y - star.lastY) < 0.1) {
        star.stuckFrames++;
        if (star.stuckFrames > 5) {
          // 随机改变速度方向，防止卡住
          const angle = Math.random() * Math.PI * 2;
          const speed = ANIMATION_BASE_SPEED * (0.5 + Math.random() * 0.5);
          star.velocityX = Math.cos(angle) * speed;
          star.velocityY = Math.sin(angle) * speed;
          star.stuckFrames = 0;
        }
      } else {
        star.stuckFrames = 0;
      }

      // 随机添加一些微小的加速度变化，使运动更自然
      // 性能优化：降低随机计算的频率
      if (Math.random() < 0.02) { // 从0.05降低到0.02
        star.velocityX += (Math.random() - 0.5) * 0.01;
        star.velocityY += (Math.random() - 0.5) * 0.01;
      }

      // 限制最大速度
      const maxSpeed = ANIMATION_BASE_SPEED * 1.5;
      const currentSpeed = Math.sqrt(star.velocityX * star.velocityX + star.velocityY * star.velocityY);
      if (currentSpeed > maxSpeed) {
        const speedScale = maxSpeed / currentSpeed;
        star.velocityX *= speedScale;
        star.velocityY *= speedScale;
      }

      // 记录上一帧位置
      star.lastX = star.x;
      star.lastY = star.y;
    }
  }

  /**
   * 绘制星星
   */
  drawStars() {
    const ctx = this.ctx;
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

    // 创建星空渐变背景 - 类似泡泡组件的渐变效果，但使用深色调
    // 渐变从画布顶部开始，而不是从navHeight开始
    const gradient = ctx.createLinearGradient(0, 0, this.canvasWidth, this.canvasHeight);

    // 设置星空渐变色 - 从深蓝色到深紫色
    gradient.addColorStop(0, 'rgb(10, 10, 40)'); // 深蓝色
    gradient.addColorStop(0.5, 'rgb(20, 10, 50)'); // 深蓝紫色
    gradient.addColorStop(1, 'rgb(30, 10, 60)'); // 深紫色

    // 填充整个画布区域
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

    // 添加星空点缀效果 - 绘制小星星
    this.drawStarryBackground(ctx);

    // 创建一个渲染队列，让交互中的星星在最上层
    const normalStars = [];
    const hoveredStars = [];
    const draggedStars = [];
    const clickedStars = [];

    // 将星星分配到不同的渲染队列
    for (let i = 0; i < this.stars.length; i++) {
      const star = this.stars[i];

      if (star.isClicked) {
        clickedStars.push(star);
      } else if (star.isDragged) {
        draggedStars.push(star);
      } else if (star.isHovered) {
        hoveredStars.push(star);
      } else {
        normalStars.push(star);
      }
    }

    // 按顺序渲染：普通 -> 悬停 -> 拖动 -> 点击
    this.renderStarsList(ctx, normalStars);
    this.renderStarsList(ctx, hoveredStars);
    this.renderStarsList(ctx, draggedStars);
    this.renderStarsList(ctx, clickedStars);
  }

  /**
   * 渲染星星列表
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {Array} starsList - 要渲染的星星列表
   */
  renderStarsList(ctx, starsList) {
    // 按照大小排序，小的星星先绘制
    starsList.sort((a, b) => a.radius - b.radius);

    // 渲染每个星星
    for (let i = 0; i < starsList.length; i++) {
      const star = starsList[i];

      // 呼吸效果 - 脉动半径变化
      const pulseScale = 1 + Math.sin(star.pulsePhase) * 0.03;
      const scaleMultiplier = star.isHovered ? 1.15 : pulseScale;
      const actualRadius = star.radius * scaleMultiplier;

      // 使用固定不透明度
      ctx.globalAlpha = 1.0; // 完全不透明

      // 绘制星星
      this.drawStar(
        ctx,
        star.x,
        star.y,
        actualRadius,
        STAR_POINTS,
        STAR_INNER_RADIUS_RATIO,
        0, // 固定旋转角度为0
        star.color,
        star.isClicked,
        star.isDragged
      );

      // 绘制星星文字
      this.drawStarText(ctx, star, actualRadius);
    }
  }

  /**
   * 绘制星星文字
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {Object} star - 星星对象
   * @param {number} actualRadius - 实际半径
   */
  drawStarText(ctx, star, actualRadius) {
    // 保存当前状态
    ctx.save();

    // 获取文字长度
    const textLength = star.text.length;

    // 动态调整字体大小
    let fontSizeMultiplier = 1;

    // 当文字长度超过3个字符时，开始缩小字体
    if (textLength > 3) {
      fontSizeMultiplier = 1 - (textLength - 3) * 0.1; // 每多1个字符，字体缩小10%
      fontSizeMultiplier = Math.max(fontSizeMultiplier, 0.7); // 最小缩小到70%
    }

    const fontSizeMain = actualRadius * 0.45 * fontSizeMultiplier;

    // 设置文字阴影
    ctx.shadowColor = 'rgba(0,0,0,0.5)';
    ctx.shadowBlur = 3;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    // 绘制中文名称
    ctx.font = `bold ${fontSizeMain}px sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // 根据背景颜色的亮度选择文字颜色
    const isLight = this.isLightColor(star.color);
    ctx.fillStyle = isLight ? '#000000' : '#FFFFFF';

    // 检查文字长度，四个字及以上时考虑换行处理
    if (textLength >= 4) {
      // 文字换行处理 - 按照2个字一行进行均匀分割
      const lines = [];
      for (let i = 0; i < textLength; i += 2) {
        lines.push(star.text.substr(i, 2));
      }

      // 绘制多行文字
      const lineCount = lines.length;
      // 调整行间距系数为1.1，保持适中的间距
      const lineSpacing = fontSizeMain * 1.1;

      for (let i = 0; i < lineCount; i++) {
        // 计算每行的垂直位置，使整体垂直居中
        const lineY = star.y - ((lineCount - 1) * lineSpacing / 2) + i * lineSpacing;
        ctx.fillText(lines[i], star.x, lineY);
      }
    } else {
      // 对于3个字及以下的情况，保持原来的单行显示
      ctx.fillText(star.text, star.x, star.y);
    }

    // 恢复状态
    ctx.restore();
  }

  /**
   * 绘制星星形状
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} outerRadius - 外圆半径
   * @param {number} points - 星星角数
   * @param {number} innerRadiusRatio - 内半径与外半径的比例
   * @param {number} rotation - 旋转角度
   * @param {string} color - 星星颜色
   * @param {boolean} isClicked - 是否被点击
   * @param {boolean} isDragged - 是否被拖动
   */
  drawStar(ctx, x, y, outerRadius, points, innerRadiusRatio, rotation, color, isClicked, isDragged) {
    // 参数验证
    if (!ctx || typeof outerRadius !== 'number' || outerRadius <= 0) {
      console.error('drawStar: 参数无效', { ctx, outerRadius });
      return;
    }

    try {
      // 保存当前状态
      ctx.save();
      ctx.translate(x, y);

      // 旋转角度固定为0，不再执行旋转
      ctx.beginPath();

      // 内半径
      const innerRadius = outerRadius * innerRadiusRatio;

      // 计算星星顶点
      const vertices = [];
      const angleIncrement = (Math.PI * 2) / (points * 2);

      for (let i = 0; i < points * 2; i++) {
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const angle = i * angleIncrement - Math.PI / 2; // 从上方开始
        vertices.push({
          x: radius * Math.cos(angle),
          y: radius * Math.sin(angle)
        });
      }

      // 绘制带圆角的星星路径
      const cornerRadius = Math.min(CORNER_RADIUS, outerRadius * 0.2); // 圆角不超过半径的20%

      for (let i = 0; i < points * 2; i++) {
        const p1 = vertices[i];
        const p2 = vertices[(i + 1) % (points * 2)];
        const p3 = vertices[(i + 2) % (points * 2)];

        // 计算每条边的长度
        const edge1Length = Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
        const edge2Length = Math.sqrt(Math.pow(p2.x - p3.x, 2) + Math.pow(p2.y - p3.y, 2));

        // 圆角距离不超过边长的一半
        const d1 = Math.min(cornerRadius, edge1Length / 2.5);
        const d2 = Math.min(cornerRadius, edge2Length / 2.5);

        // 防止除0错误
        if (edge1Length < 0.001 || edge2Length < 0.001) {
          if (i === 0) {
            ctx.moveTo(p1.x, p1.y);
          } else {
            ctx.lineTo(p1.x, p1.y);
          }
          continue;
        }

        // 计算方向向量
        const dir1 = {
          x: (p2.x - p1.x) / edge1Length,
          y: (p2.y - p1.y) / edge1Length
        };
        const dir2 = {
          x: (p2.x - p3.x) / edge2Length,
          y: (p2.y - p3.y) / edge2Length
        };

        // 计算控制点
        const cp1 = {
          x: p2.x - dir1.x * d1,
          y: p2.y - dir1.y * d1
        };
        const cp2 = {
          x: p2.x - dir2.x * d2,
          y: p2.y - dir2.y * d2
        };

        // 绘制线段和曲线
        if (i === 0) {
          ctx.moveTo(p1.x, p1.y);
        }

        ctx.lineTo(cp1.x, cp1.y);
        ctx.quadraticCurveTo(p2.x, p2.y, cp2.x, cp2.y);
      }

      // 闭合路径
      ctx.closePath();

      // 创建渐变
      const gradient = ctx.createRadialGradient(
        -outerRadius * 0.3, -outerRadius * 0.3, 0, // 内部更亮的点，从左上角偏移
        0, 0, outerRadius // 外部
      );

      // 设置渐变颜色
      try {
        const baseColor = color || '#4f46e5'; // 默认颜色，避免空值
        const lighterColor = this.lightenColor(baseColor, 40); // 更亮
        const mediumColor = this.lightenColor(baseColor, 15); // 中间亮度
        const darkerColor = this.darkenColor(baseColor, 10); // 更暗

        gradient.addColorStop(0, lighterColor); // 中心最亮
        gradient.addColorStop(0.4, mediumColor); // 中间过渡色
        gradient.addColorStop(0.7, baseColor); // 基础颜色
        gradient.addColorStop(1, darkerColor); // 边缘较暗

        // 设置阴影
        ctx.shadowColor = this.hexToRgba(baseColor, 0.6);
        ctx.shadowBlur = SHADOW_BLUR + 2;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // 填充星星
        ctx.fillStyle = gradient;
        ctx.fill();

        // 如果星星被点击，绘制高亮边框
        if (isClicked) {
          ctx.strokeStyle = '#FFFFFF';
          ctx.lineWidth = 2;
          ctx.stroke();
        }

        // 如果星星正在被拖动，添加光环效果（类似泡泡的效果）
        if (isDragged) {
          this.drawStarGlowEffects(ctx, outerRadius, baseColor);
        }
      } catch (err) {
        console.error('创建渐变或填充时出错', err);
        // 出错时使用简单填充
        ctx.fillStyle = color || '#4f46e5';
        ctx.fill();
      }

      // 恢复上下文状态
      ctx.restore();
    } catch (err) {
      console.error('绘制星星时出错', err);
      // 错误恢复：绘制一个简单的圆形代替
      this.drawFallbackStar(ctx, x, y, outerRadius, color);
    }
  }

  /**
   * 绘制星星的发光效果
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} outerRadius - 外圆半径
   * @param {string} baseColor - 基础颜色
   */
  drawStarGlowEffects(ctx, outerRadius, baseColor) {
    try {
      // 重置阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;

      // 计算脉动效果
      const now = Date.now();
      const pulseValue = Math.sin(now * 0.005) * 0.2 + 0.8; // 0.6-1.0之间脉动

      // 创建外部光圈渐变
      const outerGlowRadius = outerRadius * (1.05 + pulseValue * 0.14); // 光圈大小随脉动变化
      const glowGradient = ctx.createRadialGradient(
        0, 0, outerRadius,
        0, 0, outerGlowRadius
      );

      // 设置光圈颜色
      glowGradient.addColorStop(0, this.hexToRgba(baseColor, 0.5 * pulseValue));
      glowGradient.addColorStop(0.5, this.hexToRgba(baseColor, 0.2 * pulseValue));
      glowGradient.addColorStop(1, this.hexToRgba(baseColor, 0));

      // 绘制光圈
      ctx.beginPath();
      ctx.arc(0, 0, outerGlowRadius, 0, Math.PI * 2);
      ctx.fillStyle = glowGradient;
      ctx.fill();

      // 绘制内部脉动光环
      const innerGlowGradient = ctx.createRadialGradient(
        0, 0, outerRadius * 0.8,
        0, 0, outerRadius
      );

      // 设置内部光环颜色
      const innerAlpha = 0.6 * pulseValue;
      innerGlowGradient.addColorStop(0, this.hexToRgba('#FFFFFF', 0));
      innerGlowGradient.addColorStop(0.8, this.hexToRgba('#FFFFFF', innerAlpha * 0.3));
      innerGlowGradient.addColorStop(1, this.hexToRgba('#FFFFFF', innerAlpha));

      // 绘制内部光环
      ctx.beginPath();
      ctx.arc(0, 0, outerRadius, 0, Math.PI * 2);
      ctx.fillStyle = innerGlowGradient;
      ctx.fill();

      // 添加外部光点装饰，与泡泡效果保持一致
      this.drawGlowParticles(ctx, 0, 0, outerRadius, outerGlowRadius, baseColor, pulseValue);
    } catch (err) {
      console.error('绘制发光效果时出错', err);
      // 出错时不绘制发光效果
    }
  }

  /**
   * 在绘制失败时绘制备用星星形状（简单圆形）
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} radius - 半径
   * @param {string} color - 颜色
   */
  drawFallbackStar(ctx, x, y, radius, color) {
    try {
      ctx.save();
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fillStyle = color || '#4f46e5';
      ctx.fill();
      ctx.restore();
    } catch (err) {
      console.error('绘制备用星星形状也失败', err);
    }
  }

  /**
   * 使颜色变亮
   * @param {string} color - 十六进制颜色
   * @param {number} percent - 变亮百分比
   * @returns {string} - 新颜色
   */
  lightenColor(color, percent) {
    try {
      if (!color || typeof color !== 'string') {
        return '#FFFFFF'; // 默认返回白色
      }

      // 如果颜色不是十六进制格式，尝试转换
      let hexColor = color;
      if (!hexColor.startsWith('#')) {
        hexColor = this.colorToHex(color);
      }

      const num = parseInt(hexColor.replace('#', ''), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) + amt;
      const G = (num >> 8 & 0x00FF) + amt;
      const B = (num & 0x0000FF) + amt;

      return '#' + (
        0x1000000 +
        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
        (B < 255 ? (B < 1 ? 0 : B) : 255)
      ).toString(16).slice(1);
    } catch (err) {
      console.error('lightenColor 处理失败', err);
      return color || '#FFFFFF';
    }
  }

  /**
   * 使颜色变暗
   * @param {string} color - 十六进制颜色
   * @param {number} percent - 变暗百分比
   * @returns {string} - 新颜色
   */
  darkenColor(color, percent) {
    try {
      if (!color || typeof color !== 'string') {
        return '#000000'; // 默认返回黑色
      }

      // 如果颜色不是十六进制格式，尝试转换
      let hexColor = color;
      if (!hexColor.startsWith('#')) {
        hexColor = this.colorToHex(color);
      }

      const num = parseInt(hexColor.replace('#', ''), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) - amt;
      const G = (num >> 8 & 0x00FF) - amt;
      const B = (num & 0x0000FF) - amt;

      return '#' + (
        0x1000000 +
        (R > 0 ? R : 0) * 0x10000 +
        (G > 0 ? G : 0) * 0x100 +
        (B > 0 ? B : 0)
      ).toString(16).slice(1);
    } catch (err) {
      console.error('darkenColor 处理失败', err);
      return color || '#000000';
    }
  }

  /**
   * 将十六进制颜色转换为RGBA格式
   * @param {string} hex - 十六进制颜色
   * @param {number} alpha - 透明度
   * @returns {string} - RGBA格式的颜色
   */
  hexToRgba(hex, alpha) {
    try {
      // 确保有有效的颜色值和透明度
      if (!hex || typeof hex !== 'string') {
        return `rgba(0, 0, 0, ${alpha || 0})`;
      }

      // 如果颜色不是十六进制格式，尝试转换
      let hexColor = hex;
      if (!hexColor.startsWith('#')) {
        hexColor = this.colorToHex(hex);
      }

      // 移除可能的 # 前缀
      hexColor = hexColor.replace('#', '');

      // 确保有6位颜色值
      if (hexColor.length === 3) {
        hexColor = hexColor[0] + hexColor[0] + hexColor[1] + hexColor[1] + hexColor[2] + hexColor[2];
      }

      if (hexColor.length !== 6) {
        return `rgba(0, 0, 0, ${alpha || 0})`;
      }

      // 解析RGB值
      const r = parseInt(hexColor.substring(0, 2), 16);
      const g = parseInt(hexColor.substring(2, 4), 16);
      const b = parseInt(hexColor.substring(4, 6), 16);

      // 确保透明度在有效范围内
      const validAlpha = Math.max(0, Math.min(1, alpha || 0));

      // 返回RGBA格式
      return `rgba(${r}, ${g}, ${b}, ${validAlpha})`;
    } catch (err) {
      console.error('hexToRgba 处理失败', err);
      return `rgba(0, 0, 0, ${alpha || 0})`;
    }
  }

  /**
   * 常见颜色名称转十六进制
   * @param {string} color - 颜色名称或值
   * @returns {string} - 十六进制颜色
   */
  colorToHex(color) {
    // 常见颜色名称映射表
    const colorNames = {
      'red': '#FF0000',
      'green': '#00FF00',
      'blue': '#0000FF',
      'black': '#000000',
      'white': '#FFFFFF',
      'yellow': '#FFFF00',
      'purple': '#800080',
      'orange': '#FFA500',
      'grey': '#808080',
      'gray': '#808080',
      'cyan': '#00FFFF',
      'magenta': '#FF00FF',
      'pink': '#FFC0CB',
      'brown': '#A52A2A'
    };

    // 如果是颜色名称，直接返回映射值
    if (typeof color === 'string' && colorNames[color.toLowerCase()]) {
      return colorNames[color.toLowerCase()];
    }

    // 如果已经是十六进制格式，确保有#前缀
    if (typeof color === 'string' && /^[0-9A-F]{6}$/i.test(color)) {
      return '#' + color;
    }

    // 如果是简写十六进制
    if (typeof color === 'string' && /^#[0-9A-F]{3}$/i.test(color)) {
      const r = color[1], g = color[2], b = color[3];
      return `#${r}${r}${g}${g}${b}${b}`;
    }

    // 尝试解析RGB格式
    if (typeof color === 'string' && color.startsWith('rgb')) {
      try {
        const rgbMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/);
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0');
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0');
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0');
          return `#${r}${g}${b}`;
        }
      } catch (e) {
        console.error('RGB颜色解析失败', e);
      }
    }

    // 默认返回深蓝色
    return '#4f46e5';
  }

  /**
   * 获取随机柔和颜色
   * @returns {string} - HSL格式的柔和颜色
   */
  getRandomPastelColor() {
    const hue = Math.floor(Math.random() * 360);
    return `hsl(${hue}, 100%, 75%)`;
  }

  /**
   * 检测是否为浅色
   * @param {string} color - 十六进制颜色
   * @returns {boolean} - 是否为浅色
   */
  isLightColor(color) {
    // 预定义的浅色名称列表
    const lightColorNames = ['yellow', 'amber', 'lime', 'brightYellow', 'green', 'cyan'];
    if (typeof color === 'string' && lightColorNames.includes(color.toLowerCase())) {
      return true;
    }

    try {
      // 确保颜色是十六进制格式
      if (!color || typeof color !== 'string') {
        return false;
      }

      // 如果颜色不是十六进制格式，尝试转换
      let hexColor = color;
      if (!hexColor.startsWith('#')) {
        hexColor = this.colorToHex(color);
      }

      // 移除#前缀
      hexColor = hexColor.replace('#', '');

      // 解析RGB值
      const r = parseInt(hexColor.substr(0, 2), 16);
      const g = parseInt(hexColor.substr(2, 2), 16);
      const b = parseInt(hexColor.substr(4, 2), 16);

      // 计算亮度 (基于人眼对不同颜色的感知)
      // 公式: (0.299*R + 0.587*G + 0.114*B)
      const brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      // 亮度大于0.6认为是浅色
      return brightness > 0.6;
    } catch (err) {
      console.error('颜色亮度计算失败', err);
      return false;
    }
  }

  /**
   * 绘制光点装饰
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} innerRadius - 内圆半径
   * @param {number} outerRadius - 外圆半径
   * @param {string} color - 基础颜色
   * @param {number} pulseValue - 脉动值
   */
  drawGlowParticles(ctx, x, y, innerRadius, outerRadius, color, pulseValue) {
    // 生成一些随机位置的光点
    const particleCount = 6; // 减少光点数量
    const radiusRange = outerRadius - innerRadius;

    // 保存当前状态
    ctx.save();

    // 设置混合模式为叠加，使光点更亮
    ctx.globalCompositeOperation = 'lighter';

    // 绘制光点
    for (let i = 0; i < particleCount; i++) {
      // 计算光点位置 - 在内外圆之间随机分布，但更靠近星星
      const angle = (i / particleCount) * Math.PI * 2 + (Date.now() * 0.001) % (Math.PI * 2);
      const distance = innerRadius + radiusRange * 0.5 * Math.random(); // 减小分布范围
      const particleX = x + Math.cos(angle) * distance;
      const particleY = y + Math.sin(angle) * distance;

      // 光点大小随脉动变化，整体减小
      const particleSize = 1.5 + 2 * pulseValue * Math.random();

      // 光点透明度随脉动变化
      const particleAlpha = 0.3 * pulseValue * (1 - distance / outerRadius);

      // 创建光点渐变
      const particleGradient = ctx.createRadialGradient(
        particleX, particleY, 0,
        particleX, particleY, particleSize
      );

      // 设置光点颜色
      particleGradient.addColorStop(0, this.hexToRgba('#FFFFFF', particleAlpha));
      particleGradient.addColorStop(0.5, this.hexToRgba(color, particleAlpha * 0.5));
      particleGradient.addColorStop(1, this.hexToRgba(color, 0));

      // 绘制光点
      ctx.beginPath();
      ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
      ctx.fillStyle = particleGradient;
      ctx.fill();
    }

    // 恢复状态
    ctx.restore();
  }

  /**
   * 初始化触摸事件
   */
  initTouchEvents() {
    console.log('初始化星星触摸事件');
    // 触摸事件通过页面的事件处理函数处理
  }

  /**
   * 处理触摸开始事件
   * @param {object} e - 触摸事件对象
   * @returns {object|null} - 被触摸的星星对象或null
   */
  handleTouchStart(e) {
    // 参数验证
    if (!e || !e.touches || !e.touches[0]) {
      console.error('触摸事件数据无效');
      return null;
    }

    const touch = e.touches[0];
    const { x, y } = touch;

    // 记录触摸开始时间
    this.dragStartTime = Date.now();
    this.dragStartX = x;
    this.dragStartY = y;
    this.isDragging = false;
    this.dragDistance = 0;

    // 检测点击的是哪个星星
    for (const star of this.stars) {
      // 使用更高效的距离计算
      // 先进行外接正方形快速判断，再进行圆形距离判断
      if (Math.abs(x - star.x) <= star.radius && Math.abs(y - star.y) <= star.radius) {
        const distance = Math.sqrt(
          Math.pow(x - star.x, 2) + Math.pow(y - star.y, 2)
        );

        if (distance <= star.radius) {
          // 记录拖动开始的星星和位置
          this.draggedStar = star;
          this.starStartX = star.x;
          this.starStartY = star.y;

          // 标记星星为拖动状态
          star.isDragged = true;

          // 记录原始速度
          star.originalVelocityX = star.velocityX;
          star.originalVelocityY = star.velocityY;

          // 暂停星星动画
          star.velocityX = 0;
          star.velocityY = 0;

          // 不立即返回主题，等待触摸结束时判断是点击还是拖动
          return null;
        }
      }
    }

    return null;
  }

  /**
   * 处理触摸移动事件
   * @param {object} e - 触摸事件对象
   * @returns {string} - 鼠标样式
   */
  handleTouchMove(e) {
    // 参数验证
    if (!e || !e.touches || !e.touches[0]) {
      console.error('触摸移动事件数据无效');
      return 'default';
    }

    const touch = e.touches[0];
    const { x, y } = touch;

    // 如果有正在拖动的星星
    if (this.draggedStar) {
      // 计算拖动距离
      const dragX = x - this.dragStartX;
      const dragY = y - this.dragStartY;
      this.dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

      // 如果拖动距离超过阈值，标记为拖动状态
      if (this.dragDistance > 5) {
        this.isDragging = true;
      }

      // 获取星星参数
      const star = this.draggedStar;
      const starRadius = star.radius;

      // 碰撞边界检测 - 确保星星不会拖出屏幕边界
      let newX = this.starStartX + dragX;
      let newY = this.starStartY + dragY;

      // 左右边界
      if (newX - starRadius < 0) {
        newX = starRadius;
      } else if (newX + starRadius > this.canvasWidth) {
        newX = this.canvasWidth - starRadius;
      }

      // 上下边界
      const navBottom = this.navHeight;
      const tabbarTop = this.canvasHeight;

      if (newY - starRadius < navBottom) {
        newY = navBottom + starRadius;
      } else if (newY + starRadius > tabbarTop) {
        newY = tabbarTop - starRadius;
      }

      // 更新星星位置
      star.x = newX;
      star.y = newY;

      return 'grabbing';
    }

    // 如果没有拖动的星星，更新悬停状态
    let hasHoveredStar = false;
    for (const star of this.stars) {
      if (star.isDragged) continue; // 跳过正在拖动的星星

      // 使用相同的优化距离计算
      if (Math.abs(x - star.x) <= star.radius && Math.abs(y - star.y) <= star.radius) {
        const distance = Math.sqrt(
          Math.pow(x - star.x, 2) + Math.pow(y - star.y, 2)
        );

        if (distance <= star.radius) {
          star.isHovered = true;
          hasHoveredStar = true;
        } else {
          star.isHovered = false;
        }
      } else {
        star.isHovered = false;
      }
    }

    // 返回鼠标指针样式
    return hasHoveredStar ? 'pointer' : 'default';
  }

  /**
   * 处理触摸结束事件
   * @returns {object|null} - 如果是点击操作，返回主题；如果是拖动操作，返回null
   */
  handleTouchEnd() {
    // 如果有拖动的星星
    if (this.draggedStar) {
      const star = this.draggedStar;

      // 如果是拖动操作
      if (this.isDragging) {
        // 计算拖动向量
        const dragX = star.x - this.starStartX;
        const dragY = star.y - this.starStartY;
        const dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

        // 计算拖动距离与最大拖动距离(半径)的比例，用于调整加速度
        const dragRatio = Math.min(1.0, dragDistance / star.radius);

        if (dragDistance > 0) {
          // 计算反方向的单位向量
          const directionX = -dragX / dragDistance;
          const directionY = -dragY / dragDistance;

          // 设置加速度为正常速度的2倍
          // 计算当前速度的大小
          const currentSpeed = Math.sqrt(
            star.originalVelocityX * star.originalVelocityX +
            star.originalVelocityY * star.originalVelocityY
          );

          // 如果原始速度接近于0，使用基础速度
          const baseSpeed = currentSpeed > 0.01 ? currentSpeed : ANIMATION_BASE_SPEED;

          // 加速速度为正常速度的3倍，并根据拖动距离比例调整
          // 拖动距离越接近最大值(半径)，加速效果越明显
          const acceleratedSpeed = baseSpeed * 3.0 * dragRatio;

          // 应用新速度
          star.velocityX = directionX * acceleratedSpeed;
          star.velocityY = directionY * acceleratedSpeed;

          // 标记为加速状态
          star.isAccelerated = true;
          star.collisionCount = 0;
        } else {
          // 如果没有明显的拖动，恢复原速度
          star.velocityX = star.originalVelocityX || (Math.random() - 0.5) * ANIMATION_BASE_SPEED;
          star.velocityY = star.originalVelocityY || (Math.random() - 0.5) * ANIMATION_BASE_SPEED;
        }

        // 重置拖动状态
        star.isDragged = false;
        this.draggedStar = null;
        this.isDragging = false;

        // 拖动操作不触发点击事件
        return null;
      } else {
        // 如果是点击操作（没有明显拖动）
        // 获取星星主题配置（从页面获取）
        const starThemes = this.page.getBubbleThemes ? this.page.getBubbleThemes() : [];

        if (!Array.isArray(starThemes) || starThemes.length === 0) {
          console.warn('点击处理 - 警告: 获取到的星星主题为空数组或非数组');
          // 重置拖动状态
          star.isDragged = false;
          this.draggedStar = null;
          return null;
        }

        // 找到匹配的主题
        const theme = starThemes.find(t => t.name === star.text);
        if (theme) {
          // 标记星星为点击状态
          star.isClicked = true;

          // 重置拖动状态
          star.isDragged = false;
          this.draggedStar = null;

          // 返回主题和交互信息
          return {
            theme: theme,
            tagId: theme.id,
            interactionType: 'click',
            duration: Date.now() - this.dragStartTime,
            positionX: star.x,
            positionY: star.y
          };
        } else {
          // 如果找不到对应的主题，也重置状态
          star.isDragged = false;
          this.draggedStar = null;
        }
      }
    }

    // 重置所有星星的悬停状态
    for (const star of this.stars) {
      star.isHovered = false;
    }

    // 重置拖动状态
    this.draggedStar = null;
    this.isDragging = false;

    return null;
  }

  /**
   * 重置点击状态
   */
  resetClickState() {
    for (const star of this.stars) {
      if (star.isClicked) {
        // 为被点击的星星重新生成随机速度，保持与整体速度一致
        const angle = Math.random() * Math.PI * 2;
        star.velocityX = Math.cos(angle) * ANIMATION_BASE_SPEED * 0.8;
        star.velocityY = Math.sin(angle) * ANIMATION_BASE_SPEED * 0.8;
        star.isClicked = false;
      }
    }
  }

  /**
   * 停止动画
   */
  stopAnimation() {
    // 标记动画已停止
    this.isAnimating = false;

    // 清理动画计时器
    if (this.animationTimer) {
      try {
        if (typeof this.canvas.cancelAnimationFrame === 'function') {
          this.canvas.cancelAnimationFrame(this.animationTimer);
        } else {
          clearTimeout(this.animationTimer);
        }
      } catch (e) {
        console.error('取消动画帧失败', e);
        // 备用方案：使用clearTimeout
        clearTimeout(this.animationTimer);
      }
      this.animationTimer = null;
    }

    // 清理重试计时器
    if (this.animationRetryTimer) {
      clearTimeout(this.animationRetryTimer);
      this.animationRetryTimer = null;
    }
  }

  /**
   * 更新星星主题
   * @param {Array} newThemes - 新的星星主题配置
   */
  updateStarThemes(newThemes) {
    if (!newThemes || newThemes.length === 0) {
      console.warn('尝试更新空的星星主题');
      return;
    }

    console.log('更新星星主题', newThemes);

    // 保存当前星星的位置和速度信息
    const starStates = {};
    this.stars.forEach(star => {
      const id = star.id;
      starStates[id] = {
        x: star.x,
        y: star.y,
        velocityX: star.velocityX,
        velocityY: star.velocityY,
        radius: star.radius
      };
    });

    // 使用新主题创建星星
    this.createStarsFromThemes(newThemes);

    // 恢复星星的位置和速度
    this.stars.forEach((star, index) => {
      const oldId = `star-${index}`;
      if (starStates[oldId] && index < Object.keys(starStates).length) {
        star.x = starStates[oldId].x;
        star.y = starStates[oldId].y;
        star.velocityX = starStates[oldId].velocityX;
        star.velocityY = starStates[oldId].velocityY;
        star.radius = starStates[oldId].radius;
      }
    });
  }

  /**
   * 清理资源和重置状态
   */
  cleanup() {
    console.log('清理星星画布资源');

    // 停止动画
    this.stopAnimation();

    // 清空画布
    if (this.ctx && this.canvasWidth && this.canvasHeight) {
      try {
        this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      } catch (err) {
        console.error('清空画布失败', err);
      }
    }

    // 释放资源引用
    this.stars = [];
    this.draggedStar = null;

    // 重置状态
    this.isDragging = false;
    this.isAnimating = false;
  }

  /**
   * 销毁画布，释放资源
   */
  destroy() {
    console.log('销毁星星画布');

    // 先执行清理
    this.cleanup();

    // 移除引用
    this.canvas = null;
    this.ctx = null;
    this.page = null;
  }

  /**
   * 绘制星空背景中的小星星
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   */
  drawStarryBackground(ctx) {
    // 如果没有保存的背景星星，初始化它们
    if (!this.backgroundStars) {
      this.initBackgroundStars();
    }

    // 绘制每个背景星星
    for (const star of this.backgroundStars) {
      // 更新闪烁相位
      star.twinklePhase += star.twinkleSpeed;
      if (star.twinklePhase > Math.PI * 2) {
        star.twinklePhase -= Math.PI * 2;
      }

      // 计算闪烁亮度 (0.2-0.7)，降低整体亮度
      const brightness = 0.2 + Math.abs(Math.sin(star.twinklePhase)) * 0.5;

      // 设置星星颜色
      ctx.fillStyle = `rgba(${star.color.r}, ${star.color.g}, ${star.color.b}, ${brightness})`;

      // 绘制星星 (简单的小圆点)
      ctx.beginPath();
      ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
      ctx.fill();
    }
  }

  /**
   * 初始化背景星星
   */
  initBackgroundStars() {
    const stars = [];
    // 创建50-100个随机分布的小星星（减少50%）
    const starCount = 50 + Math.floor(Math.random() * 50);

    // 可用区域 - 导航栏以下的区域
    const areaTop = this.navHeight;
    const areaWidth = this.canvasWidth;
    const areaHeight = this.canvasHeight - this.navHeight;

    for (let i = 0; i < starCount; i++) {
      // 随机位置
      const x = Math.random() * areaWidth;
      const y = areaTop + Math.random() * areaHeight;

      // 随机大小 (0.3-1.2)，减小星星尺寸
      const size = 0.3 + Math.random() * 0.9;

      // 随机颜色 (白色、淡蓝色、淡紫色)
      let color;
      const colorType = Math.random();
      if (colorType < 0.7) {
        // 70% 白色系
        const value = 200 + Math.floor(Math.random() * 55);
        color = { r: value, g: value, b: value };
      } else if (colorType < 0.85) {
        // 15% 淡蓝色系
        color = {
          r: 180 + Math.floor(Math.random() * 30),
          g: 200 + Math.floor(Math.random() * 55),
          b: 255
        };
      } else {
        // 15% 淡紫色系
        color = {
          r: 200 + Math.floor(Math.random() * 55),
          g: 180 + Math.floor(Math.random() * 30),
          b: 255
        };
      }

      // 随机闪烁速度，降低闪烁频率
      const twinkleSpeed = 0.0005 + Math.random() * 0.002;

      // 随机初始相位
      const twinklePhase = Math.random() * Math.PI * 2;

      stars.push({ x, y, size, color, twinkleSpeed, twinklePhase });
    }

    this.backgroundStars = stars;
  }

  /**
   * 获取默认主题数据
   * @returns {Array} 默认主题数组
   */
  getDefaultThemes() {
    return [
      {
        id: '101',
        name: '平台介绍',
        englishName: 'Intro',
        description: '了解AIBUBB平台的核心功能和使用技巧',
        color: '#3B82F6'
      },
      {
        id: '102',
        name: '泡泡功能',
        englishName: 'Bubbles',
        description: '探索交互式泡泡的使用方法',
        color: '#84CC16'
      },
      {
        id: '103',
        name: '广场探索',
        englishName: 'Square',
        description: '发现社区中的精彩内容和讨论',
        color: '#0EA5E9'
      },
      {
        id: '104',
        name: '学习计划',
        englishName: 'Plans',
        description: '制定和管理个性化学习计划',
        color: '#F59E0B'
      },
      {
        id: '105',
        name: '笔记技巧',
        englishName: 'Notes',
        description: '掌握高效记录和组织学习笔记的方法',
        color: '#8B5CF6'
      }
    ];
  }

  /**
   * 重置交互状态
   */
  resetInteractionState() {
    this.resetClickState();
  }
}

module.exports = StarCanvas;
