/* pages/profile/edit-profile.wxss */

page {
  background: linear-gradient(180deg, #e0f2ff 0%, #f0e6ff 100%);
  min-height: 100vh;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding: 0 0 40rpx 0;
  box-sizing: border-box;
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 0 20rpx 0;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* 玻璃卡片基础样式 */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: 30rpx;
  margin: 0 16rpx 20rpx 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  width: calc(100% - 32rpx);
  box-sizing: border-box;
}

/* 头像编辑区域 */
.avatar-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 30rpx;
}

.avatar-container {
  position: relative;
}

.avatar-wrapper {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-avatar-text {
  color: #ffffff;
  font-size: 80rpx;
  font-weight: bold;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-wrapper:active .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.avatar-tip {
  color: #ffffff;
  font-size: 24rpx;
  text-align: center;
}

/* 表单区域 */
.form-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #3B82F6;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid rgba(59, 130, 246, 0.2);
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background: rgba(255, 255, 255, 0.8);
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #3B82F6;
  background: rgba(255, 255, 255, 0.95);
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 20rpx;
  border: 2rpx solid rgba(59, 130, 246, 0.2);
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background: rgba(255, 255, 255, 0.8);
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #3B82F6;
  background: rgba(255, 255, 255, 0.95);
}

.input-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 22rpx;
  color: #999999;
}

.picker-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  border: 2rpx solid rgba(59, 130, 246, 0.2);
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background: rgba(255, 255, 255, 0.8);
}

.arrow-right {
  font-size: 32rpx;
  color: #cccccc;
}

/* 兴趣标签 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.tag-item {
  padding: 16rpx 24rpx;
  border: 2rpx solid rgba(59, 130, 246, 0.3);
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #3B82F6;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.tag-item.selected {
  background: #3B82F6;
  color: #ffffff;
  border-color: #3B82F6;
}

.tags-tip {
  font-size: 24rpx;
  color: #999999;
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.setting-label {
  font-size: 28rpx;
  color: #333333;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 40rpx 16rpx;
}

.btn-secondary {
  flex: 1;
  padding: 24rpx 0;
  border: 2rpx solid #3B82F6;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #3B82F6;
  background: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.btn-primary {
  flex: 2;
  padding: 24rpx 0;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  text-align: center;
  box-shadow: 0 8rpx 16rpx rgba(59, 130, 246, 0.3);
}

.btn-primary[loading] {
  opacity: 0.7;
}

/* 头像裁剪弹窗 */
.avatar-crop-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999999;
  cursor: pointer;
}

.crop-container {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.crop-image {
  width: 100%;
  height: 100%;
}

.crop-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.crop-frame {
  width: 300rpx;
  height: 300rpx;
  border: 4rpx solid #3B82F6;
  border-radius: 50%;
  box-shadow: 0 0 0 9999rpx rgba(0, 0, 0, 0.5);
}

.modal-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

/* 深色模式适配 */
page[data-theme="dark"] {
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
}

page[data-theme="dark"] .glass-card {
  background: rgba(30, 30, 40, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

page[data-theme="dark"] .page-title,
page[data-theme="dark"] .section-title,
page[data-theme="dark"] .form-label,
page[data-theme="dark"] .setting-label {
  color: #ffffff;
}

page[data-theme="dark"] .form-input,
page[data-theme="dark"] .form-textarea,
page[data-theme="dark"] .picker-value {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.2);
}

page[data-theme="dark"] .form-input:focus,
page[data-theme="dark"] .form-textarea:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: #3B82F6;
}

page[data-theme="dark"] .tag-item {
  background: rgba(255, 255, 255, 0.1);
  color: #3B82F6;
  border-color: rgba(59, 130, 246, 0.5);
}

page[data-theme="dark"] .btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #3B82F6;
}

page[data-theme="dark"] .modal-content {
  background: #2a2a3e;
}

page[data-theme="dark"] .modal-title {
  color: #ffffff;
}

page[data-theme="dark"] .loading-content {
  background: rgba(30, 30, 40, 0.95);
}

page[data-theme="dark"] .loading-text {
  color: #cccccc;
}