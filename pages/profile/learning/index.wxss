/* pages/profile/learning/index.wxss */

page {
  background: linear-gradient(180deg, #e0f2ff 0%, #f0e6ff 100%);
  min-height: 100vh;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding: 0 0 40rpx 0;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  width: 100%;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  width: 100%;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 20rpx;
}

.retry-button {
  background-color: #3B82F6;
  color: #ffffff;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  border: none;
}

/* 玻璃卡片基础样式 */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: 30rpx;
  margin: 0 16rpx 20rpx 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  width: calc(100% - 32rpx);
  box-sizing: border-box;
}

/* 概览卡片 */
.overview-card {
  margin-top: 16rpx;
}

.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 3rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 100%;
  height: 100%;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-avatar-text {
  color: #ffffff;
  font-size: 48rpx;
  font-weight: bold;
}

.user-info {
  flex: 1;
}

.username {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.learning-status {
  font-size: 24rpx;
  color: #666666;
  background: rgba(59, 130, 246, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #3B82F6;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx 0;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  color: #ffffff;
  box-shadow: 0 8rpx 16rpx rgba(59, 130, 246, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #3B82F6;
  border: 2rpx solid #3B82F6;
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  margin: 0 16rpx 20rpx 16rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(31, 38, 135, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(59, 130, 246, 0.1);
  color: #3B82F6;
  font-weight: 600;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.plan-status {
  font-size: 24rpx;
  color: #10B981;
  background: rgba(16, 185, 129, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

/* 当前学习计划 */
.plan-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.plan-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.plan-cover image {
  width: 100%;
  height: 100%;
}

.default-cover {
  font-size: 48rpx;
}

.plan-details {
  flex: 1;
}

.plan-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.plan-description {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.plan-progress {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3B82F6, #8B5CF6);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #3B82F6;
  font-weight: 600;
}

/* 无计划状态 */
.no-plan-card {
  text-align: center;
}

.no-plan-content {
  padding: 40rpx 0;
}

.no-plan-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.no-plan-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.no-plan-desc {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.create-plan-btn {
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  color: #ffffff;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(59, 130, 246, 0.3);
}

/* 周进度 */
.weekly-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.day-name {
  font-size: 24rpx;
  color: #666666;
}

.progress-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-inner {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #f0f0f0;
}

.progress-value {
  font-size: 20rpx;
  color: #999999;
}

.progress-item.completed .day-name,
.progress-item.completed .progress-value {
  color: #3B82F6;
  font-weight: 600;
}

/* 计划列表 */
.add-plan-btn {
  background: rgba(59, 130, 246, 0.1);
  color: #3B82F6;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.plans-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.plan-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.3s ease;
}

.plan-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.plan-item.current {
  border-color: #3B82F6;
  background: rgba(59, 130, 246, 0.05);
}

.current-badge {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: #3B82F6;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.plan-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 8rpx;
}

/* 空状态 */
.empty-plans,
.empty-activities {
  text-align: center;
  padding: 60rpx 0;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.create-first-plan-btn,
.start-learning-btn {
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  color: #ffffff;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(59, 130, 246, 0.3);
}

/* 进度图表 */
.progress-chart {
  height: 300rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.chart-placeholder {
  font-size: 28rpx;
  color: #666666;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
}

.progress-stat-item {
  text-align: center;
}

.progress-stat-item .stat-label {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.progress-stat-item .stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #3B82F6;
}

/* 活动列表 */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.activity-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.activity-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 20rpx;
}

.activity-info {
  flex: 1;
}

.activity-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}

.activity-desc {
  font-size: 24rpx;
  color: #666666;
}

.activity-arrow {
  font-size: 32rpx;
  color: #cccccc;
}

/* 深色模式适配 */
page[data-theme="dark"] {
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
}

page[data-theme="dark"] .glass-card {
  background: rgba(30, 30, 40, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

page[data-theme="dark"] .tab-navigation {
  background: rgba(30, 30, 40, 0.25);
}

page[data-theme="dark"] .username,
page[data-theme="dark"] .card-title,
page[data-theme="dark"] .plan-title,
page[data-theme="dark"] .activity-title {
  color: #ffffff;
}

page[data-theme="dark"] .learning-status,
page[data-theme="dark"] .plan-description,
page[data-theme="dark"] .activity-desc,
page[data-theme="dark"] .stat-label,
page[data-theme="dark"] .day-name,
page[data-theme="dark"] .progress-value {
  color: #cccccc;
}

page[data-theme="dark"] .stat-item,
page[data-theme="dark"] .plan-item,
page[data-theme="dark"] .activity-item {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.1);
}

page[data-theme="dark"] .action-btn.secondary,
page[data-theme="dark"] .add-plan-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #3B82F6;
}

page[data-theme="dark"] .progress-chart {
  background: rgba(255, 255, 255, 0.1);
}

page[data-theme="dark"] .chart-placeholder,
page[data-theme="dark"] .empty-text {
  color: #cccccc;
}