// pages/profile/learning/index.js
// 导入API工具
const { learningPlanAPI, statisticsAPI } = require('../../../utils/api');
// 导入认证服务
const authService = require('../../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,

    // 学习数据
    learningPlans: [],
    currentPlan: null,
    learningStats: {
      totalDays: 0,
      completedCourses: 0,
      inProgressCourses: 0,
      totalMinutes: 0,
      streak: 0
    },

    // 最近学习记录
    recentActivities: [],

    // 学习进度
    weeklyProgress: [],

    // 状态控制
    isLoading: true,
    isRefreshing: false,
    loadingFailed: false,

    // 视图控制
    activeTab: 'overview', // overview, plans, progress, activities

    // 其他
    theme: 'light'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // 获取主题模式
    const app = getApp();
    const theme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme });

    // 检查登录状态
    const isLoggedIn = await authService.isLoggedIn();
    if (!isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 加载数据
    await this.loadAllData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新主题
    const app = getApp();
    const theme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme });
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    try {
      this.setData({ isLoading: true, loadingFailed: false });

      // 获取用户信息
      const userInfo = await authService.getCurrentUser();

      // 并行加载数据
      const [
        learningStatsResult,
        learningPlansResult,
        recentActivitiesResult
      ] = await Promise.allSettled([
        this.loadLearningStats(),
        this.loadLearningPlans(),
        this.loadRecentActivities()
      ]);

      // 处理结果
      if (learningStatsResult.status === 'fulfilled') {
        this.setData({ learningStats: learningStatsResult.value });
      }

      if (learningPlansResult.status === 'fulfilled') {
        const { plans, currentPlan } = learningPlansResult.value;
        this.setData({
          learningPlans: plans,
          currentPlan: currentPlan
        });
      }

      if (recentActivitiesResult.status === 'fulfilled') {
        this.setData({ recentActivities: recentActivitiesResult.value });
      }

      // 生成周进度数据
      this.generateWeeklyProgress();

      this.setData({
        userInfo,
        isLoading: false
      });

    } catch (error) {
      console.error('加载学习数据失败:', error);

      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 加载学习统计数据
   */
  async loadLearningStats() {
    try {
      const response = await statisticsAPI.getLearningStatistics();

      if (response.success && response.data) {
        return {
          totalDays: response.data.totalDays || 0,
          completedCourses: response.data.completedCourses || 0,
          inProgressCourses: response.data.inProgressCourses || 0,
          totalMinutes: response.data.totalMinutes || 0,
          streak: response.data.streak || 0
        };
      }

      // 返回默认数据
      return {
        totalDays: 0,
        completedCourses: 0,
        inProgressCourses: 0,
        totalMinutes: 0,
        streak: 0
      };

    } catch (error) {
      console.error('获取学习统计失败:', error);
      throw error;
    }
  },

  /**
   * 加载学习计划
   */
  async loadLearningPlans() {
    try {
      const response = await learningPlanAPI.getPlans({
        status: 'all',
        limit: 10
      });

      if (response.success && response.data) {
        const plans = response.data.plans || [];
        const currentPlan = plans.find(plan => plan.isCurrent) || null;

        return { plans, currentPlan };
      }

      return { plans: [], currentPlan: null };

    } catch (error) {
      console.error('获取学习计划失败:', error);
      throw error;
    }
  },

  /**
   * 加载最近学习活动
   */
  async loadRecentActivities() {
    try {
      const response = await statisticsAPI.getActivities({
        limit: 10,
        orderBy: 'createdAt',
        order: 'desc'
      });

      if (response.success && response.data) {
        return response.data.activities || [];
      }

      return [];

    } catch (error) {
      console.error('获取学习活动失败:', error);
      throw error;
    }
  },

  /**
   * 生成周进度数据
   */
  generateWeeklyProgress() {
    const weeklyProgress = [];
    const today = new Date();

    // 生成最近7天的数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      const dayName = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];
      const progress = Math.random() * 100; // 模拟数据，实际应从API获取

      weeklyProgress.push({
        date: date.toISOString().split('T')[0],
        dayName,
        progress: Math.round(progress),
        completed: progress > 50
      });
    }

    this.setData({ weeklyProgress });
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  /**
   * 跳转到学习计划详情
   */
  navigateToPlanDetail(e) {
    const planId = e.currentTarget.dataset.planId;
    wx.navigateTo({
      url: `/pages/plan-detail/index?id=${planId}`
    });
  },

  /**
   * 跳转到创建学习计划
   */
  navigateToCreatePlan() {
    wx.navigateTo({
      url: '/pages/create-plan/index'
    });
  },

  /**
   * 跳转到学习统计详情
   */
  navigateToStatistics() {
    wx.navigateTo({
      url: '/pages/statistics/index'
    });
  },

  /**
   * 开始学习
   */
  startLearning() {
    if (this.data.currentPlan) {
      wx.navigateTo({
        url: `/pages/learn/index?planId=${this.data.currentPlan.id}`
      });
    } else {
      wx.showModal({
        title: '提示',
        content: '您还没有激活的学习计划，是否创建一个？',
        success: res => {
          if (res.confirm) {
            this.navigateToCreatePlan();
          }
        }
      });
    }
  },

  /**
   * 查看学习活动详情
   */
  viewActivityDetail(e) {
    const activity = e.currentTarget.dataset.activity;

    // 根据活动类型跳转到相应页面
    switch (activity.type) {
      case 'exercise':
        wx.navigateTo({
          url: `/pages/exercise-detail/index?id=${activity.targetId}`
        });
        break;
      case 'insight':
        wx.navigateTo({
          url: `/pages/insight-detail/index?id=${activity.targetId}`
        });
        break;
      case 'note':
        wx.navigateTo({
          url: `/pages/note-detail/index?id=${activity.targetId}`
        });
        break;
      default:
        wx.showToast({
          title: '页面开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(minutes) {
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
    }
  },

  /**
   * 格式化活动类型
   */
  formatActivityType(type) {
    const typeMap = {
      'exercise': '练习',
      'insight': '观点',
      'note': '笔记',
      'plan': '计划'
    };
    return typeMap[type] || '学习';
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  async onPullDownRefresh() {
    this.setData({ isRefreshing: true });

    try {
      await this.loadAllData();
    } finally {
      this.setData({ isRefreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 可以在这里加载更多学习活动
    console.log('触底加载更多');
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的学习进度 - AI互动泡泡',
      path: '/pages/profile/learning/index',
      imageUrl: '/assets/share-learning.png'
    };
  }
});