<!--pages/profile/learning/index.wxml-->
<view class="container" data-theme="{{theme}}">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载学习数据中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:elif="{{loadingFailed}}">
    <view class="error-icon">!</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="loadAllData">重试</button>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 用户学习概览 -->
    <view class="glass-card overview-card">
      <view class="user-section">
        <view class="avatar-container">
          <image class="avatar" src="{{userInfo.avatarUrl}}" wx:if="{{userInfo.avatarUrl}}"></image>
          <view class="default-avatar" wx:else>
            <text class="default-avatar-text">{{userInfo.nickName ? userInfo.nickName.substring(0, 1) : 'U'}}</text>
          </view>
        </view>
        <view class="user-info">
          <text class="username">{{userInfo.nickName || '学习者'}}</text>
          <text class="learning-status">{{currentPlan ? '学习中' : '待开始'}}</text>
        </view>
      </view>

      <!-- 学习统计 -->
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{learningStats.totalDays}}</text>
          <text class="stat-label">学习天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{learningStats.completedCourses}}</text>
          <text class="stat-label">完成课程</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{formatTime(learningStats.totalMinutes)}}</text>
          <text class="stat-label">学习时长</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{learningStats.streak}}</text>
          <text class="stat-label">连续天数</text>
        </view>
      </view>

      <!-- 快速操作 -->
      <view class="quick-actions">
        <button class="action-btn primary" bindtap="startLearning">
          {{currentPlan ? '继续学习' : '开始学习'}}
        </button>
        <button class="action-btn secondary" bindtap="navigateToStatistics">学习统计</button>
      </view>
    </view>

    <!-- 标签页导航 -->
    <view class="tab-navigation">
      <view class="tab-item {{activeTab === 'overview' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="overview">
        <text>概览</text>
      </view>
      <view class="tab-item {{activeTab === 'plans' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="plans">
        <text>计划</text>
      </view>
      <view class="tab-item {{activeTab === 'progress' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="progress">
        <text>进度</text>
      </view>
      <view class="tab-item {{activeTab === 'activities' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="activities">
        <text>活动</text>
      </view>
    </view>

    <!-- 概览标签页 -->
    <view class="tab-content" wx:if="{{activeTab === 'overview'}}">
      <!-- 当前学习计划 -->
      <view class="glass-card current-plan-card" wx:if="{{currentPlan}}">
        <view class="card-header">
          <text class="card-title">当前学习计划</text>
          <text class="plan-status">进行中</text>
        </view>
        <view class="plan-info" bindtap="navigateToPlanDetail" data-plan-id="{{currentPlan.id}}">
          <view class="plan-cover">
            <image src="{{currentPlan.coverImageUrl}}" wx:if="{{currentPlan.coverImageUrl}}"></image>
            <view class="default-cover" wx:else>📚</view>
          </view>
          <view class="plan-details">
            <text class="plan-title">{{currentPlan.title}}</text>
            <text class="plan-description">{{currentPlan.description}}</text>
            <view class="plan-progress">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{currentPlan.progress}}%"></view>
              </view>
              <text class="progress-text">{{currentPlan.progress}}%</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 无当前计划提示 -->
      <view class="glass-card no-plan-card" wx:else>
        <view class="no-plan-content">
          <text class="no-plan-icon">📖</text>
          <text class="no-plan-title">还没有学习计划</text>
          <text class="no-plan-desc">创建一个学习计划开始你的学习之旅</text>
          <button class="create-plan-btn" bindtap="navigateToCreatePlan">创建计划</button>
        </view>
      </view>

      <!-- 本周学习进度 -->
      <view class="glass-card weekly-progress-card">
        <view class="card-header">
          <text class="card-title">本周学习进度</text>
        </view>
        <view class="weekly-progress">
          <view class="progress-item {{item.completed ? 'completed' : ''}}" 
                wx:for="{{weeklyProgress}}" wx:key="date">
            <text class="day-name">{{item.dayName}}</text>
            <view class="progress-circle">
              <view class="progress-inner" style="background: {{item.completed ? '#3B82F6' : '#f0f0f0'}}"></view>
            </view>
            <text class="progress-value">{{item.progress}}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 计划标签页 -->
    <view class="tab-content" wx:if="{{activeTab === 'plans'}}">
      <view class="glass-card plans-card">
        <view class="card-header">
          <text class="card-title">我的学习计划</text>
          <button class="add-plan-btn" bindtap="navigateToCreatePlan">+ 新建</button>
        </view>
        
        <view class="plans-list" wx:if="{{learningPlans.length > 0}}">
          <view class="plan-item {{plan.isCurrent ? 'current' : ''}}" 
                wx:for="{{learningPlans}}" wx:key="id"
                bindtap="navigateToPlanDetail" data-plan-id="{{item.id}}">
            <view class="plan-cover">
              <image src="{{item.coverImageUrl}}" wx:if="{{item.coverImageUrl}}"></image>
              <view class="default-cover" wx:else>📚</view>
            </view>
            <view class="plan-info">
              <text class="plan-title">{{item.title}}</text>
              <text class="plan-description">{{item.description}}</text>
              <view class="plan-meta">
                <text class="plan-status">{{item.status === 'completed' ? '已完成' : item.status === 'inProgress' ? '进行中' : '未开始'}}</text>
                <text class="plan-progress">{{item.progress}}%</text>
              </view>
            </view>
            <view class="current-badge" wx:if="{{item.isCurrent}}">当前</view>
          </view>
        </view>

        <view class="empty-plans" wx:else>
          <text class="empty-icon">📝</text>
          <text class="empty-text">还没有学习计划</text>
          <button class="create-first-plan-btn" bindtap="navigateToCreatePlan">创建第一个计划</button>
        </view>
      </view>
    </view>

    <!-- 进度标签页 -->
    <view class="tab-content" wx:if="{{activeTab === 'progress'}}">
      <view class="glass-card progress-card">
        <view class="card-header">
          <text class="card-title">学习进度分析</text>
        </view>
        
        <!-- 进度图表区域 -->
        <view class="progress-chart">
          <text class="chart-placeholder">📊 进度图表开发中...</text>
        </view>

        <!-- 进度统计 -->
        <view class="progress-stats">
          <view class="progress-stat-item">
            <text class="stat-label">本周完成</text>
            <text class="stat-value">{{learningStats.completedCourses}}</text>
          </view>
          <view class="progress-stat-item">
            <text class="stat-label">进行中</text>
            <text class="stat-value">{{learningStats.inProgressCourses}}</text>
          </view>
          <view class="progress-stat-item">
            <text class="stat-label">平均时长</text>
            <text class="stat-value">{{Math.round(learningStats.totalMinutes / Math.max(learningStats.totalDays, 1))}}分钟</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 活动标签页 -->
    <view class="tab-content" wx:if="{{activeTab === 'activities'}}">
      <view class="glass-card activities-card">
        <view class="card-header">
          <text class="card-title">最近学习活动</text>
        </view>
        
        <view class="activities-list" wx:if="{{recentActivities.length > 0}}">
          <view class="activity-item" 
                wx:for="{{recentActivities}}" wx:key="id"
                bindtap="viewActivityDetail" data-activity="{{item}}">
            <view class="activity-icon">
              <text>{{formatActivityType(item.type) === '练习' ? '🏃' : formatActivityType(item.type) === '观点' ? '💡' : '📝'}}</text>
            </view>
            <view class="activity-info">
              <text class="activity-title">{{item.title || '学习活动'}}</text>
              <text class="activity-desc">{{formatActivityType(item.type)}} · {{item.createdAt}}</text>
            </view>
            <view class="activity-arrow">›</view>
          </view>
        </view>

        <view class="empty-activities" wx:else>
          <text class="empty-icon">📚</text>
          <text class="empty-text">还没有学习活动</text>
          <button class="start-learning-btn" bindtap="startLearning">开始学习</button>
        </view>
      </view>
    </view>
  </view>
</view>