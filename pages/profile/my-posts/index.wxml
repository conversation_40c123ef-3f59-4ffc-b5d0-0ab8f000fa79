<!--pages/profile/my-posts/index.wxml-->
<view class="container" data-theme="{{theme}}">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载发布数据中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:elif="{{loadingFailed}}">
    <view class="error-icon">!</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="loadMyPosts">重试</button>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 统计信息 -->
    <view class="glass-card stats-card">
      <view class="stats-header">
        <text class="stats-title">我的发布</text>
        <button class="create-btn" bindtap="createNewPost">+ 创建</button>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{postStats.total}}</text>
          <text class="stat-label">总数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{postStats.published}}</text>
          <text class="stat-label">已发布</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{postStats.totalViews}}</text>
          <text class="stat-label">总浏览</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{postStats.totalLikes}}</text>
          <text class="stat-label">总点赞</text>
        </view>
      </view>
    </view>

    <!-- 状态筛选 -->
    <view class="status-filter">
      <scroll-view class="status-scroll" scroll-x="true" show-scrollbar="false">
        <view class="status-item {{activeStatus === 'all' ? 'active' : ''}}"
              bindtap="switchStatus" data-status="all">
          <text class="status-name">全部</text>
          <text class="status-count">({{postStats.total}})</text>
        </view>
        <view class="status-item {{activeStatus === 'published' ? 'active' : ''}}"
              bindtap="switchStatus" data-status="published">
          <text class="status-name">已发布</text>
          <text class="status-count">({{postStats.published}})</text>
        </view>
        <view class="status-item {{activeStatus === 'draft' ? 'active' : ''}}"
              bindtap="switchStatus" data-status="draft">
          <text class="status-name">草稿</text>
          <text class="status-count">({{postStats.draft}})</text>
        </view>
        <view class="status-item {{activeStatus === 'private' ? 'active' : ''}}"
              bindtap="switchStatus" data-status="private">
          <text class="status-name">私密</text>
          <text class="status-count">({{postStats.private}})</text>
        </view>
      </scroll-view>
    </view>

    <!-- 工具栏 -->
    <view class="toolbar">
      <view class="toolbar-left">
        <view class="view-mode-toggle" bindtap="switchViewMode">
          <image class="mode-icon" src="{{viewMode === 'list' ? '/assets/icons/grid.png' : '/assets/icons/list.png'}}"></image>
        </view>
        <view class="sort-button" bindtap="switchSort">
          <image class="sort-icon" src="/assets/icons/sort.png"></image>
          <text class="sort-text">排序</text>
        </view>
      </view>
      <view class="toolbar-right">
        <view class="selection-toggle" bindtap="toggleSelectionMode">
          <text class="selection-text">{{isSelectionMode ? '取消' : '选择'}}</text>
        </view>
      </view>
    </view>

    <!-- 批量操作栏 -->
    <view class="batch-actions" wx:if="{{isSelectionMode}}">
      <view class="batch-left">
        <view class="select-all" bindtap="toggleSelectAll">
          <text class="select-all-text">
            {{selectedItems.length === myPosts.length ? '取消全选' : '全选'}}
          </text>
        </view>
        <text class="selected-count">已选择 {{selectedItems.length}} 项</text>
      </view>
      <view class="batch-right">
        <button class="batch-button delete" bindtap="batchDelete">
          删除
        </button>
      </view>
    </view>

    <!-- 发布列表 -->
    <view class="posts-container">
      <!-- 列表视图 -->
      <view class="posts-list" wx:if="{{viewMode === 'list'}}">
        <view class="post-item {{isSelectionMode ? 'selection-mode' : ''}} {{selectedItems.includes(item.id) ? 'selected' : ''}}"
              wx:for="{{myPosts}}" wx:key="id"
              bindtap="viewPostDetail" data-post="{{item}}" data-item-id="{{item.id}}">
          
          <!-- 选择框 -->
          <view class="selection-checkbox" wx:if="{{isSelectionMode}}">
            <view class="checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}">
              <text class="checkbox-icon" wx:if="{{selectedItems.includes(item.id)}}">✓</text>
            </view>
          </view>

          <!-- 内容区域 -->
          <view class="item-content">
            <!-- 封面图片 -->
            <view class="item-cover" wx:if="{{item.coverImage}}">
              <image class="cover-image" src="{{item.coverImage}}" mode="aspectFill"></image>
            </view>

            <!-- 详情信息 -->
            <view class="item-details">
              <view class="item-header">
                <text class="item-title">{{item.title}}</text>
                <view class="status-badge {{item.status}}">{{formatStatusName(item.status)}}</view>
              </view>
              
              <text class="item-content-preview">{{item.content}}</text>
              
              <!-- 标签 -->
              <view class="item-tags" wx:if="{{item.tags && item.tags.length > 0}}">
                <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">#{{tag}}</text>
              </view>

              <!-- 元信息 -->
              <view class="item-meta">
                <text class="meta-time">{{formatTime(item.createdAt)}}</text>
                <text class="meta-words">{{item.wordCount}}字</text>
                <text class="meta-stats" wx:if="{{item.status === 'published'}}">
                  {{item.stats.views}}浏览 · {{item.stats.likes}}点赞
                </text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="item-actions" wx:if="{{!isSelectionMode}}">
              <view class="action-button edit-btn" 
                    bindtap="editPost" data-post="{{item}}">
                <image class="action-icon" src="/assets/icons/edit.png"></image>
              </view>
              <view class="action-button publish-btn" 
                    bindtap="togglePublish" data-post="{{item}}"
                    wx:if="{{item.status !== 'published'}}">
                <image class="action-icon" src="/assets/icons/publish.png"></image>
              </view>
              <view class="action-button unpublish-btn" 
                    bindtap="togglePublish" data-post="{{item}}"
                    wx:if="{{item.status === 'published'}}">
                <image class="action-icon" src="/assets/icons/unpublish.png"></image>
              </view>
              <view class="action-button delete-btn" 
                    bindtap="deletePost" data-post="{{item}}">
                <image class="action-icon" src="/assets/icons/delete.png"></image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 网格视图 -->
      <view class="posts-grid" wx:elif="{{viewMode === 'grid'}}">
        <view class="grid-item {{isSelectionMode ? 'selection-mode' : ''}} {{selectedItems.includes(item.id) ? 'selected' : ''}}"
              wx:for="{{myPosts}}" wx:key="id"
              bindtap="viewPostDetail" data-post="{{item}}" data-item-id="{{item.id}}">
          
          <!-- 选择框 -->
          <view class="grid-selection-checkbox" wx:if="{{isSelectionMode}}">
            <view class="checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}">
              <text class="checkbox-icon" wx:if="{{selectedItems.includes(item.id)}}">✓</text>
            </view>
          </view>

          <!-- 封面 -->
          <view class="grid-cover">
            <image class="grid-cover-image" src="{{item.coverImage}}" wx:if="{{item.coverImage}}" mode="aspectFill"></image>
            <view class="grid-default-cover" wx:else>
              <text class="grid-cover-icon">📝</text>
            </view>
            <view class="grid-status-badge {{item.status}}">{{formatStatusName(item.status)}}</view>
            
            <!-- 操作按钮 -->
            <view class="grid-actions" wx:if="{{!isSelectionMode}}">
              <view class="grid-action-btn edit" bindtap="editPost" data-post="{{item}}">
                <image class="grid-action-icon" src="/assets/icons/edit.png"></image>
              </view>
              <view class="grid-action-btn delete" bindtap="deletePost" data-post="{{item}}">
                <image class="grid-action-icon" src="/assets/icons/delete.png"></image>
              </view>
            </view>
          </view>

          <!-- 信息 -->
          <view class="grid-info">
            <text class="grid-title">{{item.title}}</text>
            <text class="grid-meta">{{formatTime(item.createdAt)}} · {{item.wordCount}}字</text>
            <text class="grid-stats" wx:if="{{item.status === 'published'}}">
              {{item.stats.views}}浏览 · {{item.stats.likes}}点赞
            </text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-posts" wx:if="{{myPosts.length === 0 && !isLoading}}">
        <text class="empty-icon">✍️</text>
        <text class="empty-title">还没有发布内容</text>
        <text class="empty-desc">开始创作你的第一篇文章吧</text>
        <button class="create-first-button" bindtap="createNewPost">开始创作</button>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{isLoadingMore}}">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && myPosts.length > 0}}">
        <text class="no-more-text">没有更多内容了</text>
      </view>
    </view>
  </view>
</view>