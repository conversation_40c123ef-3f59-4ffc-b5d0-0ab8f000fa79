// pages/profile/my-posts/index.js
// 导入API工具
const { userAPI } = require('../../../utils/api');
// 导入认证服务
const authService = require('../../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 发布数据
    myPosts: [],
    postStats: {
      total: 0,
      published: 0,
      draft: 0,
      private: 0,
      totalViews: 0,
      totalLikes: 0
    },

    // 筛选和排序
    activeStatus: 'all', // all, published, draft, private
    sortBy: 'createdAt', // createdAt, title, views, likes
    sortOrder: 'desc', // asc, desc

    // 视图控制
    viewMode: 'list', // list, grid

    // 分页
    currentPage: 1,
    pageSize: 20,
    hasMore: true,

    // 状态控制
    isLoading: true,
    isLoadingMore: false,
    isRefreshing: false,
    loadingFailed: false,

    // 批量操作
    isSelectionMode: false,
    selectedItems: [],

    // 其他
    theme: 'light'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // 获取主题模式
    const app = getApp();
    const theme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme });

    // 检查登录状态
    const isLoggedIn = await authService.isLoggedIn();
    if (!isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 加载发布数据
    await this.loadMyPosts();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新主题
    const app = getApp();
    const theme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme });
  },

  /**
   * 加载我的发布数据
   */
  async loadMyPosts(isLoadMore = false) {
    try {
      if (!isLoadMore) {
        this.setData({
          isLoading: true,
          loadingFailed: false,
          currentPage: 1
        });
      } else {
        this.setData({ isLoadingMore: true });
      }

      // 模拟API调用 - 实际应该调用真实的用户发布API
      const mockPosts = this.generateMockPosts();

      // 根据状态筛选
      let filteredPosts = mockPosts;
      if (this.data.activeStatus !== 'all') {
        filteredPosts = mockPosts.filter(post => post.status === this.data.activeStatus);
      }

      // 排序
      filteredPosts = this.sortPosts(filteredPosts);

      // 分页处理
      const startIndex = (this.data.currentPage - 1) * this.data.pageSize;
      const endIndex = startIndex + this.data.pageSize;
      const pagePosts = filteredPosts.slice(startIndex, endIndex);

      // 更新数据
      const myPosts = isLoadMore ? [...this.data.myPosts, ...pagePosts] : pagePosts;

      // 计算统计数据
      const postStats = this.calculateStats(mockPosts);

      this.setData({
        myPosts,
        postStats,
        hasMore: endIndex < filteredPosts.length,
        isLoading: false,
        isLoadingMore: false,
        currentPage: isLoadMore ? this.data.currentPage + 1 : 1
      });
    } catch (error) {
      console.error('加载发布数据失败:', error);

      this.setData({
        isLoading: false,
        isLoadingMore: false,
        loadingFailed: true
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 生成模拟发布数据
   */
  generateMockPosts() {
    const statuses = ['published', 'draft', 'private'];
    const mockData = [];

    for (let i = 1; i <= 30; i++) {
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const createdAt = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000);
      const views = Math.floor(Math.random() * 1000);
      const likes = Math.floor(Math.random() * 100);
      const comments = Math.floor(Math.random() * 50);

      mockData.push({
        id: i,
        title: this.generateMockTitle(i),
        content: this.generateMockContent(),
        coverImage: Math.random() > 0.6 ? `/assets/images/mock-cover-${(i % 5) + 1}.png` : null,
        status,
        createdAt: createdAt.toISOString(),
        updatedAt: new Date(
          createdAt.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000
        ).toISOString(),
        publishedAt: status === 'published' ? createdAt.toISOString() : null,
        tags: this.generateMockTags(),
        stats: {
          views: status === 'published' ? views : 0,
          likes: status === 'published' ? likes : 0,
          comments: status === 'published' ? comments : 0,
          shares: status === 'published' ? Math.floor(Math.random() * 20) : 0
        },
        wordCount: Math.floor(Math.random() * 2000) + 500
      });
    }

    return mockData;
  },

  /**
   * 生成模拟标题
   */
  generateMockTitle(index) {
    const titles = [
      '我的学习心得：如何高效掌握新技能',
      '产品设计思考：用户体验的重要性',
      '技术分享：前端开发最佳实践总结',
      '读书笔记：《高效能人士的七个习惯》',
      '工作感悟：团队协作的艺术',
      '学习方法论：如何构建知识体系',
      '创业思考：从0到1的产品之路',
      '技术成长：我的编程学习之路',
      '职场经验：如何提升沟通能力',
      '生活感悟：保持学习的热情',
      '项目复盘：失败中的宝贵经验',
      '行业观察：未来技术发展趋势',
      '个人成长：突破舒适圈的重要性',
      '学习笔记：深度学习入门指南',
      '工具推荐：提高工作效率的神器'
    ];

    return titles[index % titles.length];
  },

  /**
   * 生成模拟内容
   */
  generateMockContent() {
    const contents = [
      '在这篇文章中，我想分享一些关于学习和成长的思考。通过不断的实践和反思，我发现了一些有效的方法...',
      '最近在工作中遇到了一些挑战，让我对团队协作有了新的认识。在这里记录下来，希望对大家有所帮助...',
      '技术的发展日新月异，作为开发者，我们需要保持持续学习的态度。今天想和大家分享一些学习心得...',
      '读完这本书后，我对个人效能管理有了全新的理解。书中的七个习惯确实值得我们深入思考和实践...',
      '在产品设计的过程中，用户体验始终是我们需要关注的核心。通过这个项目，我学到了很多宝贵的经验...'
    ];

    return contents[Math.floor(Math.random() * contents.length)];
  },

  /**
   * 生成模拟标签
   */
  generateMockTags() {
    const allTags = [
      '学习',
      '成长',
      '技能',
      '工作',
      '产品',
      '技术',
      '思考',
      '经验',
      '分享',
      '总结'
    ];
    const tagCount = Math.floor(Math.random() * 4) + 1;
    const selectedTags = [];

    for (let i = 0; i < tagCount; i++) {
      const randomTag = allTags[Math.floor(Math.random() * allTags.length)];
      if (!selectedTags.includes(randomTag)) {
        selectedTags.push(randomTag);
      }
    }

    return selectedTags;
  },

  /**
   * 计算统计数据
   */
  calculateStats(posts) {
    const published = posts.filter(post => post.status === 'published');
    const draft = posts.filter(post => post.status === 'draft');
    const privateCount = posts.filter(post => post.status === 'private');

    const totalViews = published.reduce((sum, post) => sum + post.stats.views, 0);
    const totalLikes = published.reduce((sum, post) => sum + post.stats.likes, 0);

    return {
      total: posts.length,
      published: published.length,
      draft: draft.length,
      private: privateCount.length,
      totalViews,
      totalLikes
    };
  },

  /**
   * 排序文章
   */
  sortPosts(posts) {
    return posts.sort((a, b) => {
      let aValue, bValue;

      switch (this.data.sortBy) {
        case 'title':
          aValue = a.title;
          bValue = b.title;
          break;
        case 'views':
          aValue = a.stats.views;
          bValue = b.stats.views;
          break;
        case 'likes':
          aValue = a.stats.likes;
          bValue = b.stats.likes;
          break;
        case 'createdAt':
        default:
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
      }

      if (this.data.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  },

  /**
   * 切换状态筛选
   */
  switchStatus(e) {
    const status = e.currentTarget.dataset.status;

    if (status === this.data.activeStatus) return;

    this.setData({
      activeStatus: status,
      currentPage: 1
    });

    this.loadMyPosts();
  },

  /**
   * 切换视图模式
   */
  switchViewMode() {
    const newMode = this.data.viewMode === 'list' ? 'grid' : 'list';
    this.setData({ viewMode: newMode });
  },

  /**
   * 切换排序方式
   */
  switchSort() {
    wx.showActionSheet({
      itemList: ['按创建时间', '按标题', '按浏览量', '按点赞数'],
      success: res => {
        const sortOptions = ['createdAt', 'title', 'views', 'likes'];
        const newSortBy = sortOptions[res.tapIndex];

        if (newSortBy === this.data.sortBy) {
          // 如果是同一个排序字段，切换排序顺序
          this.setData({
            sortOrder: this.data.sortOrder === 'asc' ? 'desc' : 'asc'
          });
        } else {
          // 如果是不同排序字段，使用默认顺序
          this.setData({
            sortBy: newSortBy,
            sortOrder: 'desc'
          });
        }

        this.loadMyPosts();
      }
    });
  },

  /**
   * 切换选择模式
   */
  toggleSelectionMode() {
    this.setData({
      isSelectionMode: !this.data.isSelectionMode,
      selectedItems: []
    });
  },

  /**
   * 选择/取消选择项目
   */
  toggleItemSelection(e) {
    if (!this.data.isSelectionMode) return;

    const itemId = e.currentTarget.dataset.itemId;
    const selectedItems = [...this.data.selectedItems];
    const index = selectedItems.indexOf(itemId);

    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(itemId);
    }

    this.setData({ selectedItems });
  },

  /**
   * 全选/取消全选
   */
  toggleSelectAll() {
    const allSelected = this.data.selectedItems.length === this.data.myPosts.length;

    if (allSelected) {
      this.setData({ selectedItems: [] });
    } else {
      const allIds = this.data.myPosts.map(post => post.id);
      this.setData({ selectedItems: allIds });
    }
  },

  /**
   * 批量删除
   */
  batchDelete() {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的文章',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${this.data.selectedItems.length} 篇文章吗？此操作不可恢复。`,
      success: res => {
        if (res.confirm) {
          this.performBatchDelete();
        }
      }
    });
  },

  /**
   * 执行批量删除
   */
  async performBatchDelete() {
    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 从列表中移除选中的文章
      const myPosts = this.data.myPosts.filter(post => !this.data.selectedItems.includes(post.id));

      this.setData({
        myPosts,
        selectedItems: [],
        isSelectionMode: false
      });

      wx.hideLoading();

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('批量删除失败:', error);

      wx.hideLoading();

      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 查看文章详情
   */
  viewPostDetail(e) {
    if (this.data.isSelectionMode) {
      this.toggleItemSelection(e);
      return;
    }

    const post = e.currentTarget.dataset.post;

    wx.navigateTo({
      url: `/pages/note-detail/index?id=${post.id}`
    });
  },

  /**
   * 编辑文章
   */
  editPost(e) {
    e.stopPropagation();

    const post = e.currentTarget.dataset.post;

    wx.navigateTo({
      url: `/pages/note-edit/index?id=${post.id}`
    });
  },

  /**
   * 删除文章
   */
  deletePost(e) {
    e.stopPropagation();

    const post = e.currentTarget.dataset.post;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${post.title}"吗？此操作不可恢复。`,
      success: res => {
        if (res.confirm) {
          this.performDelete(post.id);
        }
      }
    });
  },

  /**
   * 执行删除
   */
  async performDelete(postId) {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 从列表中移除文章
      const myPosts = this.data.myPosts.filter(post => post.id !== postId);

      this.setData({ myPosts });

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('删除失败:', error);

      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 发布/取消发布文章
   */
  togglePublish(e) {
    e.stopPropagation();

    const post = e.currentTarget.dataset.post;
    const isPublished = post.status === 'published';

    wx.showModal({
      title: isPublished ? '确认取消发布' : '确认发布',
      content: `确定要${isPublished ? '取消发布' : '发布'}"${post.title}"吗？`,
      success: res => {
        if (res.confirm) {
          this.performTogglePublish(post.id, !isPublished);
        }
      }
    });
  },

  /**
   * 执行发布/取消发布
   */
  async performTogglePublish(postId, shouldPublish) {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 更新文章状态
      const myPosts = this.data.myPosts.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            status: shouldPublish ? 'published' : 'draft',
            publishedAt: shouldPublish ? new Date().toISOString() : null
          };
        }
        return post;
      });

      this.setData({ myPosts });

      wx.showToast({
        title: shouldPublish ? '发布成功' : '取消发布成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('操作失败:', error);

      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 创建新文章
   */
  createNewPost() {
    wx.navigateTo({
      url: '/pages/note-edit/index'
    });
  },

  /**
   * 格式化状态名称
   */
  formatStatusName(status) {
    const statusMap = {
      published: '已发布',
      draft: '草稿',
      private: '私密'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 格式化时间
   */
  formatTime(timeString) {
    const time = new Date(timeString);
    const now = new Date();
    const diff = now - time;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 30) {
      return `${days}天前`;
    } else {
      return time.toLocaleDateString();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  async onPullDownRefresh() {
    this.setData({ isRefreshing: true });

    try {
      await this.loadMyPosts();
    } finally {
      this.setData({ isRefreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadMyPosts(true);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的发布 - AI互动泡泡',
      path: '/pages/profile/my-posts/index',
      imageUrl: '/assets/share-posts.png'
    };
  }
});
