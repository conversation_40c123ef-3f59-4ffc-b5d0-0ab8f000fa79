<!--pages/profile/edit-profile.wxml-->
<view class="container" id="page-container" data-theme="{{theme}}">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">编辑资料</text>
  </view>

  <!-- 头像编辑区域 -->
  <view class="avatar-section glass-card">
    <view class="avatar-container">
      <view class="avatar-wrapper" bindtap="chooseAvatar">
        <image class="avatar-image" src="{{userInfo.avatarUrl}}" wx:if="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view class="default-avatar" wx:else>
          <text class="default-avatar-text">{{userInfo.nickName ? userInfo.nickName.substring(0, 1).toUpperCase() : 'U'}}</text>
        </view>
        <view class="avatar-overlay">
          <image class="camera-icon" src="/assets/icons/camera.png"></image>
          <text class="avatar-tip">点击更换头像</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 基本信息编辑 -->
  <view class="form-section glass-card">
    <view class="section-title">基本信息</view>
    
    <!-- 昵称 -->
    <view class="form-item">
      <view class="form-label">昵称</view>
      <view class="form-input-wrapper">
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入昵称" 
          value="{{editData.nickName}}"
          bindinput="onNickNameInput"
          maxlength="20"
        />
        <text class="input-counter">{{editData.nickName.length}}/20</text>
      </view>
    </view>

    <!-- 个性签名 -->
    <view class="form-item">
      <view class="form-label">个性签名</view>
      <view class="form-input-wrapper">
        <textarea 
          class="form-textarea" 
          placeholder="写点什么介绍一下自己吧..." 
          value="{{editData.signature}}"
          bindinput="onSignatureInput"
          maxlength="100"
          auto-height
        ></textarea>
        <text class="input-counter">{{editData.signature.length}}/100</text>
      </view>
    </view>

    <!-- 性别 -->
    <view class="form-item">
      <view class="form-label">性别</view>
      <view class="form-input-wrapper">
        <picker 
          bindchange="onGenderChange" 
          value="{{genderIndex}}" 
          range="{{genderOptions}}"
        >
          <view class="picker-value">
            {{genderOptions[genderIndex]}}
            <text class="arrow-right">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 生日 -->
    <view class="form-item">
      <view class="form-label">生日</view>
      <view class="form-input-wrapper">
        <picker 
          mode="date" 
          value="{{editData.birthday}}" 
          start="1900-01-01" 
          end="{{currentDate}}"
          bindchange="onBirthdayChange"
        >
          <view class="picker-value">
            {{editData.birthday || '请选择生日'}}
            <text class="arrow-right">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 所在地区 -->
    <view class="form-item">
      <view class="form-label">所在地区</view>
      <view class="form-input-wrapper">
        <picker 
          mode="region" 
          value="{{editData.region}}" 
          bindchange="onRegionChange"
        >
          <view class="picker-value">
            {{regionText || '请选择地区'}}
            <text class="arrow-right">›</text>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 学习偏好设置 -->
  <view class="form-section glass-card">
    <view class="section-title">学习偏好</view>
    
    <!-- 学习目标 -->
    <view class="form-item">
      <view class="form-label">学习目标</view>
      <view class="form-input-wrapper">
        <input 
          class="form-input" 
          type="text" 
          placeholder="例如：提升编程技能" 
          value="{{editData.learningGoal}}"
          bindinput="onLearningGoalInput"
          maxlength="50"
        />
        <text class="input-counter">{{editData.learningGoal.length}}/50</text>
      </view>
    </view>

    <!-- 兴趣标签 -->
    <view class="form-item">
      <view class="form-label">兴趣标签</view>
      <view class="tags-container">
        <view class="tag-item {{item.selected ? 'selected' : ''}}" 
              wx:for="{{interestTags}}" 
              wx:key="id"
              bindtap="toggleInterestTag"
              data-index="{{index}}">
          {{item.name}}
        </view>
      </view>
      <text class="tags-tip">最多选择5个标签</text>
    </view>
  </view>

  <!-- 隐私设置 -->
  <view class="form-section glass-card">
    <view class="section-title">隐私设置</view>
    
    <!-- 资料可见性 -->
    <view class="form-item">
      <view class="form-label">资料可见性</view>
      <view class="form-input-wrapper">
        <picker 
          bindchange="onPrivacyChange" 
          value="{{privacyIndex}}" 
          range="{{privacyOptions}}"
        >
          <view class="picker-value">
            {{privacyOptions[privacyIndex]}}
            <text class="arrow-right">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 学习记录可见性 -->
    <view class="form-item">
      <view class="setting-item">
        <view class="setting-label">学习记录公开</view>
        <switch 
          checked="{{editData.showLearningRecord}}" 
          bindchange="onLearningRecordVisibilityChange" 
          color="#3B82F6" 
        />
      </view>
    </view>

    <!-- 在线状态显示 -->
    <view class="form-item">
      <view class="setting-item">
        <view class="setting-label">显示在线状态</view>
        <switch 
          checked="{{editData.showOnlineStatus}}" 
          bindchange="onOnlineStatusVisibilityChange" 
          color="#3B82F6" 
        />
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="btn-secondary" bindtap="resetForm">重置</button>
    <button class="btn-primary" bindtap="saveProfile" loading="{{isSaving}}">
      {{isSaving ? '保存中...' : '保存'}}
    </button>
  </view>

  <!-- 头像裁剪弹窗 -->
  <view class="avatar-crop-modal" wx:if="{{showCropModal}}">
    <view class="modal-overlay" bindtap="closeCropModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">裁剪头像</text>
        <view class="modal-close" bindtap="closeCropModal">×</view>
      </view>
      <view class="crop-container">
        <image class="crop-image" src="{{tempAvatarUrl}}" mode="aspectFit"></image>
        <view class="crop-overlay">
          <view class="crop-frame"></view>
        </view>
      </view>
      <view class="modal-actions">
        <button class="btn-secondary" bindtap="closeCropModal">取消</button>
        <button class="btn-primary" bindtap="confirmCrop">确定</button>
      </view>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view>
