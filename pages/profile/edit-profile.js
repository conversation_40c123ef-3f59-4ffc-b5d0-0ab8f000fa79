// pages/profile/edit-profile.js
// 导入API工具
const { userAPI } = require('../../utils/api');
// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,

    // 编辑数据
    editData: {
      nickName: '',
      signature: '',
      gender: 0, // 0: 未知, 1: 男, 2: 女
      birthday: '',
      region: [],
      learningGoal: '',
      showLearningRecord: true,
      showOnlineStatus: true
    },

    // 选择器数据
    genderOptions: ['保密', '男', '女'],
    genderIndex: 0,
    privacyOptions: ['公开', '仅好友可见', '仅自己可见'],
    privacyIndex: 0,

    // 兴趣标签
    interestTags: [
      { id: 1, name: '编程开发', selected: false },
      { id: 2, name: '产品设计', selected: false },
      { id: 3, name: '数据分析', selected: false },
      { id: 4, name: '人工智能', selected: false },
      { id: 5, name: '项目管理', selected: false },
      { id: 6, name: '市场营销', selected: false },
      { id: 7, name: '用户体验', selected: false },
      { id: 8, name: '商业分析', selected: false },
      { id: 9, name: '创业', selected: false },
      { id: 10, name: '投资理财', selected: false }
    ],

    // 状态控制
    isLoading: false,
    isSaving: false,
    loadingText: '加载中...',

    // 头像相关
    tempAvatarUrl: '',
    showCropModal: false,

    // 其他
    currentDate: '',
    regionText: '',
    theme: 'light'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // 设置当前日期
    const now = new Date();
    const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    this.setData({ currentDate });

    // 获取主题模式
    const app = getApp();
    const theme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme });

    // 加载用户信息
    await this.loadUserInfo();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新主题
    const app = getApp();
    const theme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme });
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      this.setData({
        isLoading: true,
        loadingText: '加载用户信息...'
      });

      // 获取当前用户信息
      const userInfo = await authService.getCurrentUser();

      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 设置用户信息和编辑数据
      const editData = {
        nickName: userInfo.nickName || userInfo.nickname || '',
        signature: userInfo.signature || '',
        gender: userInfo.gender || 0,
        birthday: userInfo.birthday || '',
        region: userInfo.region || [],
        learningGoal: userInfo.learningGoal || '',
        showLearningRecord: userInfo.showLearningRecord !== false,
        showOnlineStatus: userInfo.showOnlineStatus !== false
      };

      // 设置性别索引
      const genderIndex = Math.max(0, Math.min(2, editData.gender));

      // 设置地区文本
      const regionText = editData.region.length > 0 ? editData.region.join(' ') : '';

      // 设置兴趣标签
      const interestTags = this.data.interestTags.map(tag => ({
        ...tag,
        selected: userInfo.interestTags ? userInfo.interestTags.includes(tag.id) : false
      }));

      this.setData({
        userInfo,
        editData,
        genderIndex,
        regionText,
        interestTags,
        isLoading: false
      });

    } catch (error) {
      console.error('加载用户信息失败:', error);

      this.setData({ isLoading: false });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 昵称输入处理
   */
  onNickNameInput(e) {
    this.setData({
      'editData.nickName': e.detail.value
    });
  },

  /**
   * 个性签名输入处理
   */
  onSignatureInput(e) {
    this.setData({
      'editData.signature': e.detail.value
    });
  },

  /**
   * 学习目标输入处理
   */
  onLearningGoalInput(e) {
    this.setData({
      'editData.learningGoal': e.detail.value
    });
  },

  /**
   * 性别选择处理
   */
  onGenderChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      genderIndex: index,
      'editData.gender': index
    });
  },

  /**
   * 生日选择处理
   */
  onBirthdayChange(e) {
    this.setData({
      'editData.birthday': e.detail.value
    });
  },

  /**
   * 地区选择处理
   */
  onRegionChange(e) {
    const region = e.detail.value;
    const regionText = region.join(' ');

    this.setData({
      'editData.region': region,
      regionText
    });
  },

  /**
   * 隐私设置变更
   */
  onPrivacyChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      privacyIndex: index
    });
  },

  /**
   * 学习记录可见性变更
   */
  onLearningRecordVisibilityChange(e) {
    this.setData({
      'editData.showLearningRecord': e.detail.value
    });
  },

  /**
   * 在线状态可见性变更
   */
  onOnlineStatusVisibilityChange(e) {
    this.setData({
      'editData.showOnlineStatus': e.detail.value
    });
  },

  /**
   * 切换兴趣标签
   */
  toggleInterestTag(e) {
    const index = e.currentTarget.dataset.index;
    const interestTags = [...this.data.interestTags];

    // 检查已选择的标签数量
    const selectedCount = interestTags.filter(tag => tag.selected).length;

    if (!interestTags[index].selected && selectedCount >= 5) {
      wx.showToast({
        title: '最多只能选择5个标签',
        icon: 'none'
      });
      return;
    }

    interestTags[index].selected = !interestTags[index].selected;

    this.setData({ interestTags });
  },

  /**
   * 选择头像
   */
  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: res => {
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 显示裁剪弹窗
        this.setData({
          tempAvatarUrl: tempFilePath,
          showCropModal: true
        });
      },
      fail: error => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 关闭裁剪弹窗
   */
  closeCropModal() {
    this.setData({
      showCropModal: false,
      tempAvatarUrl: ''
    });
  },

  /**
   * 确认裁剪头像
   */
  async confirmCrop() {
    try {
      this.setData({
        isLoading: true,
        loadingText: '上传头像中...'
      });

      // 这里应该调用头像上传API
      // 暂时使用临时文件路径作为头像URL
      const avatarUrl = this.data.tempAvatarUrl;

      // 更新用户信息中的头像
      const userInfo = { ...this.data.userInfo };
      userInfo.avatarUrl = avatarUrl;

      this.setData({
        userInfo,
        showCropModal: false,
        tempAvatarUrl: '',
        isLoading: false
      });

      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('上传头像失败:', error);

      this.setData({ isLoading: false });

      wx.showToast({
        title: '上传头像失败',
        icon: 'none'
      });
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有修改吗？',
      success: res => {
        if (res.confirm) {
          this.loadUserInfo();
        }
      }
    });
  },

  /**
   * 保存用户资料
   */
  async saveProfile() {
    try {
      // 验证必填字段
      if (!this.data.editData.nickName.trim()) {
        wx.showToast({
          title: '请输入昵称',
          icon: 'none'
        });
        return;
      }

      this.setData({
        isSaving: true
      });

      // 准备更新数据
      const updateData = {
        nickname: this.data.editData.nickName.trim(),
        signature: this.data.editData.signature.trim(),
        gender: this.data.editData.gender,
        birthday: this.data.editData.birthday,
        region: this.data.editData.region,
        learningGoal: this.data.editData.learningGoal.trim(),
        showLearningRecord: this.data.editData.showLearningRecord,
        showOnlineStatus: this.data.editData.showOnlineStatus,
        interestTags: this.data.interestTags.filter(tag => tag.selected).map(tag => tag.id)
      };

      // 如果有新头像，添加到更新数据
      if (this.data.userInfo.avatarUrl !== this.data.userInfo.originalAvatarUrl) {
        updateData.avatarUrl = this.data.userInfo.avatarUrl;
      }

      // 调用更新API
      const result = await authService.updateUserInfo(updateData);

      if (result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result.error || '保存失败');
      }

    } catch (error) {
      console.error('保存用户资料失败:', error);

      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isSaving: false });
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadUserInfo().then(() => {
      wx.stopPullDownRefresh();
    });
  }
});