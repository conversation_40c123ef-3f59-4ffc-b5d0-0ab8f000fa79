// pages/profile/favorites/index.js
// 导入API工具
const { userAPI } = require('../../../utils/api');
// 导入认证服务
const authService = require('../../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 收藏数据
    favoriteItems: [],
    favoriteStats: {
      total: 0,
      notes: 0,
      exercises: 0,
      insights: 0,
      plans: 0
    },

    // 收藏夹分类
    categories: [
      { id: 'all', name: '全部', count: 0 },
      { id: 'notes', name: '笔记', count: 0 },
      { id: 'exercises', name: '练习', count: 0 },
      { id: 'insights', name: '观点', count: 0 },
      { id: 'plans', name: '计划', count: 0 }
    ],

    // 筛选和排序
    activeCategory: 'all',
    sortBy: 'createdAt', // createdAt, title, type
    sortOrder: 'desc', // asc, desc

    // 视图控制
    viewMode: 'list', // list, grid

    // 分页
    currentPage: 1,
    pageSize: 20,
    hasMore: true,

    // 状态控制
    isLoading: true,
    isLoadingMore: false,
    isRefreshing: false,
    loadingFailed: false,

    // 批量操作
    isSelectionMode: false,
    selectedItems: [],

    // 其他
    theme: 'light'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // 获取主题模式
    const app = getApp();
    const theme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme });

    // 检查登录状态
    const isLoggedIn = await authService.isLoggedIn();
    if (!isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 加载收藏数据
    await this.loadFavorites();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新主题
    const app = getApp();
    const theme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme });
  },

  /**
   * 加载收藏数据
   */
  async loadFavorites(isLoadMore = false) {
    try {
      if (!isLoadMore) {
        this.setData({
          isLoading: true,
          loadingFailed: false,
          currentPage: 1
        });
      } else {
        this.setData({ isLoadingMore: true });
      }

      // 模拟API调用 - 实际应该调用真实的收藏API
      const mockFavorites = this.generateMockFavorites();

      // 根据分类筛选
      let filteredItems = mockFavorites;
      if (this.data.activeCategory !== 'all') {
        filteredItems = mockFavorites.filter(item => item.type === this.data.activeCategory);
      }

      // 排序
      filteredItems = this.sortItems(filteredItems);

      // 分页处理
      const startIndex = (this.data.currentPage - 1) * this.data.pageSize;
      const endIndex = startIndex + this.data.pageSize;
      const pageItems = filteredItems.slice(startIndex, endIndex);

      // 更新数据
      const favoriteItems = isLoadMore ?
        [...this.data.favoriteItems, ...pageItems] :
        pageItems;

      // 计算统计数据
      const favoriteStats = this.calculateStats(mockFavorites);

      // 更新分类计数
      const categories = this.data.categories.map(cat => ({
        ...cat,
        count: cat.id === 'all' ?
          mockFavorites.length :
          mockFavorites.filter(item => item.type === cat.id).length
      }));

      this.setData({
        favoriteItems,
        favoriteStats,
        categories,
        hasMore: endIndex < filteredItems.length,
        isLoading: false,
        isLoadingMore: false,
        currentPage: isLoadMore ? this.data.currentPage + 1 : 1
      });

    } catch (error) {
      console.error('加载收藏数据失败:', error);

      this.setData({
        isLoading: false,
        isLoadingMore: false,
        loadingFailed: true
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 生成模拟收藏数据
   */
  generateMockFavorites() {
    const types = ['notes', 'exercises', 'insights', 'plans'];
    const mockData = [];

    for (let i = 1; i <= 50; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      const createdAt = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);

      mockData.push({
        id: i,
        type,
        title: this.generateMockTitle(type, i),
        description: this.generateMockDescription(type),
        coverImage: Math.random() > 0.5 ? `/assets/images/mock-cover-${i % 5 + 1}.png` : null,
        author: `用户${Math.floor(Math.random() * 100) + 1}`,
        createdAt: createdAt.toISOString(),
        favoriteAt: new Date(createdAt.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        tags: this.generateMockTags(),
        stats: {
          likes: Math.floor(Math.random() * 100),
          comments: Math.floor(Math.random() * 50),
          views: Math.floor(Math.random() * 1000)
        }
      });
    }

    return mockData;
  },

  /**
   * 生成模拟标题
   */
  generateMockTitle(type, index) {
    const titles = {
      notes: [
        '学习笔记：如何提高沟通技巧',
        '产品设计思考：用户体验的重要性',
        '技术分享：前端开发最佳实践',
        '读书笔记：《高效能人士的七个习惯》',
        '工作总结：项目管理经验分享'
      ],
      exercises: [
        '沟通技巧练习：倾听的艺术',
        '表达能力训练：如何清晰表达观点',
        '同理心练习：理解他人的感受',
        '反馈技巧：给出建设性意见',
        '赞美练习：发现他人的优点'
      ],
      insights: [
        '关于团队协作的思考',
        '产品创新的关键要素',
        '用户研究的重要发现',
        '市场趋势分析与预测',
        '技术发展的未来方向'
      ],
      plans: [
        '30天沟通技能提升计划',
        '产品经理成长路径',
        '前端技术学习规划',
        '个人品牌建设方案',
        '职业发展五年计划'
      ]
    };

    const typeTitle = titles[type] || titles.notes;
    return typeTitle[index % typeTitle.length];
  },

  /**
   * 生成模拟描述
   */
  generateMockDescription(type) {
    const descriptions = {
      notes: '这是一篇关于学习和成长的笔记，包含了实用的技巧和深入的思考...',
      exercises: '通过这个练习，你可以提升相关技能，获得实际的经验和反馈...',
      insights: '基于实践经验和深入思考，分享一些有价值的观点和见解...',
      plans: '详细的学习计划，包含明确的目标、时间安排和执行步骤...'
    };

    return descriptions[type] || descriptions.notes;
  },

  /**
   * 生成模拟标签
   */
  generateMockTags() {
    const allTags = ['学习', '成长', '技能', '沟通', '产品', '技术', '管理', '创新'];
    const tagCount = Math.floor(Math.random() * 3) + 1;
    const selectedTags = [];

    for (let i = 0; i < tagCount; i++) {
      const randomTag = allTags[Math.floor(Math.random() * allTags.length)];
      if (!selectedTags.includes(randomTag)) {
        selectedTags.push(randomTag);
      }
    }

    return selectedTags;
  },

  /**
   * 计算统计数据
   */
  calculateStats(items) {
    return {
      total: items.length,
      notes: items.filter(item => item.type === 'notes').length,
      exercises: items.filter(item => item.type === 'exercises').length,
      insights: items.filter(item => item.type === 'insights').length,
      plans: items.filter(item => item.type === 'plans').length
    };
  },

  /**
   * 排序项目
   */
  sortItems(items) {
    return items.sort((a, b) => {
      let aValue, bValue;

      switch (this.data.sortBy) {
        case 'title':
          aValue = a.title;
          bValue = b.title;
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'createdAt':
        default:
          aValue = new Date(a.favoriteAt);
          bValue = new Date(b.favoriteAt);
          break;
      }

      if (this.data.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  },

  /**
   * 切换分类
   */
  switchCategory(e) {
    const category = e.currentTarget.dataset.category;

    if (category === this.data.activeCategory) return;

    this.setData({
      activeCategory: category,
      currentPage: 1
    });

    this.loadFavorites();
  },

  /**
   * 切换视图模式
   */
  switchViewMode() {
    const newMode = this.data.viewMode === 'list' ? 'grid' : 'list';
    this.setData({ viewMode: newMode });
  },

  /**
   * 切换排序方式
   */
  switchSort() {
    wx.showActionSheet({
      itemList: ['按收藏时间', '按标题', '按类型'],
      success: res => {
        const sortOptions = ['createdAt', 'title', 'type'];
        const newSortBy = sortOptions[res.tapIndex];

        if (newSortBy === this.data.sortBy) {
          // 如果是同一个排序字段，切换排序顺序
          this.setData({
            sortOrder: this.data.sortOrder === 'asc' ? 'desc' : 'asc'
          });
        } else {
          // 如果是不同排序字段，使用默认顺序
          this.setData({
            sortBy: newSortBy,
            sortOrder: 'desc'
          });
        }

        this.loadFavorites();
      }
    });
  },

  /**
   * 切换选择模式
   */
  toggleSelectionMode() {
    this.setData({
      isSelectionMode: !this.data.isSelectionMode,
      selectedItems: []
    });
  },

  /**
   * 选择/取消选择项目
   */
  toggleItemSelection(e) {
    if (!this.data.isSelectionMode) return;

    const itemId = e.currentTarget.dataset.itemId;
    const selectedItems = [...this.data.selectedItems];
    const index = selectedItems.indexOf(itemId);

    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(itemId);
    }

    this.setData({ selectedItems });
  },

  /**
   * 全选/取消全选
   */
  toggleSelectAll() {
    const allSelected = this.data.selectedItems.length === this.data.favoriteItems.length;

    if (allSelected) {
      this.setData({ selectedItems: [] });
    } else {
      const allIds = this.data.favoriteItems.map(item => item.id);
      this.setData({ selectedItems: allIds });
    }
  },

  /**
   * 批量取消收藏
   */
  batchUnfavorite() {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要取消收藏的项目',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认取消收藏',
      content: `确定要取消收藏选中的 ${this.data.selectedItems.length} 个项目吗？`,
      success: res => {
        if (res.confirm) {
          this.performBatchUnfavorite();
        }
      }
    });
  },

  /**
   * 执行批量取消收藏
   */
  async performBatchUnfavorite() {
    try {
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 从列表中移除选中的项目
      const favoriteItems = this.data.favoriteItems.filter(
        item => !this.data.selectedItems.includes(item.id)
      );

      this.setData({
        favoriteItems,
        selectedItems: [],
        isSelectionMode: false
      });

      wx.hideLoading();

      wx.showToast({
        title: '取消收藏成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('批量取消收藏失败:', error);

      wx.hideLoading();

      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 查看项目详情
   */
  viewItemDetail(e) {
    if (this.data.isSelectionMode) {
      this.toggleItemSelection(e);
      return;
    }

    const item = e.currentTarget.dataset.item;

    // 根据类型跳转到相应页面
    switch (item.type) {
      case 'notes':
        wx.navigateTo({
          url: `/pages/note-detail/index?id=${item.id}`
        });
        break;
      case 'exercises':
        wx.navigateTo({
          url: `/pages/exercise-detail/index?id=${item.id}`
        });
        break;
      case 'insights':
        wx.navigateTo({
          url: `/pages/insight-detail/index?id=${item.id}`
        });
        break;
      case 'plans':
        wx.navigateTo({
          url: `/pages/plan-detail/index?id=${item.id}`
        });
        break;
      default:
        wx.showToast({
          title: '页面开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 取消收藏单个项目
   */
  unfavoriteItem(e) {
    e.stopPropagation();

    const item = e.currentTarget.dataset.item;

    wx.showModal({
      title: '确认取消收藏',
      content: `确定要取消收藏"${item.title}"吗？`,
      success: res => {
        if (res.confirm) {
          this.performUnfavorite(item.id);
        }
      }
    });
  },

  /**
   * 执行取消收藏
   */
  async performUnfavorite(itemId) {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 从列表中移除项目
      const favoriteItems = this.data.favoriteItems.filter(item => item.id !== itemId);

      this.setData({ favoriteItems });

      wx.showToast({
        title: '取消收藏成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('取消收藏失败:', error);

      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化类型名称
   */
  formatTypeName(type) {
    const typeMap = {
      'notes': '笔记',
      'exercises': '练习',
      'insights': '观点',
      'plans': '计划'
    };
    return typeMap[type] || '未知';
  },

  /**
   * 格式化时间
   */
  formatTime(timeString) {
    const time = new Date(timeString);
    const now = new Date();
    const diff = now - time;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 30) {
      return `${days}天前`;
    } else {
      return time.toLocaleDateString();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  async onPullDownRefresh() {
    this.setData({ isRefreshing: true });

    try {
      await this.loadFavorites();
    } finally {
      this.setData({ isRefreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadFavorites(true);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的收藏 - AI互动泡泡',
      path: '/pages/profile/favorites/index',
      imageUrl: '/assets/share-favorites.png'
    };
  }
});