/* pages/profile/favorites/index.wxss */

page {
  background: linear-gradient(180deg, #e0f2ff 0%, #f0e6ff 100%);
  min-height: 100vh;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding: 0 0 40rpx 0;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  width: 100%;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
  margin-bottom: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  width: 100%;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 20rpx;
}

.retry-button {
  background-color: #3B82F6;
  color: #ffffff;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  border: none;
}

/* 玻璃卡片基础样式 */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: 30rpx;
  margin: 16rpx 16rpx 20rpx 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  width: calc(100% - 32rpx);
  box-sizing: border-box;
}

/* 统计卡片 */
.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.stats-total {
  font-size: 24rpx;
  color: #666666;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #3B82F6;
  margin-bottom: 6rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

/* 分类筛选 */
.category-filter {
  margin: 0 16rpx 16rpx 16rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-item {
  display: inline-block;
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 26rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.category-item.active {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3B82F6;
  color: #3B82F6;
  font-weight: 600;
}

.category-name {
  margin-right: 8rpx;
}

.category-count {
  font-size: 22rpx;
  opacity: 0.8;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx 16rpx 16rpx;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.view-mode-toggle,
.sort-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 24rpx;
  color: #666666;
}

.mode-icon,
.sort-icon {
  width: 32rpx;
  height: 32rpx;
}

.selection-toggle {
  padding: 12rpx 20rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 20rpx;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.selection-text {
  font-size: 24rpx;
  color: #3B82F6;
}

/* 批量操作栏 */
.batch-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  margin: 0 16rpx 16rpx 16rpx;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 16rpx;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.batch-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.select-all {
  padding: 8rpx 16rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 16rpx;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.select-all-text {
  font-size: 24rpx;
  color: #3B82F6;
}

.selected-count {
  font-size: 24rpx;
  color: #666666;
}

.batch-button {
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}

.batch-button.unfavorite {
  background: #ff4d4f;
  color: #ffffff;
}

/* 收藏列表容器 */
.favorites-container {
  margin: 0 16rpx;
}

/* 列表视图 */
.favorites-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.favorite-item {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.favorite-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.35);
}

.favorite-item.selection-mode {
  padding-left: 80rpx;
}

.favorite-item.selected {
  border-color: #3B82F6;
  background: rgba(59, 130, 246, 0.05);
}

.selection-checkbox {
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #cccccc;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #3B82F6;
  border-color: #3B82F6;
}

.checkbox-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

.item-content {
  display: flex;
  padding: 24rpx;
  gap: 20rpx;
}

.item-cover {
  position: relative;
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.default-cover {
  width: 100%;
  height: 100%;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-icon {
  font-size: 48rpx;
}

.type-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-description {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag {
  font-size: 20rpx;
  color: #3B82F6;
  background: rgba(59, 130, 246, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: auto;
}

.meta-author,
.meta-time {
  font-size: 22rpx;
  color: #999999;
}

.item-stats {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 8rpx;
}

.item-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 0;
  background: none;
  border: none;
}

.stat-icon {
  width: 24rpx;
  height: 24rpx;
}

.stat-text {
  font-size: 20rpx;
  color: #999999;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.action-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-button:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.9);
}

.unfavorite-btn {
  background: rgba(255, 77, 79, 0.1);
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 网格视图 */
.favorites-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.grid-item {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.grid-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.35);
}

.grid-item.selected {
  border-color: #3B82F6;
  background: rgba(59, 130, 246, 0.05);
}

.grid-selection-checkbox {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  z-index: 10;
}

.grid-cover {
  position: relative;
  width: 100%;
  height: 200rpx;
}

.grid-cover-image {
  width: 100%;
  height: 100%;
}

.grid-default-cover {
  width: 100%;
  height: 100%;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-cover-icon {
  font-size: 60rpx;
}

.grid-type-badge {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.grid-unfavorite-btn {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 77, 79, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.grid-unfavorite-btn:active {
  transform: scale(0.9);
}

.grid-unfavorite-icon {
  width: 24rpx;
  height: 24rpx;
}

.grid-info {
  padding: 20rpx;
}

.grid-title {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.grid-author {
  display: block;
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.grid-time {
  font-size: 20rpx;
  color: #999999;
}

/* 空状态 */
.empty-favorites {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

.explore-button {
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  color: #ffffff;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(59, 130, 246, 0.3);
}

/* 加载更多 */
.load-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #999999;
}

/* 深色模式适配 */
page[data-theme="dark"] {
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
}

page[data-theme="dark"] .glass-card,
page[data-theme="dark"] .favorite-item,
page[data-theme="dark"] .grid-item {
  background: rgba(30, 30, 40, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

page[data-theme="dark"] .stats-title,
page[data-theme="dark"] .item-title,
page[data-theme="dark"] .grid-title,
page[data-theme="dark"] .empty-title {
  color: #ffffff;
}

page[data-theme="dark"] .stats-total,
page[data-theme="dark"] .item-description,
page[data-theme="dark"] .grid-author,
page[data-theme="dark"] .meta-author,
page[data-theme="dark"] .meta-time,
page[data-theme="dark"] .grid-time,
page[data-theme="dark"] .empty-desc {
  color: #cccccc;
}

page[data-theme="dark"] .stat-item,
page[data-theme="dark"] .category-item,
page[data-theme="dark"] .view-mode-toggle,
page[data-theme="dark"] .sort-button {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.1);
}

page[data-theme="dark"] .category-item.active {
  background: rgba(59, 130, 246, 0.2);
}

page[data-theme="dark"] .default-cover,
page[data-theme="dark"] .grid-default-cover {
  background: rgba(59, 130, 246, 0.2);
}

page[data-theme="dark"] .checkbox {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

page[data-theme="dark"] .action-button {
  background: rgba(255, 255, 255, 0.1);
}