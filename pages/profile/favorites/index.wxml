<!--pages/profile/favorites/index.wxml-->
<view class="container" data-theme="{{theme}}">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载收藏数据中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:elif="{{loadingFailed}}">
    <view class="error-icon">!</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="loadFavorites">重试</button>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 统计信息 -->
    <view class="glass-card stats-card">
      <view class="stats-header">
        <text class="stats-title">我的收藏</text>
        <text class="stats-total">共 {{favoriteStats.total}} 项</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{favoriteStats.notes}}</text>
          <text class="stat-label">笔记</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{favoriteStats.exercises}}</text>
          <text class="stat-label">练习</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{favoriteStats.insights}}</text>
          <text class="stat-label">观点</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{favoriteStats.plans}}</text>
          <text class="stat-label">计划</text>
        </view>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view class="category-filter">
      <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
        <view class="category-item {{activeCategory === item.id ? 'active' : ''}}"
              wx:for="{{categories}}" wx:key="id"
              bindtap="switchCategory" data-category="{{item.id}}">
          <text class="category-name">{{item.name}}</text>
          <text class="category-count" wx:if="{{item.count > 0}}">({{item.count}})</text>
        </view>
      </scroll-view>
    </view>

    <!-- 工具栏 -->
    <view class="toolbar">
      <view class="toolbar-left">
        <view class="view-mode-toggle" bindtap="switchViewMode">
          <image class="mode-icon" src="{{viewMode === 'list' ? '/assets/icons/grid.png' : '/assets/icons/list.png'}}"></image>
        </view>
        <view class="sort-button" bindtap="switchSort">
          <image class="sort-icon" src="/assets/icons/sort.png"></image>
          <text class="sort-text">排序</text>
        </view>
      </view>
      <view class="toolbar-right">
        <view class="selection-toggle" bindtap="toggleSelectionMode">
          <text class="selection-text">{{isSelectionMode ? '取消' : '选择'}}</text>
        </view>
      </view>
    </view>

    <!-- 批量操作栏 -->
    <view class="batch-actions" wx:if="{{isSelectionMode}}">
      <view class="batch-left">
        <view class="select-all" bindtap="toggleSelectAll">
          <text class="select-all-text">
            {{selectedItems.length === favoriteItems.length ? '取消全选' : '全选'}}
          </text>
        </view>
        <text class="selected-count">已选择 {{selectedItems.length}} 项</text>
      </view>
      <view class="batch-right">
        <button class="batch-button unfavorite" bindtap="batchUnfavorite">
          取消收藏
        </button>
      </view>
    </view>

    <!-- 收藏列表 -->
    <view class="favorites-container">
      <!-- 列表视图 -->
      <view class="favorites-list" wx:if="{{viewMode === 'list'}}">
        <view class="favorite-item {{isSelectionMode ? 'selection-mode' : ''}} {{selectedItems.includes(item.id) ? 'selected' : ''}}"
              wx:for="{{favoriteItems}}" wx:key="id"
              bindtap="viewItemDetail" data-item="{{item}}" data-item-id="{{item.id}}">
          
          <!-- 选择框 -->
          <view class="selection-checkbox" wx:if="{{isSelectionMode}}">
            <view class="checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}">
              <text class="checkbox-icon" wx:if="{{selectedItems.includes(item.id)}}">✓</text>
            </view>
          </view>

          <!-- 内容区域 -->
          <view class="item-content">
            <!-- 封面图片 -->
            <view class="item-cover">
              <image class="cover-image" src="{{item.coverImage}}" wx:if="{{item.coverImage}}" mode="aspectFill"></image>
              <view class="default-cover" wx:else>
                <text class="cover-icon">{{item.type === 'notes' ? '📝' : item.type === 'exercises' ? '🏃' : item.type === 'insights' ? '💡' : '📚'}}</text>
              </view>
              <view class="type-badge">{{formatTypeName(item.type)}}</view>
            </view>

            <!-- 详情信息 -->
            <view class="item-details">
              <text class="item-title">{{item.title}}</text>
              <text class="item-description">{{item.description}}</text>
              
              <!-- 标签 -->
              <view class="item-tags" wx:if="{{item.tags && item.tags.length > 0}}">
                <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">#{{tag}}</text>
              </view>

              <!-- 元信息 -->
              <view class="item-meta">
                <text class="meta-author">{{item.author}}</text>
                <text class="meta-time">收藏于 {{formatTime(item.favoriteAt)}}</text>
              </view>

              <!-- 统计信息 -->
              <view class="item-stats">
                <view class="stat-item">
                  <image class="stat-icon" src="/assets/icons/like.png"></image>
                  <text class="stat-text">{{item.stats.likes}}</text>
                </view>
                <view class="stat-item">
                  <image class="stat-icon" src="/assets/icons/comment.png"></image>
                  <text class="stat-text">{{item.stats.comments}}</text>
                </view>
                <view class="stat-item">
                  <image class="stat-icon" src="/assets/icons/view.png"></image>
                  <text class="stat-text">{{item.stats.views}}</text>
                </view>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="item-actions" wx:if="{{!isSelectionMode}}">
              <view class="action-button unfavorite-btn" 
                    bindtap="unfavoriteItem" data-item="{{item}}">
                <image class="action-icon" src="/assets/icons/unfavorite.png"></image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 网格视图 -->
      <view class="favorites-grid" wx:elif="{{viewMode === 'grid'}}">
        <view class="grid-item {{isSelectionMode ? 'selection-mode' : ''}} {{selectedItems.includes(item.id) ? 'selected' : ''}}"
              wx:for="{{favoriteItems}}" wx:key="id"
              bindtap="viewItemDetail" data-item="{{item}}" data-item-id="{{item.id}}">
          
          <!-- 选择框 -->
          <view class="grid-selection-checkbox" wx:if="{{isSelectionMode}}">
            <view class="checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}">
              <text class="checkbox-icon" wx:if="{{selectedItems.includes(item.id)}}">✓</text>
            </view>
          </view>

          <!-- 封面 -->
          <view class="grid-cover">
            <image class="grid-cover-image" src="{{item.coverImage}}" wx:if="{{item.coverImage}}" mode="aspectFill"></image>
            <view class="grid-default-cover" wx:else>
              <text class="grid-cover-icon">{{item.type === 'notes' ? '📝' : item.type === 'exercises' ? '🏃' : item.type === 'insights' ? '💡' : '📚'}}</text>
            </view>
            <view class="grid-type-badge">{{formatTypeName(item.type)}}</view>
            
            <!-- 取消收藏按钮 -->
            <view class="grid-unfavorite-btn" wx:if="{{!isSelectionMode}}"
                  bindtap="unfavoriteItem" data-item="{{item}}">
              <image class="grid-unfavorite-icon" src="/assets/icons/unfavorite.png"></image>
            </view>
          </view>

          <!-- 信息 -->
          <view class="grid-info">
            <text class="grid-title">{{item.title}}</text>
            <text class="grid-author">{{item.author}}</text>
            <text class="grid-time">{{formatTime(item.favoriteAt)}}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-favorites" wx:if="{{favoriteItems.length === 0 && !isLoading}}">
        <text class="empty-icon">⭐</text>
        <text class="empty-title">还没有收藏内容</text>
        <text class="empty-desc">去发现一些有趣的内容并收藏吧</text>
        <button class="explore-button" bindtap="navigateToSquare">去广场看看</button>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{isLoadingMore}}">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && favoriteItems.length > 0}}">
        <text class="no-more-text">没有更多内容了</text>
      </view>
    </view>
  </view>
</view>