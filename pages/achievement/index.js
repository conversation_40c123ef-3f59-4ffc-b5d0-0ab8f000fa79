// pages/achievement/index.js
// 成就系统页面

// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    currentUser: null,

    // 成就数据
    achievements: [],
    badges: [],

    // 页面状态
    isLoading: true,
    loadingFailed: false,

    // 筛选和显示选项
    currentCategory: 'all', // all, learning, social, creation, special
    currentDifficulty: 'all', // all, easy, medium, hard, expert
    showLocked: true,
    displayType: 'grid', // grid, list

    // 统计信息
    stats: {
      totalAchievements: 0,
      unlockedAchievements: 0,
      totalPoints: 0,
      completionRate: 0
    },

    // 分类选项
    categories: [
      { key: 'all', name: '全部', icon: '🏆' },
      { key: 'learning', name: '学习', icon: '📚' },
      { key: 'social', name: '社交', icon: '👥' },
      { key: 'creation', name: '创作', icon: '✍️' },
      { key: 'special', name: '特殊', icon: '⭐' }
    ],

    // 难度选项
    difficulties: [
      { key: 'all', name: '全部', color: '#95a5a6' },
      { key: 'easy', name: '简单', color: '#2ecc71' },
      { key: 'medium', name: '中等', color: '#f39c12' },
      { key: 'hard', name: '困难', color: '#e74c3c' },
      { key: 'expert', name: '专家', color: '#9b59b6' }
    ],

    // 主题模式
    isDarkMode: false,

    // 筛选后的成就数据
    filteredAchievements: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取主题模式
    const themeMode = wx.getStorageSync('themeMode') || 'light';
    const isDarkMode = themeMode === 'dark' ||
                      (themeMode === 'system' && wx.getSystemInfoSync().theme === 'dark');

    this.setData({ isDarkMode });

    // 加载当前用户信息
    this.loadCurrentUser();

    // 加载成就数据
    this.loadAchievements();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示时刷新数据
    this.loadAchievements();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadAchievements().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载当前用户信息
   */
  async loadCurrentUser() {
    try {
      const isLoggedIn = await authService.isLoggedIn();
      if (isLoggedIn) {
        const userInfo = await authService.getCurrentUser();
        this.setData({ currentUser: userInfo });
      } else {
        // 未登录，跳转到登录页面
        wx.navigateTo({
          url: '/pages/login/phone'
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  /**
   * 加载成就数据
   */
  async loadAchievements() {
    if (!this.data.currentUser) {
      return;
    }

    this.setData({ isLoading: true, loadingFailed: false });

    try {
      // 获取API客户端
      const app = getApp();
      const api = app.globalData.api;

      if (!api) {
        throw new Error('API客户端未初始化');
      }

      // 并行加载成就和徽章数据
      const [achievementsResponse, badgesResponse] = await Promise.all([
        api.user.getUserAchievements(),
        api.user.getUserBadges()
      ]);

      if (achievementsResponse.success && badgesResponse.success) {
        const achievements = achievementsResponse.data.achievements || [];
        const badges = badgesResponse.data.badges || [];

        // 计算统计信息
        const stats = this.calculateStats(achievements);

        this.setData({
          achievements,
          badges,
          stats,
          isLoading: false
        });

        // 更新筛选后的数据
        this.updateFilteredAchievements();
      } else {
        throw new Error('获取成就数据失败');
      }
    } catch (error) {
      console.error('加载成就数据失败:', error);

      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 计算统计信息
   */
  calculateStats(achievements) {
    const totalAchievements = achievements.length;
    const unlockedAchievements = achievements.filter(item => item.unlocked).length;
    const totalPoints = achievements
      .filter(item => item.unlocked)
      .reduce((sum, item) => sum + (item.points || 0), 0);
    const completionRate = totalAchievements > 0
      ? Math.round((unlockedAchievements / totalAchievements) * 100)
      : 0;

    return {
      totalAchievements,
      unlockedAchievements,
      totalPoints,
      completionRate
    };
  },

  /**
   * 筛选成就数据
   */
  getFilteredAchievements() {
    let filtered = [...this.data.achievements];

    // 按分类筛选
    if (this.data.currentCategory !== 'all') {
      filtered = filtered.filter(item => item.category === this.data.currentCategory);
    }

    // 按难度筛选
    if (this.data.currentDifficulty !== 'all') {
      filtered = filtered.filter(item => item.difficulty === this.data.currentDifficulty);
    }

    // 是否显示未解锁的成就
    if (!this.data.showLocked) {
      filtered = filtered.filter(item => item.unlocked);
    }

    return filtered;
  },

  /**
   * 更新筛选后的成就数据
   */
  updateFilteredAchievements() {
    const filteredAchievements = this.getFilteredAchievements();
    this.setData({ filteredAchievements });
  },

  /**
   * 切换分类
   */
  switchCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ currentCategory: category });
    this.updateFilteredAchievements();
  },

  /**
   * 切换难度
   */
  switchDifficulty(e) {
    const difficulty = e.currentTarget.dataset.difficulty;
    this.setData({ currentDifficulty: difficulty });
    this.updateFilteredAchievements();
  },

  /**
   * 切换显示类型
   */
  switchDisplayType() {
    const newType = this.data.displayType === 'grid' ? 'list' : 'grid';
    this.setData({ displayType: newType });
  },

  /**
   * 切换是否显示未解锁成就
   */
  toggleShowLocked() {
    this.setData({ showLocked: !this.data.showLocked });
    this.updateFilteredAchievements();
  },

  /**
   * 处理成就点击事件
   */
  handleAchievementClick(e) {
    const { type, item } = e.detail;

    if (type === 'achievement') {
      this.showAchievementDetail(item);
    } else if (type === 'badge') {
      this.showBadgeDetail(item);
    }
  },

  /**
   * 显示成就详情
   */
  showAchievementDetail(achievement) {
    const content = achievement.unlocked
      ? `🎉 恭喜获得成就！\n\n${achievement.description}\n\n获得时间：${this.formatDate(achievement.unlockedAt)}\n获得积分：${achievement.points || 0}`
      : `🔒 未解锁成就\n\n${achievement.description}\n\n解锁条件：${this.getConditionText(achievement)}\n奖励积分：${achievement.points || 0}`;

    wx.showModal({
      title: achievement.name,
      content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 显示徽章详情
   */
  showBadgeDetail(badge) {
    const content = badge.unlocked
      ? `🏅 恭喜获得徽章！\n\n${badge.description}\n\n获得时间：${this.formatDate(badge.unlockedAt)}`
      : `🔒 未解锁徽章\n\n${badge.description}\n\n解锁条件：${this.getConditionText(badge)}`;

    wx.showModal({
      title: badge.name,
      content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 获取解锁条件文本
   */
  getConditionText(item) {
    if (!item.conditionType || !item.conditionValue) {
      return '暂无详细条件';
    }

    // 根据条件类型生成描述文本
    switch (item.conditionType) {
      case 'complete_exercises':
        return `完成 ${item.conditionValue.count || 0} 个练习`;
      case 'consecutive_days':
        return `连续学习 ${item.conditionValue.days || 0} 天`;
      case 'total_points':
        return `累计获得 ${item.conditionValue.points || 0} 积分`;
      case 'create_notes':
        return `创建 ${item.conditionValue.count || 0} 篇笔记`;
      default:
        return '完成特定任务';
    }
  },

  /**
   * 格式化日期
   */
  formatDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  /**
   * 重新加载
   */
  retryLoad() {
    this.loadAchievements();
  },

  /**
   * 分享成就
   */
  shareAchievements() {
    const { stats } = this.data;
    return {
      title: `我在AIBUBB获得了${stats.unlockedAchievements}个成就，累计${stats.totalPoints}积分！`,
      path: '/pages/achievement/index',
      imageUrl: ''
    };
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    return this.shareAchievements();
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return this.shareAchievements();
  }
});
