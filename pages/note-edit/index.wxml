<!-- pages/note-edit/index.wxml -->
<view class="note-edit-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 编辑表单 -->
  <view class="edit-form" wx:else>
    <!-- 草稿状态提示 -->
    <view class="draft-status" wx:if="{{isDraft}}">
      <text class="draft-icon">📝</text>
      <text class="draft-text">草稿已保存</text>
      <text class="draft-time" wx:if="{{lastSaveTime}}">{{lastSaveTime}}</text>
    </view>

    <!-- 标题输入 -->
    <view class="form-item">
      <input
        class="title-input"
        placeholder="请输入标题"
        value="{{title}}"
        bindinput="handleTitleInput"
        maxlength="100"
      />
      <view class="word-count">{{title.length}}/100</view>
    </view>

    <!-- 编辑器工具栏 -->
    <view class="editor-toolbar" wx:if="{{showToolbar}}">
      <view class="toolbar-group">
        <button class="toolbar-btn" data-format="bold" bindtap="insertFormat">
          <text class="toolbar-icon">B</text>
        </button>
        <button class="toolbar-btn" data-format="italic" bindtap="insertFormat">
          <text class="toolbar-icon">I</text>
        </button>
        <button class="toolbar-btn" data-format="heading" bindtap="insertFormat">
          <text class="toolbar-icon">H</text>
        </button>
        <button class="toolbar-btn" data-format="list" bindtap="insertFormat">
          <text class="toolbar-icon">•</text>
        </button>
        <button class="toolbar-btn" data-format="link" bindtap="insertFormat">
          <text class="toolbar-icon">🔗</text>
        </button>
        <button class="toolbar-btn" data-format="quote" bindtap="insertFormat">
          <text class="toolbar-icon">"</text>
        </button>
      </view>
    </view>

    <!-- 内容输入区域 -->
    <view class="content-section">
      <!-- 编辑模式 -->
      <view class="content-edit" wx:if="{{editorMode === 'edit'}}">
        <textarea
          class="content-input"
          placeholder="请输入内容，支持Markdown格式"
          value="{{content}}"
          bindinput="handleContentInput"
          maxlength="10000"
          auto-height
          show-confirm-bar="{{false}}"
          cursor="{{cursorPosition}}"
        />
        <view class="content-actions">
          <button class="action-btn" bindtap="toggleToolbar">
            <text class="action-icon">🛠</text>
            <text class="action-text">工具</text>
          </button>
          <button class="action-btn" bindtap="selectImage">
            <text class="action-icon">📷</text>
            <text class="action-text">图片</text>
          </button>
          <button class="action-btn" bindtap="toggleEditorMode">
            <text class="action-icon">👁</text>
            <text class="action-text">预览</text>
          </button>
        </view>
      </view>

      <!-- 预览模式 -->
      <view class="content-preview" wx:if="{{editorMode === 'preview'}}">
        <view class="preview-content">
          <text class="preview-text">{{content}}</text>
        </view>
        <button class="preview-back-btn" bindtap="toggleEditorMode">
          <text class="btn-text">返回编辑</text>
        </button>
      </view>

      <!-- 字数统计 -->
      <view class="word-count-bar">
        <text class="word-count-text">{{wordCount}}/10000字</text>
        <text class="save-status" wx:if="{{hasUnsavedChanges}}">未保存</text>
        <text class="save-status saved" wx:else>已保存</text>
      </view>
    </view>

    <!-- 图片管理 -->
    <view class="images-section" wx:if="{{images.length > 0 || isUploadingImage}}">
      <view class="section-title">图片 ({{images.length}}/9)</view>
      <view class="images-grid">
        <view
          class="image-item"
          wx:for="{{images}}"
          wx:key="url"
          data-index="{{index}}"
        >
          <image
            class="image-thumb"
            src="{{item.url}}"
            mode="aspectFill"
            bindtap="previewImage"
            data-index="{{index}}"
          />
          <view class="image-actions">
            <button
              class="image-action-btn insert"
              data-index="{{index}}"
              bindtap="insertImageToContent"
            >插入</button>
            <button
              class="image-action-btn delete"
              data-index="{{index}}"
              bindtap="removeImage"
            >删除</button>
          </view>
        </view>

        <!-- 上传中状态 -->
        <view class="image-item uploading" wx:if="{{isUploadingImage}}">
          <view class="upload-progress">
            <view class="loading-spinner"></view>
            <text class="upload-text">上传中...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签选择 -->
    <view class="form-item">
      <view class="section-title">标签</view>
      <view class="tags-container">
        <view 
          class="tag-item" 
          wx:for="{{tags}}" 
          wx:key="id"
        >
          <text class="tag-name">{{item.name}}</text>
          <text class="tag-remove" data-id="{{item.id}}" catchtap="removeTag">×</text>
        </view>
        <view class="add-tag" bindtap="showTagSelector">
          <text class="add-icon">+</text>
          <text class="add-text">添加标签</text>
        </view>
      </view>
    </view>

    <!-- 公开设置 -->
    <view class="form-item">
      <view class="section-title">公开设置</view>
      <view class="public-switch" bindtap="togglePublic">
        <text class="switch-label">是否公开</text>
        <view class="switch-container {{isPublic ? 'active' : ''}}">
          <view class="switch-handle"></view>
        </view>
      </view>
      <text class="public-hint">{{isPublic ? '公开的笔记将显示在广场中' : '私密笔记仅自己可见'}}</text>
    </view>

    <!-- 底部按钮 -->
    <view class="form-actions">
      <button
        class="cancel-btn"
        bindtap="cancelEdit"
        hover-class="btn-hover"
      >取消</button>

      <button
        class="draft-btn"
        bindtap="saveDraft"
        hover-class="btn-hover"
        wx:if="{{hasUnsavedChanges}}"
      >保存草稿</button>

      <button
        class="save-btn {{isSubmitting ? 'disabled' : ''}}"
        bindtap="saveNote"
        disabled="{{isSubmitting}}"
        hover-class="btn-hover"
      >{{isSubmitting ? '发布中...' : '发布'}}</button>
    </view>
  </view>

  <!-- 标签选择器弹窗 -->
  <view class="tag-selector-overlay" wx:if="{{showTagSelector}}" bindtap="hideTagSelector">
    <view class="tag-selector-container" catchtap>
      <view class="selector-header">
        <text class="selector-title">选择标签</text>
        <text class="selector-close" bindtap="hideTagSelector">×</text>
      </view>
      
      <view class="selector-content">
        <view class="loading-tags" wx:if="{{isLoadingTags}}">
          <view class="loading-spinner small"></view>
          <text class="loading-text">加载标签中...</text>
        </view>
        
        <view class="tag-list" wx:else>
          <view 
            class="selector-tag-item {{item.isSelected ? 'selected' : ''}}" 
            wx:for="{{availableTags}}" 
            wx:key="id"
            data-id="{{item.id}}"
            bindtap="selectTag"
          >
            <text class="selector-tag-name">{{item.name}}</text>
            <text class="selector-tag-check" wx:if="{{item.isSelected}}">✓</text>
          </view>
          
          <view class="empty-tags" wx:if="{{availableTags.length === 0}}">
            <text>暂无可用标签</text>
          </view>
        </view>
      </view>
      
      <view class="selector-footer">
        <button class="confirm-btn" bindtap="hideTagSelector">确定</button>
      </view>
    </view>
  </view>
</view>
