/* pages/learn/index.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 固定的顶部区域 */
.fixed-header {
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 99;
  background-color: #f5f5f5;
  padding: 20rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 深色模式下的顶部区域 */
page[data-theme="dark"] .fixed-header {
  background-color: var(--graphite-bg); /* 使用石墨色 */
  box-shadow: 0 2rpx 10rpx var(--graphite-shadow);
}

/* 顶部操作区域 */
.header-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  margin-bottom: 10rpx;
}

/* 添加学习计划按钮 */
.add-plan-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.3);
  margin-right: 20rpx;
  transition: all 0.2s ease;
  position: relative;
  z-index: 100; /* 提高z-index确保按钮在最上层 */
}

/* 增加点击区域 */
.add-plan-button::after {
  content: '';
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  z-index: -1;
}

.add-plan-button:active {
  transform: scale(0.9);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
}

.add-icon {
  color: #ffffff;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

/* 当前学习计划信息 */
.current-plan-info {
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
}

.current-plan-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.plan-progress {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

/* 可滚动的内容区域 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  height: calc(100vh - 180rpx); /* 减去固定头部的高度 */
}

/* 内容容器 */
.content-container {
  padding: 10rpx 20rpx;
  padding-bottom: 130rpx;
}

/* 视图切换按钮 */
.view-toggle {
  display: flex;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  padding: 6rpx;
  width: 75%;
  margin-left: 30rpx; /* 增加左边距，向右移动 */
  margin-right: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  flex: 1;
}

.toggle-button {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  border-radius: 36rpx;
  font-size: 26rpx; /* 稍微减小字体大小 */
  color: #666;
  transition: all 0.3s ease;
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 防止文字溢出 */
  text-overflow: ellipsis; /* 文字溢出时显示省略号 */
}

.toggle-button.active {
  background-color: #ffffff;
  color: #3B82F6;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 学习计划选择器 */
.plan-selector {
  margin: 20rpx 0 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.plan-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 创建新计划按钮 */
.create-plan-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  color: #3B82F6;
  font-size: 30rpx;
}

.plus-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
  font-weight: bold;
}

/* 模块列表 */
.module-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-top: 20rpx;
  padding: 0 10rpx;
}

/* 模块项 */
.module-item {
  width: 100%;
  position: relative;
  margin-bottom: 30rpx;
  overflow: visible;
}

/* 卡片容器 */
.card-container {
  width: 100%;
  height: 340rpx; /* 增加高度，确保内容不会被挤压 */
  position: relative;
  overflow: hidden; /* 确保滑动效果正常 */
  border-radius: 24rpx;
  background: transparent;
  box-sizing: border-box;
}

/* 操作按钮区域 */
.action-buttons {
  position: absolute;
  right: 0; /* 固定在右侧 */
  top: 0;
  height: 100%;
  display: flex;
  flex-direction: column; /* 纵向排列 */
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  z-index: 5; /* 提高z-index确保按钮在最上层 */
  width: 100rpx; /* 设置合适的宽度 */
  padding: 0;
}

.edit-button, .delete-button {
  height: 60rpx;
  width: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  margin: 0;
}

.edit-button {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
}

.edit-button:active {
  opacity: 0.8;
  transform: scale(0.9);
}

.delete-button {
  background: linear-gradient(135deg, #F44336, #C62828);
}

.delete-button:active {
  opacity: 0.8;
  transform: scale(0.9);
}

/* 模块卡片 */
.module-card {
  display: flex;
  flex-direction: column;
  border-radius: 24rpx;
  overflow: hidden;
  padding: 30rpx 30rpx 30rpx 30rpx; /* 减少底部内边距 */
  box-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.15);
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: transform 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  transform: translateX(0); /* 默认位置 */
  z-index: 10; /* 确保卡片在按钮上方 */
  will-change: transform; /* 优化性能 */
  touch-action: pan-x; /* 优化触摸体验 */
  justify-content: space-between; /* 均匀分布内容 */
}

/* 模块头部 - 图标和标题在一行 */
.module-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16rpx;
}

.module-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.module-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 10rpx;
}

.module-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
}

.module-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

/* 进度条 */
.module-progress {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
  margin-bottom: 15rpx;
}

.progress-bar {
  flex: 1;
  height: 10rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 5rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.progress-fill {
  height: 100%;
  background-color: #ffffff;
  border-radius: 5rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #ffffff;
  min-width: 60rpx;
  text-align: right;
}

/* 按钮容器 */
.button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 0;
  margin-bottom: 0;
  position: relative;
  bottom: 20rpx; /* 使按钮进一步上移 */
}

/* 按钮 */
.module-button {
  width: 65%;
  background-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
  font-size: 32rpx;
  padding: 14rpx 0;
  border-radius: 100rpx;
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
  margin: 0;
  min-height: 0;
  letter-spacing: 2rpx;
}

.module-button:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.35);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 学习资源列表 */
.template-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.template-item {
  width: 100%;
}

.template-card {
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.1);
}

.template-header {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.template-type {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  padding: 6rpx 12rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
}

.template-content {
  padding: 20rpx 30rpx;
}

.template-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
  display: block;
  line-height: 1.5;
}

.template-button {
  background-color: #f0f0f0;
  color: #666666;
  font-size: 28rpx;
  padding: 12rpx 0;
  border-radius: 30rpx;
  width: 100%;
  text-align: center;
  margin-top: 10rpx;
}

.template-button:active {
  background-color: #e0e0e0;
}



/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.create-button {
  background-color: #3B82F6;
  color: #ffffff;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  border: none;
  margin-top: 20rpx;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  margin-top: 100rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  margin-top: 100rpx;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 32rpx;
  color: #ff4d4f;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.error-subtext {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 20rpx;
  text-align: center;
  padding: 0 40rpx;
}

.retry-button {
  background-color: #3B82F6;
  color: #ffffff;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  border: none;
}