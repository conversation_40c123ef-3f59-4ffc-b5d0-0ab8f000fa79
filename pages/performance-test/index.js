// pages/performance-test/index.js
// 泡泡交互系统性能测试页面

// 导入性能监控工具
const performanceMonitor = require('../../utils/performance-monitor');

Page({
  data: {
    initialized: false,
    fps: 0,
    avgFps: 0,
    frameTime: 0,
    bubbleCount: 20,
    performanceMode: 'auto',
    testScenario: 'medium',
    interactionMode: 'normal',
    testResults: null,
    isTestRunning: false,
    testProgress: 0,
    testDuration: 10, // 测试持续时间（秒）
    showDebugInfo: true,
    deviceInfo: {} // 设备信息
  },

  onLoad() {
    // 获取设备信息
    this.getDeviceInfo();

    // 启动性能监控
    performanceMonitor.start();

    // 初始化泡泡画布
    this.initBubbleCanvas();

    // 设置性能监控定时器
    this.performanceTimer = setInterval(() => {
      this.updatePerformanceMetrics();
    }, 1000);

    // 获取初始电池信息
    this.getBatteryInfo();
  },

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    try {
      const windowInfo = wx.getWindowInfo();
      const deviceInfo = wx.getDeviceInfo();
      const appBaseInfo = wx.getAppBaseInfo();

      this.setData({
        deviceInfo: {
          model: deviceInfo.model,
          system: deviceInfo.system,
          platform: deviceInfo.platform,
          benchmarkLevel: appBaseInfo.benchmarkLevel || '未知',
          brand: deviceInfo.brand,
          screenWidth: windowInfo.screenWidth,
          screenHeight: windowInfo.screenHeight,
          pixelRatio: windowInfo.pixelRatio
        }
      });

      console.log('设备信息:', this.data.deviceInfo);
    } catch (err) {
      console.error('获取设备信息失败:', err);
    }
  },

  /**
   * 获取电池信息
   */
  getBatteryInfo() {
    try {
      wx.getBatteryInfo({
        success: res => {
          this.batteryLevel = res.level;
          console.log('当前电池电量:', this.batteryLevel);
        },
        fail: err => {
          console.error('获取电池信息失败:', err);
        }
      });
    } catch (err) {
      console.error('获取电池信息API调用失败:', err);
    }
  },

  onUnload() {
    // 清除性能监控定时器
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
      this.performanceTimer = null;
    }

    // 停止性能监控
    performanceMonitor.stop();

    // 停止测试
    this.stopTest();
  },

  /**
   * 初始化泡泡画布
   */
  initBubbleCanvas() {
    // 获取泡泡画布组件
    this.bubbleCanvas = this.selectComponent('#bubble-canvas');

    if (!this.bubbleCanvas) {
      console.error('获取泡泡画布组件失败');

      // 延迟重试
      setTimeout(() => {
        console.log('尝试重新获取泡泡画布组件');
        this.initBubbleCanvas();
      }, 1000);

      return;
    }

    console.log('成功获取泡泡画布组件，开始初始化');

    // 初始化泡泡画布
    this.bubbleCanvas.init({
      page: this,
      canvasId: 'bubble-canvas',
      getThemes: () => this.generateThemes(this.data.bubbleCount),
      config: {
        animationSpeedMultiplier: 0.064,
        baseSpeed: 0.64,
        shadowBlur: 4
      }
    }).then(success => {
      if (success) {
        console.log('泡泡画布初始化成功');
        this.setData({ initialized: true });

        // 初始化成功后，设置性能模式
        if (this.bubbleCanvas.adaptivePerformance) {
          this.bubbleCanvas.adaptivePerformance.setMode(this.data.performanceMode);
          console.log(`设置性能模式: ${this.data.performanceMode}`);
        }
      } else {
        console.error('泡泡画布初始化失败');

        // 延迟重试
        setTimeout(() => {
          console.log('尝试重新初始化泡泡画布');
          this.initBubbleCanvas();
        }, 2000);
      }
    }).catch(err => {
      console.error('泡泡画布初始化出错:', err);

      // 延迟重试
      setTimeout(() => {
        console.log('尝试重新初始化泡泡画布');
        this.initBubbleCanvas();
      }, 2000);
    });
  },

  /**
   * 生成测试主题数据
   * @param {number} count - 主题数量
   * @returns {Array} 主题数组
   */
  generateThemes(count) {
    const themes = [];

    // 预定义的颜色数组 - 使用明亮、饱和的颜色
    const colors = [
      '#3B82F6', // 蓝色
      '#6366F1', // 靛蓝色
      '#8B5CF6', // 紫色
      '#EC4899', // 粉色
      '#EF4444', // 红色
      '#F97316', // 橙色
      '#F59E0B', // 琥珀色
      '#EAB308', // 黄色
      '#84CC16', // 酸橙色
      '#22C55E', // 绿色
      '#10B981', // 祖母绿
      '#14B8A6', // 蓝绿色
      '#06B6D4', // 青色
      '#0EA5E9' // 天蓝色
    ];

    // 预定义的名称数组 - 使用不同长度的名称测试文字渲染
    const nameTemplates = [
      '泡泡', // 2个字
      '测试泡泡', // 4个字
      '性能测试', // 4个字
      '优化', // 2个字
      '交互系统', // 4个字
      '画布', // 2个字
      '渲染', // 2个字
      '动画效果', // 4个字
      '碰撞', // 2个字
      '物理' // 2个字
    ];

    // 生成主题
    for (let i = 0; i < count; i++) {
      // 选择名称模板
      const nameTemplate = nameTemplates[i % nameTemplates.length];
      // 生成名称
      const name = `${nameTemplate}${Math.floor(i / nameTemplates.length) + 1}`;

      // 创建主题
      themes.push({
        id: `theme-${i}`,
        name: name,
        color: colors[i % colors.length]
      });
    }

    console.log(`生成了 ${themes.length} 个测试主题`);
    return themes;
  },

  /**
   * 更新性能指标
   */
  updatePerformanceMetrics() {
    if (!performanceMonitor) return;

    // 获取性能指标
    const metrics = performanceMonitor.getMetrics();

    // 更新数据
    this.setData({
      fps: metrics.fps.current.toFixed(1),
      avgFps: metrics.fps.avg.toFixed(1),
      frameTime: metrics.frameTime.current.toFixed(2)
    });

    // 如果正在测试，更新测试进度
    if (this.data.isTestRunning) {
      const elapsed = (Date.now() - this.testStartTime) / 1000;
      const progress = Math.min(100, (elapsed / this.data.testDuration) * 100);

      this.setData({ testProgress: progress });

      // 如果测试完成，结束测试
      if (elapsed >= this.data.testDuration) {
        this.finishTest();
      }
    }
  },

  /**
   * 处理泡泡数量变更
   * @param {Object} e - 事件对象
   */
  handleBubbleCountChange(e) {
    const bubbleCount = parseInt(e.detail.value);

    this.setData({ bubbleCount });

    // 重新生成泡泡
    if (this.bubbleCanvas) {
      this.bubbleCanvas.updateThemes(this.generateThemes(bubbleCount));
    }
  },

  /**
   * 处理性能模式变更
   * @param {Object} e - 事件对象
   */
  handlePerformanceModeChange(e) {
    const performanceMode = e.detail.value;

    this.setData({ performanceMode });

    // 设置性能模式
    if (this.bubbleCanvas && this.bubbleCanvas.adaptivePerformance) {
      this.bubbleCanvas.adaptivePerformance.setMode(performanceMode);
    }
  },

  /**
   * 处理测试场景变更
   * @param {Object} e - 事件对象
   */
  handleTestScenarioChange(e) {
    const testScenario = e.detail.value;

    // 根据测试场景设置泡泡数量
    let bubbleCount = 20;
    switch (testScenario) {
      case 'basic':
        bubbleCount = 10;
        break;
      case 'medium':
        bubbleCount = 20;
        break;
      case 'high':
        bubbleCount = 30;
        break;
    }

    this.setData({
      testScenario,
      bubbleCount
    });

    // 重新生成泡泡
    if (this.bubbleCanvas) {
      this.bubbleCanvas.updateThemes(this.generateThemes(this.data.bubbleCount));
    }
  },

  /**
   * 处理交互模式变更
   * @param {Object} e - 事件对象
   */
  handleInteractionModeChange(e) {
    const interactionMode = e.detail.value;
    this.setData({ interactionMode });
  },

  /**
   * 处理测试时长变更
   * @param {Object} e - 事件对象
   */
  handleTestDurationChange(e) {
    const testDuration = parseInt(e.detail.value);
    this.setData({ testDuration });
  },

  /**
   * 开始性能测试
   */
  startTest() {
    if (this.data.isTestRunning) return;

    // 检查泡泡画布是否已初始化
    if (!this.bubbleCanvas) {
      console.error('泡泡画布未初始化，无法开始测试');
      wx.showToast({
        title: '画布未初始化',
        icon: 'none',
        duration: 2000
      });

      // 尝试重新初始化
      this.initBubbleCanvas();
      return;
    }

    console.log('开始性能测试');

    // 重置性能监控
    performanceMonitor.reset();

    // 获取测试前的电池电量
    this.getBatteryInfo();
    this.testStartBatteryLevel = this.batteryLevel;

    console.log(`测试场景: ${this.data.testScenario}, 交互模式: ${this.data.interactionMode}, 泡泡数量: ${this.data.bubbleCount}`);

    // 根据交互模式设置自动交互
    if (this.data.interactionMode !== 'normal') {
      this.setupInteractionSimulation(this.data.interactionMode);
    }

    // 设置性能模式
    if (this.bubbleCanvas.adaptivePerformance) {
      this.bubbleCanvas.adaptivePerformance.setMode(this.data.performanceMode);
      console.log(`设置性能模式: ${this.data.performanceMode}`);
    }

    // 重新生成泡泡
    if (this.bubbleCanvas) {
      const themes = this.generateThemes(this.data.bubbleCount);
      const success = this.bubbleCanvas.updateThemes(themes);
      console.log(`更新泡泡主题${success ? '成功' : '失败'}`);
    }

    // 预热阶段 - 等待1秒让画布稳定
    wx.showLoading({
      title: '测试准备中...',
      mask: true
    });

    setTimeout(() => {
      wx.hideLoading();

      // 开始正式测试
      this.testStartTime = Date.now();
      this.testMetrics = [];

      // 设置测试采样定时器 - 每100毫秒采样一次
      this.testSampleTimer = setInterval(() => {
        // 获取性能指标
        const metrics = performanceMonitor.getMetrics();

        // 记录性能指标
        this.testMetrics.push({
          timestamp: Date.now() - this.testStartTime,
          fps: metrics.fps.current,
          frameTime: metrics.frameTime.current,
          cpuUsage: metrics.cpuUsage ? metrics.cpuUsage.current : 0,
          memoryUsage: metrics.memoryUsage ? metrics.memoryUsage.current : 0
        });

        // 每秒输出一次当前性能
        if (this.testMetrics.length % 10 === 0) {
          console.log(`测试进行中 - FPS: ${metrics.fps.current.toFixed(1)}, 帧时间: ${metrics.frameTime.current.toFixed(2)}ms`);
        }
      }, 100);

      // 更新状态
      this.setData({
        isTestRunning: true,
        testProgress: 0,
        testResults: null
      });

      console.log(`测试正式开始，持续时间: ${this.data.testDuration}秒`);
    }, 1000);
  },

  /**
   * 停止性能测试
   */
  stopTest() {
    if (!this.data.isTestRunning) return;

    // 清除测试采样定时器
    if (this.testSampleTimer) {
      clearInterval(this.testSampleTimer);
      this.testSampleTimer = null;
    }

    // 更新状态
    this.setData({
      isTestRunning: false,
      testProgress: 0
    });
  },

  /**
   * 设置交互模拟
   * @param {string} mode - 交互模式
   */
  setupInteractionSimulation(mode) {
    // 清除之前的交互模拟
    if (this.interactionTimer) {
      clearInterval(this.interactionTimer);
      this.interactionTimer = null;
    }

    // 设置交互频率
    let interactionInterval = 1000; // 默认1秒一次

    switch (mode) {
      case 'frequent':
        interactionInterval = 500; // 0.5秒一次
        break;
      case 'extreme':
        interactionInterval = 200; // 0.2秒一次
        break;
    }

    // 创建交互模拟定时器
    this.interactionTimer = setInterval(() => {
      if (!this.bubbleCanvas || !this.data.isTestRunning) return;

      // 模拟随机点击泡泡
      const randomX = Math.random() * this.data.deviceInfo.screenWidth;
      const randomY = Math.random() * (this.data.deviceInfo.screenHeight * 0.4); // 只在画布区域内点击

      this.bubbleCanvas.simulateTouch(randomX, randomY);
    }, interactionInterval);

    console.log(`设置交互模拟，模式: ${mode}, 间隔: ${interactionInterval}ms`);
  },

  /**
   * 完成性能测试
   */
  finishTest() {
    // 停止测试
    this.stopTest();

    // 停止交互模拟
    if (this.interactionTimer) {
      clearInterval(this.interactionTimer);
      this.interactionTimer = null;
    }

    // 获取测试后的电池电量
    wx.getBatteryInfo({
      success: res => {
        this.testEndBatteryLevel = res.level;
        console.log(`测试结束电池电量: ${this.testEndBatteryLevel}, 开始电量: ${this.testStartBatteryLevel}`);

        // 计算测试结果
        const results = this.calculateTestResults();

        // 更新状态
        this.setData({
          testResults: results
        });
      },
      fail: err => {
        console.error('获取测试结束电池信息失败:', err);

        // 计算测试结果（没有电池信息）
        const results = this.calculateTestResults();

        // 更新状态
        this.setData({
          testResults: results
        });
      }
    });
  },

  /**
   * 计算测试结果
   * @returns {Object} 测试结果
   */
  calculateTestResults() {
    if (!this.testMetrics || this.testMetrics.length === 0) {
      console.error('没有测试数据，无法计算结果');
      return null;
    }

    console.log(`计算测试结果，共 ${this.testMetrics.length} 个样本`);

    // 去除前10%的样本，避免启动阶段的不稳定数据影响结果
    const skipSamples = Math.floor(this.testMetrics.length * 0.1);
    const validMetrics = this.testMetrics.slice(skipSamples);

    if (validMetrics.length === 0) {
      console.error('有效样本数为0，无法计算结果');
      return null;
    }

    console.log(`去除前 ${skipSamples} 个样本后，有效样本数: ${validMetrics.length}`);

    // 计算平均FPS
    const totalFps = validMetrics.reduce((sum, metric) => sum + metric.fps, 0);
    const avgFps = totalFps / validMetrics.length;

    // 计算平均帧时间
    const totalFrameTime = validMetrics.reduce((sum, metric) => sum + metric.frameTime, 0);
    const avgFrameTime = totalFrameTime / validMetrics.length;

    // 计算最低FPS
    const minFps = Math.min(...validMetrics.map(metric => metric.fps));

    // 计算最高FPS
    const maxFps = Math.max(...validMetrics.map(metric => metric.fps));

    // 计算FPS稳定性（标准差）
    const fpsVariance = validMetrics.reduce((sum, metric) => sum + Math.pow(metric.fps - avgFps, 2), 0) / validMetrics.length;
    const fpsStdDev = Math.sqrt(fpsVariance);

    // 计算FPS分布
    const fpsDistribution = {
      excellent: validMetrics.filter(metric => metric.fps >= 55).length,
      good: validMetrics.filter(metric => metric.fps >= 45 && metric.fps < 55).length,
      fair: validMetrics.filter(metric => metric.fps >= 30 && metric.fps < 45).length,
      poor: validMetrics.filter(metric => metric.fps < 30).length
    };

    // 计算总样本数
    const totalSamples = validMetrics.length;

    // 计算分布百分比
    const fpsDistributionPercent = {
      excellent: (fpsDistribution.excellent / totalSamples * 100).toFixed(1) + '%',
      good: (fpsDistribution.good / totalSamples * 100).toFixed(1) + '%',
      fair: (fpsDistribution.fair / totalSamples * 100).toFixed(1) + '%',
      poor: (fpsDistribution.poor / totalSamples * 100).toFixed(1) + '%'
    };

    // 计算CPU使用率（如果有）
    let avgCpuUsage = 0;
    let maxCpuUsage = 0;
    if (validMetrics[0].cpuUsage !== undefined) {
      const totalCpuUsage = validMetrics.reduce((sum, metric) => sum + (metric.cpuUsage || 0), 0);
      avgCpuUsage = totalCpuUsage / validMetrics.length;
      maxCpuUsage = Math.max(...validMetrics.map(metric => metric.cpuUsage || 0));
    }

    // 计算内存使用（如果有）
    let avgMemoryUsage = 0;
    let maxMemoryUsage = 0;
    if (validMetrics[0].memoryUsage !== undefined) {
      const totalMemoryUsage = validMetrics.reduce((sum, metric) => sum + (metric.memoryUsage || 0), 0);
      avgMemoryUsage = totalMemoryUsage / validMetrics.length;
      maxMemoryUsage = Math.max(...validMetrics.map(metric => metric.memoryUsage || 0));
    }

    // 计算性能得分 (0-100)
    // 权重: FPS 60%, 稳定性 20%, 帧时间 20%
    const fpsScore = Math.min(100, (avgFps / 60) * 100);
    const stabilityScore = Math.max(0, 100 - (fpsStdDev / 5) * 100);
    const frameTimeScore = Math.max(0, 100 - (avgFrameTime / 16.67) * 100);

    const performanceScore = Math.round(
      fpsScore * 0.6 +
      stabilityScore * 0.2 +
      frameTimeScore * 0.2
    );

    // 性能等级
    let performanceGrade;
    if (performanceScore >= 90) {
      performanceGrade = 'A+';
    } else if (performanceScore >= 80) {
      performanceGrade = 'A';
    } else if (performanceScore >= 70) {
      performanceGrade = 'B+';
    } else if (performanceScore >= 60) {
      performanceGrade = 'B';
    } else if (performanceScore >= 50) {
      performanceGrade = 'C';
    } else {
      performanceGrade = 'D';
    }

    console.log(`测试结果计算完成，性能得分: ${performanceScore} (${performanceGrade})`);

    // 计算电池消耗
    let batteryConsumption = 0;
    if (this.testStartBatteryLevel && this.testEndBatteryLevel) {
      batteryConsumption = this.testStartBatteryLevel - this.testEndBatteryLevel;
      console.log(`电池消耗: ${batteryConsumption}%`);
    }

    // 生成测试报告
    const testReport = {
      avgFps: avgFps.toFixed(1),
      avgFrameTime: avgFrameTime.toFixed(2),
      minFps: minFps.toFixed(1),
      maxFps: maxFps.toFixed(1),
      fpsStdDev: fpsStdDev.toFixed(2),
      fpsDistribution,
      fpsDistributionPercent,
      avgCpuUsage: avgCpuUsage.toFixed(1),
      maxCpuUsage: maxCpuUsage.toFixed(1),
      avgMemoryUsage: avgMemoryUsage.toFixed(1),
      maxMemoryUsage: maxMemoryUsage.toFixed(1),
      batteryConsumption: batteryConsumption > 0 ? batteryConsumption : null,
      performanceScore,
      performanceGrade,
      bubbleCount: this.data.bubbleCount,
      testScenario: this.data.testScenario,
      interactionMode: this.data.interactionMode,
      performanceMode: this.data.performanceMode,
      duration: this.data.testDuration,
      timestamp: new Date().toISOString()
    };

    // 保存测试报告到本地存储
    this.saveTestReport(testReport);

    return testReport;
  },

  /**
   * 切换调试信息显示
   */
  toggleDebugInfo() {
    this.setData({
      showDebugInfo: !this.data.showDebugInfo
    });
  },

  /**
   * 保存测试报告到本地存储
   * @param {Object} report - 测试报告
   */
  saveTestReport(report) {
    try {
      // 获取已有的测试报告
      const reportsStr = wx.getStorageSync('performance_test_reports') || '[]';
      const reports = JSON.parse(reportsStr);

      // 添加新报告
      reports.push(report);

      // 只保留最近10条报告
      if (reports.length > 10) {
        reports.shift();
      }

      // 保存回本地存储
      wx.setStorage({
        key: 'performance_test_reports',
        data: JSON.stringify(reports),
        success: () => {
          console.log('测试报告保存成功');
        },
        fail: err => {
          console.error('测试报告保存失败:', err);
        }
      });
    } catch (err) {
      console.error('保存测试报告出错:', err);
    }
  }
});
