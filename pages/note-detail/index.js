// pages/note-detail/index.js
// 笔记详情页面

// 导入API工具
const { noteAPI } = require('../../utils/api');
// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    noteId: null,
    note: null,
    isLoading: true,
    loadingFailed: false,
    isLiked: false,
    isCollected: false,
    showShareModal: false,
    showCommentModal: false,
    commentText: '',
    comments: [],
    isSubmittingComment: false,

    // 用户信息
    currentUser: null,
    isOwner: false,

    // 相关笔记
    relatedNotes: [],

    // 主题模式
    isDarkMode: false,

    // 新增互动功能数据
    // 分享功能
    shareOptions: [
      { type: 'wechat', name: '微信好友', icon: '💬' },
      { type: 'moments', name: '朋友圈', icon: '🌟' },
      { type: 'copy', name: '复制链接', icon: '🔗' },
      { type: 'qrcode', name: '生成二维码', icon: '📱' }
    ],
    showQRCode: false,
    qrCodeUrl: '',

    // 收藏夹管理
    showCollectionModal: false,
    collections: [], // 用户的收藏夹列表
    selectedCollections: [], // 选中的收藏夹
    newCollectionName: '', // 新建收藏夹名称
    showCreateCollection: false,

    // 点赞动画
    likeAnimation: false,
    likeParticles: [], // 点赞粒子效果

    // 评论增强
    replyToComment: null, // 回复的评论
    showEmojiPanel: false, // 表情面板
    emojiList: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'],

    // 阅读进度
    readingProgress: 0,
    isReading: false,
    readStartTime: null,

    // 笔记操作历史
    operationHistory: [],

    // 快捷操作
    showQuickActions: false,
    quickActionPosition: { x: 0, y: 0 }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const noteId = options.id;

    if (!noteId) {
      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: '缺少笔记ID',
        icon: 'none'
      });

      return;
    }

    this.setData({ noteId });

    // 获取主题模式
    const themeMode = wx.getStorageSync('themeMode') || 'light';
    const isDarkMode = themeMode === 'dark' ||
                      (themeMode === 'system' && wx.getSystemInfoSync().theme === 'dark');

    this.setData({ isDarkMode });

    // 加载笔记详情
    this.loadNoteDetail();

    // 加载当前用户信息
    this.loadCurrentUser();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示时刷新数据
    if (this.data.noteId) {
      this.loadNoteDetail();
    }
  },

  /**
   * 加载当前用户信息
   */
  async loadCurrentUser() {
    try {
      const isLoggedIn = await authService.isLoggedIn();
      if (isLoggedIn) {
        const userInfo = await authService.getCurrentUser();
        this.setData({ currentUser: userInfo });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  /**
   * 加载笔记详情
   */
  async loadNoteDetail() {
    this.setData({ isLoading: true, loadingFailed: false });

    try {
      // 获取API客户端
      const app = getApp();
      const api = app.globalData.api;

      if (!api) {
        throw new Error('API客户端未初始化');
      }

      // 调用API获取笔记详情
      const response = await api.note.getNoteById(this.data.noteId);

      if (response.success && response.data) {
        const note = response.data;

        // 检查是否为笔记作者
        const isOwner = this.data.currentUser &&
                       this.data.currentUser.id === note.userId;

        this.setData({
          note,
          isOwner,
          isLiked: note.isLiked || false,
          isCollected: note.isCollected || false,
          isLoading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: note.title || '笔记详情'
        });

        // 加载评论
        this.loadComments();

        // 加载相关笔记
        this.loadRelatedNotes();

      } else {
        throw new Error(response.error || '获取笔记详情失败');
      }
    } catch (error) {
      console.error('加载笔记详情失败:', error);

      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载评论列表
   */
  async loadComments() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) return;

      const response = await api.note.getComments(this.data.noteId);

      if (response.success && response.data) {
        this.setData({
          comments: response.data.comments || []
        });
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    }
  },

  /**
   * 加载相关笔记
   */
  async loadRelatedNotes() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api || !this.data.note) return;

      // 根据标签获取相关笔记
      const tagIds = this.data.note.tags ? this.data.note.tags.map(tag => tag.id) : [];

      if (tagIds.length === 0) return;

      const response = await api.note.getRelatedNotes(this.data.noteId, {
        tagIds,
        limit: 6
      });

      if (response.success && response.data) {
        this.setData({
          relatedNotes: response.data.notes || []
        });
      }
    } catch (error) {
      console.error('加载相关笔记失败:', error);
    }
  },

  /**
   * 点赞/取消点赞 (增强版)
   */
  async toggleLike() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const isLiked = this.data.isLiked;
    const note = this.data.note;

    // 触发点赞动画
    if (!isLiked) {
      this.triggerLikeAnimation();
    }

    // 乐观更新UI
    this.setData({
      isLiked: !isLiked,
      'note.likeCount': note.likeCount + (isLiked ? -1 : 1)
    });

    // 记录操作历史
    this.addOperationHistory('like', !isLiked);

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = isLiked
        ? await api.note.unlikeNote(this.data.noteId)
        : await api.note.likeNote(this.data.noteId);

      if (!response.success) {
        // 如果失败，回滚UI更新
        this.setData({
          isLiked: isLiked,
          'note.likeCount': note.likeCount
        });

        throw new Error(response.error || '操作失败');
      }

      // 触觉反馈
      wx.vibrateShort({ type: 'light' });

      // 成功提示
      if (!isLiked) {
        wx.showToast({
          title: '点赞成功',
          icon: 'none',
          duration: 1000
        });
      }

    } catch (error) {
      console.error('点赞操作失败:', error);

      // 回滚UI更新
      this.setData({
        isLiked: isLiked,
        'note.likeCount': note.likeCount
      });

      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 触发点赞动画
   */
  triggerLikeAnimation() {
    // 设置动画状态
    this.setData({ likeAnimation: true });

    // 生成粒子效果
    const particles = [];
    for (let i = 0; i < 6; i++) {
      particles.push({
        id: Date.now() + i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: i * 100
      });
    }

    this.setData({ likeParticles: particles });

    // 清除动画状态
    setTimeout(() => {
      this.setData({
        likeAnimation: false,
        likeParticles: []
      });
    }, 1000);
  },

  /**
   * 添加操作历史
   */
  addOperationHistory(type, value) {
    const history = this.data.operationHistory;
    history.push({
      type,
      value,
      timestamp: Date.now()
    });

    // 只保留最近10条记录
    if (history.length > 10) {
      history.shift();
    }

    this.setData({ operationHistory: history });
  },

  /**
   * 显示收藏夹选择弹窗
   */
  async showCollectionModal() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 加载用户收藏夹列表
    await this.loadCollections();

    this.setData({
      showCollectionModal: true
    });
  },

  /**
   * 隐藏收藏夹弹窗
   */
  hideCollectionModal() {
    this.setData({
      showCollectionModal: false,
      selectedCollections: [],
      showCreateCollection: false,
      newCollectionName: ''
    });
  },

  /**
   * 加载用户收藏夹列表
   */
  async loadCollections() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) return;

      const response = await api.collection.getUserCollections();

      if (response.success && response.data) {
        // 检查当前笔记在哪些收藏夹中
        const noteCollections = await api.collection.getNoteCollections(this.data.noteId);
        const selectedCollections = noteCollections.success ?
          noteCollections.data.map(c => c.id) : [];

        this.setData({
          collections: response.data.collections || [],
          selectedCollections: selectedCollections
        });
      }
    } catch (error) {
      console.error('加载收藏夹失败:', error);
    }
  },

  /**
   * 切换收藏夹选择
   */
  toggleCollectionSelection(e) {
    const collectionId = e.currentTarget.dataset.id;
    const selectedCollections = [...this.data.selectedCollections];
    const index = selectedCollections.indexOf(collectionId);

    if (index === -1) {
      selectedCollections.push(collectionId);
    } else {
      selectedCollections.splice(index, 1);
    }

    this.setData({ selectedCollections });
  },

  /**
   * 显示创建收藏夹
   */
  showCreateCollection() {
    this.setData({
      showCreateCollection: true,
      newCollectionName: ''
    });
  },

  /**
   * 隐藏创建收藏夹
   */
  hideCreateCollection() {
    this.setData({
      showCreateCollection: false,
      newCollectionName: ''
    });
  },

  /**
   * 收藏夹名称输入
   */
  onCollectionNameInput(e) {
    this.setData({
      newCollectionName: e.detail.value
    });
  },

  /**
   * 创建新收藏夹
   */
  async createCollection() {
    const name = this.data.newCollectionName.trim();

    if (!name) {
      wx.showToast({
        title: '请输入收藏夹名称',
        icon: 'none'
      });
      return;
    }

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = await api.collection.createCollection({
        name: name,
        description: ''
      });

      if (response.success) {
        wx.showToast({
          title: '创建成功',
          icon: 'success'
        });

        // 重新加载收藏夹列表
        await this.loadCollections();

        // 隐藏创建界面
        this.hideCreateCollection();

      } else {
        throw new Error(response.error || '创建失败');
      }
    } catch (error) {
      console.error('创建收藏夹失败:', error);

      wx.showToast({
        title: error.message || '创建失败',
        icon: 'none'
      });
    }
  },

  /**
   * 确认收藏到选中的收藏夹
   */
  async confirmCollect() {
    const selectedCollections = this.data.selectedCollections;

    if (selectedCollections.length === 0) {
      wx.showToast({
        title: '请选择收藏夹',
        icon: 'none'
      });
      return;
    }

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = await api.collection.addNoteToCollections(this.data.noteId, selectedCollections);

      if (response.success) {
        this.setData({ isCollected: true });

        wx.showToast({
          title: `已收藏到${selectedCollections.length}个收藏夹`,
          icon: 'success'
        });

        // 隐藏弹窗
        this.hideCollectionModal();

        // 记录操作历史
        this.addOperationHistory('collect', true);

      } else {
        throw new Error(response.error || '收藏失败');
      }
    } catch (error) {
      console.error('收藏失败:', error);

      wx.showToast({
        title: error.message || '收藏失败',
        icon: 'none'
      });
    }
  },

  /**
   * 快速收藏/取消收藏 (原有功能保留)
   */
  async toggleCollect() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const isCollected = this.data.isCollected;

    // 如果已收藏，直接取消收藏
    if (isCollected) {
      await this.uncollectNote();
    } else {
      // 如果未收藏，显示收藏夹选择
      this.showCollectionModal();
    }
  },

  /**
   * 取消收藏
   */
  async uncollectNote() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = await api.collection.removeNoteFromAllCollections(this.data.noteId);

      if (response.success) {
        this.setData({ isCollected: false });

        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        });

        // 记录操作历史
        this.addOperationHistory('collect', false);

      } else {
        throw new Error(response.error || '操作失败');
      }
    } catch (error) {
      console.error('取消收藏失败:', error);

      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示评论弹窗
   */
  showCommentModal() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCommentModal: true,
      commentText: ''
    });
  },

  /**
   * 隐藏评论弹窗
   */
  hideCommentModal() {
    this.setData({
      showCommentModal: false,
      commentText: ''
    });
  },

  /**
   * 评论输入
   */
  onCommentInput(e) {
    this.setData({
      commentText: e.detail.value
    });
  },

  /**
   * 提交评论
   */
  async submitComment() {
    const commentText = this.data.commentText.trim();

    if (!commentText) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmittingComment: true });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = await api.note.addComment(this.data.noteId, {
        content: commentText
      });

      if (response.success) {
        wx.showToast({
          title: '评论成功',
          icon: 'success'
        });

        // 隐藏弹窗
        this.hideCommentModal();

        // 重新加载评论
        this.loadComments();

        // 更新评论数量
        const note = this.data.note;
        this.setData({
          'note.commentCount': (note.commentCount || 0) + 1
        });

      } else {
        throw new Error(response.error || '评论失败');
      }
    } catch (error) {
      console.error('提交评论失败:', error);

      wx.showToast({
        title: error.message || '评论失败',
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmittingComment: false });
    }
  },

  /**
   * 编辑笔记
   */
  editNote() {
    if (!this.data.isOwner) {
      wx.showToast({
        title: '只能编辑自己的笔记',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/note-edit/index?id=${this.data.noteId}`
    });
  },

  /**
   * 删除笔记
   */
  deleteNote() {
    if (!this.data.isOwner) {
      wx.showToast({
        title: '只能删除自己的笔记',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这篇笔记吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#e64340',
      success: async res => {
        if (res.confirm) {
          await this.performDelete();
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async performDelete() {
    wx.showLoading({ title: '删除中...' });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = await api.note.deleteNote(this.data.noteId);

      if (response.success) {
        wx.hideLoading();

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('删除笔记失败:', error);

      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示分享弹窗
   */
  showShareModal() {
    this.setData({
      showShareModal: true,
      showQRCode: false
    });
  },

  /**
   * 隐藏分享弹窗
   */
  hideShareModal() {
    this.setData({
      showShareModal: false,
      showQRCode: false
    });
  },

  /**
   * 处理分享选项
   */
  handleShareOption(e) {
    const type = e.currentTarget.dataset.type;

    switch (type) {
      case 'wechat':
        this.shareToWechat();
        break;
      case 'moments':
        this.shareToMoments();
        break;
      case 'copy':
        this.copyShareLink();
        break;
      case 'qrcode':
        this.generateQRCode();
        break;
    }
  },

  /**
   * 分享到微信好友
   */
  shareToWechat() {
    this.hideShareModal();

    wx.showToast({
      title: '请点击右上角分享',
      icon: 'none'
    });

    // 记录分享操作
    this.addOperationHistory('share', 'wechat');
  },

  /**
   * 分享到朋友圈
   */
  shareToMoments() {
    this.hideShareModal();

    wx.showToast({
      title: '请点击右上角分享到朋友圈',
      icon: 'none'
    });

    // 记录分享操作
    this.addOperationHistory('share', 'moments');
  },

  /**
   * 复制分享链接
   */
  copyShareLink() {
    const note = this.data.note;
    const shareText = `${note.title}\n\n${note.content.substring(0, 100)}${note.content.length > 100 ? '...' : ''}\n\n来自AIBUBB智能学习笔记`;

    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });

        this.hideShareModal();

        // 记录分享操作
        this.addOperationHistory('share', 'copy');
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 生成二维码
   */
  async generateQRCode() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      wx.showLoading({ title: '生成中...' });

      const response = await api.share.generateQRCode({
        page: 'pages/note-detail/index',
        scene: `id=${this.data.noteId}`,
        width: 280
      });

      wx.hideLoading();

      if (response.success && response.data.qrCodeUrl) {
        this.setData({
          showQRCode: true,
          qrCodeUrl: response.data.qrCodeUrl
        });

        // 记录分享操作
        this.addOperationHistory('share', 'qrcode');

      } else {
        throw new Error(response.error || '生成二维码失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('生成二维码失败:', error);

      wx.showToast({
        title: error.message || '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 保存二维码到相册
   */
  saveQRCode() {
    if (!this.data.qrCodeUrl) return;

    wx.downloadFile({
      url: this.data.qrCodeUrl,
      success: res => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '已保存到相册',
              icon: 'success'
            });
          },
          fail: () => {
            wx.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 增强评论功能 - 回复评论
   */
  replyToComment(e) {
    const comment = e.currentTarget.dataset.comment;

    this.setData({
      replyToComment: comment,
      showCommentModal: true,
      commentText: `@${comment.userName} `
    });
  },

  /**
   * 显示表情面板
   */
  showEmojiPanel() {
    this.setData({
      showEmojiPanel: !this.data.showEmojiPanel
    });
  },

  /**
   * 插入表情
   */
  insertEmoji(e) {
    const emoji = e.currentTarget.dataset.emoji;
    const commentText = this.data.commentText + emoji;

    this.setData({
      commentText: commentText,
      showEmojiPanel: false
    });
  },

  /**
   * 查看相关笔记
   */
  viewRelatedNote(e) {
    const noteId = e.currentTarget.dataset.id;

    wx.navigateTo({
      url: `/pages/note-detail/index?id=${noteId}`
    });
  },

  /**
   * 重试加载
   */
  retryLoading() {
    this.loadNoteDetail();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const note = this.data.note;

    return {
      title: note ? note.title : '分享笔记',
      path: `/pages/note-detail/index?id=${this.data.noteId}`,
      imageUrl: note && note.imageUrl ? note.imageUrl : ''
    };
  }
});
