<!-- 虚拟列表测试页面 -->
<view class="container">
  <view class="header">
    <view class="title">虚拟列表性能测试</view>
    <view class="controls">
      <view class="control-group">
        <text class="control-label">列表类型:</text>
        <radio-group class="radio-group" bindchange="handleListTypeChange">
          <label class="radio-label">
            <radio value="fixed" checked="{{listType === 'fixed'}}" />
            <text>固定高度</text>
          </label>
          <label class="radio-label">
            <radio value="dynamic" checked="{{listType === 'dynamic'}}" />
            <text>动态高度</text>
          </label>
        </radio-group>
      </view>
      
      <view class="control-group">
        <text class="control-label">数据量:</text>
        <slider 
          min="100" 
          max="10000" 
          step="100" 
          value="{{dataCount}}" 
          show-value 
          bindchange="handleDataCountChange"
        ></slider>
      </view>
      
      <button class="btn-primary" bindtap="generateData">生成数据</button>
    </view>
  </view>
  
  <view class="content">
    <view class="list-container">
      <view class="list-header">
        <view class="list-title">虚拟列表 ({{visibleCount}}/{{totalCount}})</view>
        <view class="performance-info">
          <text>FPS: {{fps}}</text>
          <text>内存: {{memory}}MB</text>
        </view>
      </view>
      
      <virtual-list
        class="virtual-list"
        items="{{listData}}"
        item-height="{{listType === 'fixed' ? 100 : 0}}"
        dynamic="{{listType === 'dynamic'}}"
        buffer-size="{{5}}"
        item-key="id"
        bind:scroll="handleListScroll"
        bind:itemtap="handleItemTap"
        bind:loadmore="handleLoadMore"
        loading="{{loading}}"
        no-more="{{noMore}}"
      >
        <view slot="item">
          <view class="list-item {{listType === 'dynamic' ? 'dynamic-height' : ''}}">
            <view class="item-avatar" style="background-color: {{listItem.color}}">
              {{listIndex + 1}}
            </view>
            <view class="item-content">
              <view class="item-title">{{listItem.title}}</view>
              <view class="item-desc">{{listItem.description}}</view>
              <view class="item-tags" wx:if="{{listType === 'dynamic' && listItem.tags.length > 0}}">
                <view 
                  wx:for="{{listItem.tags}}" 
                  wx:key="*this"
                  wx:for-item="tag"
                  class="item-tag"
                >
                  {{tag}}
                </view>
              </view>
            </view>
          </view>
        </view>
      </virtual-list>
    </view>
    
    <view class="list-container">
      <view class="list-header">
        <view class="list-title">普通列表 ({{totalCount}})</view>
        <view class="performance-info">
          <text>FPS: {{normalFps}}</text>
          <text>内存: {{normalMemory}}MB</text>
        </view>
      </view>
      
      <scroll-view
        class="normal-list"
        scroll-y
        bindscroll="handleNormalListScroll"
        bindscrolltolower="handleNormalListLoadMore"
        lower-threshold="200"
      >
        <view 
          wx:for="{{listData}}" 
          wx:key="id"
          class="list-item {{listType === 'dynamic' ? 'dynamic-height' : ''}}"
          data-item="{{item}}"
          data-index="{{index}}"
          bindtap="handleNormalItemTap"
        >
          <view class="item-avatar" style="background-color: {{item.color}}">
            {{index + 1}}
          </view>
          <view class="item-content">
            <view class="item-title">{{item.title}}</view>
            <view class="item-desc">{{item.description}}</view>
            <view class="item-tags" wx:if="{{listType === 'dynamic' && item.tags.length > 0}}">
              <view 
                wx:for="{{item.tags}}" 
                wx:key="*this"
                wx:for-item="tag"
                class="item-tag"
              >
                {{tag}}
              </view>
            </view>
          </view>
        </view>
        
        <view class="loading-more" wx:if="{{loading}}">
          <view class="loading-indicator"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <view class="no-more" wx:if="{{noMore && !loading}}">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </scroll-view>
    </view>
  </view>
  
  <view class="footer">
    <view class="performance-summary">
      <view class="summary-item">
        <text class="summary-label">虚拟列表平均FPS:</text>
        <text class="summary-value">{{avgFps}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">普通列表平均FPS:</text>
        <text class="summary-value">{{avgNormalFps}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">性能提升:</text>
        <text class="summary-value">{{performanceImprovement}}%</text>
      </view>
    </view>
    
    <button class="btn-secondary" bindtap="resetTest">重置测试</button>
  </view>
</view>
