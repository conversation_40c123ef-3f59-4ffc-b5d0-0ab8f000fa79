// app.js
// 导入认证服务
const authService = require('./utils/auth-service');
// 导入令牌管理器
const tokenManager = require('./utils/token-manager');
// 导入环境配置
const { getEnvConfig } = require('./utils/env-config');

App({
  onLaunch() {
    // 初始化环境配置
    this.initEnvConfig();

    // 可在此处做一些全局的初始化工作
    this.initTheme();
    this.initFeatureFlags();

    // 检查登录状态
    this.checkLoginState();

    // 获取系统信息
    try {
      const windowInfo = wx.getWindowInfo();
      const deviceInfo = wx.getDeviceInfo();
      const appBaseInfo = wx.getAppBaseInfo();

      // 合并系统信息以保持兼容性
      const systemInfo = {
        ...windowInfo,
        ...deviceInfo,
        ...appBaseInfo
      };

      this.globalData.systemInfo = systemInfo;
      console.log('系统信息:', systemInfo);

      // 如果主题模式设置为system，则使用系统主题
      if (this.globalData.themeMode === 'system') {
        const systemTheme = appBaseInfo.theme || 'light';
        this.globalData.currentTheme = systemTheme;
        console.log('使用系统主题:', systemTheme);
      }

      // 在测试模式下，跳过需要登录的API调用
      if (!this.globalData.isTestMode) {
        // 根据当前主题设置导航栏和背景色
        if (this.globalData.currentTheme === 'dark') {
          // 深色模式
          wx.setBackgroundColor({
            backgroundColor: '#2C2C2E', // 石墨色
            backgroundColorTop: '#2C2C2E',
            backgroundColorBottom: '#2C2C2E'
          });
        } else {
          // 浅色模式
          wx.setBackgroundColor({
            backgroundColor: '#ffffff',
            backgroundColorTop: '#ffffff',
            backgroundColorBottom: '#ffffff'
          });
        }
      } else {
        console.log('[Test Mode] Skipping initial background color set in onLaunch (requires login).');
      }
    } catch (err) {
      console.error('获取系统信息失败:', err);
    }

    // 延迟执行，确保导航栏样式已更新
    setTimeout(() => {
      this.updateNavigationBarStyle();
    }, 500);
  },

  globalData: {
    userInfo: null,
    systemInfo: null,
    hasLogin: false,
    apiBaseUrl: null, // 将由环境配置动态设置
    interfaceStyle: 'bubble', // 界面样式：bubble或star
    themeMode: 'light', // 主题模式：light、dark或system
    currentTheme: 'light', // 当前实际使用的主题：light或dark
    navBarStyleNeedsUpdate: true, // 导航栏样式是否需要更新
    // 测试模式标志
    isTestMode: null, // 将由环境配置动态设置
    // 特性开关 - 用于控制是否使用新的组件实现
    useNewImplementation: true, // 是否使用新的组件实现
    loginReadyCallback: null, // 新增回调
    isLoginProcessing: false, // 标记登录是否正在进行
    envVersion: 'develop' // 当前环境版本，默认为开发环境
  },

  // 初始化环境配置
  initEnvConfig() {
    // 获取当前环境配置
    const envConfig = getEnvConfig();
    console.log('当前环境配置:', envConfig);

    // 将环境配置应用到全局数据
    this.globalData.apiBaseUrl = envConfig.apiBaseUrl;
    this.globalData.isTestMode = envConfig.isTestMode;
    this.globalData.envVersion = envConfig.envVersion;

    // 记录环境信息
    console.log(`应用启动 - 环境: ${envConfig.envVersion}, API: ${envConfig.apiBaseUrl}, 测试模式: ${envConfig.isTestMode}`);
  },

  // 初始化主题设置
  initTheme() {
    // 加载界面样式设置
    const interfaceStyle = wx.getStorageSync('interfaceStyle') || 'bubble';
    this.globalData.interfaceStyle = interfaceStyle;

    // 加载主题模式设置
    const themeMode = wx.getStorageSync('themeMode') || 'light';
    this.globalData.themeMode = themeMode;

    // 初始化当前实际主题为浅色
    this.globalData.currentTheme = 'light';

    // 如果主题模式是深色，直接切换到深色
    if (themeMode === 'dark') {
      this.globalData.currentTheme = 'dark';
    }
    // 如果是系统主题，将在onLaunch获取系统信息后设置

    console.log('界面样式:', interfaceStyle, '主题模式:', themeMode, '当前主题:', this.globalData.currentTheme);

    // 应用当前主题样式
    this.applyThemeStyles(this.globalData.currentTheme);

    // 监听系统主题变化
    wx.onThemeChange(result => {
      const systemTheme = result.theme; // 'light' 或 'dark'
      console.log('系统主题变化为:', systemTheme);

      // 如果设置为跟随系统主题，则更新当前主题
      if (this.globalData.themeMode === 'system') {
        this.globalData.currentTheme = systemTheme;

        // 应用新的主题样式
        this.applyThemeStyles(systemTheme);

        // 通知页面刷新
        if (this.themeModeChangeCallback) {
          this.themeModeChangeCallback(systemTheme);
        }
      }
    });
  },

  // 初始化应用配置
  initFeatureFlags() {
    // 清除旧的特性开关设置
    wx.removeStorageSync('useNewImplementation');

    // 始终使用新组件
    this.globalData.useNewImplementation = true;

    console.log('新组件已正式启用');
  },

  // 应用主题样式 - 集中处理所有与主题相关的样式更新
  applyThemeStyles(theme) {
    console.log('应用主题样式:', theme);

    // 更新tabBar样式
    this.updateTabBarStyle(theme);

    // 设置导航栏样式更新标志
    this.globalData.navBarStyleNeedsUpdate = true;

    // 更新当前页面的导航栏样式
    this.updateCurrentPageNavigationBar();

    // 更新背景色
    this.updateBackgroundColor(theme);
  },

  // 更新背景色
  updateBackgroundColor(theme) {
    try {
      // 定义背景色配置
      const bgConfig = theme === 'dark'
        ? {
          backgroundColor: '#2C2C2E', // 石墨色
          backgroundColorTop: '#2C2C2E',
          backgroundColorBottom: '#2C2C2E',
          themeName: '深色'
        }
        : {
          backgroundColor: '#ffffff',
          backgroundColorTop: '#ffffff',
          backgroundColorBottom: '#ffffff',
          themeName: '浅色'
        };

      // 设置背景色
      wx.setBackgroundColor({
        backgroundColor: bgConfig.backgroundColor,
        backgroundColorTop: bgConfig.backgroundColorTop,
        backgroundColorBottom: bgConfig.backgroundColorBottom,
        success: () => {
          console.log(`设置${bgConfig.themeName}背景色成功`);
        },
        fail: err => {
          console.error(`设置${bgConfig.themeName}背景色失败:`, err);

          // 错误处理：检查 INVALID_LOGIN 错误
          if (err && err.errMsg && err.errMsg.includes('INVALID_LOGIN')) {
            console.warn(`因登录状态无效，设置${bgConfig.themeName}背景色失败，不再重试。`);
            // 不再尝试重试或使用 setPageStyle
          } else if (err) { // 其他错误，尝试现有回退逻辑
            console.log('尝试只设置主背景色');
            try {
              // 某些版本的微信可能不支持backgroundColorTop和backgroundColorBottom
              wx.setBackgroundColor({
                backgroundColor: bgConfig.backgroundColor,
                success: () => {
                  console.log(`只设置主${bgConfig.themeName}背景色成功`);
                },
                fail: retryErr => {
                  console.error(`只设置主${bgConfig.themeName}背景色仍然失败:`, retryErr);
                  // 如果重试仍然失败，且是 INVALID_LOGIN，也不再尝试 setPageStyle
                  if (retryErr && retryErr.errMsg && retryErr.errMsg.includes('INVALID_LOGIN')) {
                    console.warn(`重试设置主${bgConfig.themeName}背景色因登录状态无效失败，不再尝试 setPageStyle。`);
                  } else {
                    // 最后的尝试：使用页面样式
                    try {
                      if (typeof wx.setPageStyle === 'function') {
                        wx.setPageStyle({
                          style: {
                            dark: theme === 'dark',
                            backgroundColor: bgConfig.backgroundColor
                          }
                        });
                        console.log(`尝试使用setPageStyle设置${bgConfig.themeName}背景色`);
                      }
                    } catch (pageStyleErr) {
                      console.error('setPageStyle失败:', pageStyleErr);
                    }
                  }
                }
              });
            } catch (innerErr) {
              console.error('尝试只设置主背景色时出错:', innerErr);
            }
          }
        },
        complete: () => {
          console.log('背景色更新操作完成');
        }
      });
    } catch (err) {
      console.error('更新背景色时出错:', err);
    }
  },

  // 更新界面样式
  updateInterfaceStyle(style) {
    if (style !== 'bubble' && style !== 'star') {
      console.error('无效的界面样式:', style);
      return;
    }

    this.globalData.interfaceStyle = style;
    wx.setStorageSync('interfaceStyle', style);
    console.log('界面样式已更新为:', style);

    // 通知页面刷新（如果有注册的回调函数）
    if (this.interfaceStyleChangeCallback) {
      this.interfaceStyleChangeCallback(style);
    }
  },

  // 更新主题模式
  updateThemeMode(mode) {
    if (mode !== 'light' && mode !== 'dark' && mode !== 'system') {
      console.error('无效的主题模式:', mode);
      return;
    }

    this.globalData.themeMode = mode;
    wx.setStorageSync('themeMode', mode);
    console.log('主题模式已更新为:', mode);

    // 根据模式设置当前主题
    let newTheme = 'light';
    if (mode === 'light') {
      newTheme = 'light';
    } else if (mode === 'dark') {
      newTheme = 'dark';
    } else if (mode === 'system') {
      // 获取系统当前主题
      try {
        const systemInfo = wx.getSystemInfoSync();
        newTheme = systemInfo.theme || 'light';
      } catch (err) {
        console.error('获取系统主题失败:', err);
        newTheme = 'light'; // 默认使用浅色主题
      }
    }

    this.globalData.currentTheme = newTheme;

    // 设置界面样式（深色模式用星星，浅色模式用气泡）
    const newInterfaceStyle = newTheme === 'dark' ? 'star' : 'bubble';
    this.globalData.interfaceStyle = newInterfaceStyle;
    wx.setStorageSync('interfaceStyle', newInterfaceStyle);

    // 应用主题样式
    this.applyThemeStyles(newTheme);

    // 保存主题更新时间，触发首页刷新
    wx.setStorageSync('themeUpdateTime', Date.now());

    // 获取当前页面栈
    try {
      const pages = getCurrentPages();
      // 遍历所有页面，尝试调用它们的onThemeChange方法（如果有的话）
      if (pages && pages.length > 0) {
        pages.forEach(page => {
          if (page && typeof page.onThemeChange === 'function') {
            page.onThemeChange(newTheme);
          }
        });
      }
    } catch (err) {
      console.error('刷新页面主题时出错:', err);
    }

    // 通知页面刷新（通过主题变化回调）
    if (this.themeModeChangeCallback) {
      this.themeModeChangeCallback(newTheme);
    }
  },

  // 更新导航栏样式 - 保留此方法以兼容现有代码
  updateNavigationBarStyle() {
    this.updateCurrentPageNavigationBar();
  },

  // 更新当前页面的导航栏样式
  updateCurrentPageNavigationBar() {
    const pages = getCurrentPages();
    if (pages.length === 0) {
      console.log('页面栈为空，无法更新导航栏样式');
      return;
    }
    const currentPage = pages[pages.length - 1];
    const currentPath = currentPage.route;
    console.log('更新导航栏样式 - 当前页面路径:', currentPath);

    try {
      // 获取当前页面
      const pages = getCurrentPages();

      // 检查页面栈是否为空
      if (!pages || pages.length === 0) {
        console.warn('页面栈为空，无法更新导航栏样式');
        return;
      }

      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage ? currentPage.route : '';

      console.log('更新导航栏样式 - 当前页面路径:', currentRoute);

      // 如果无法获取当前页面路径，记录警告但继续执行
      if (!currentRoute) {
        console.warn('无法获取当前页面路径，但仍将尝试更新导航栏样式');
      }

      // 定义导航栏样式配置
      const navBarConfig = this.globalData.currentTheme === 'dark'
        ? {
          frontColor: '#ffffff',
          backgroundColor: '#2C2C2E', // 石墨色
          animation: { duration: 300, timingFunc: 'easeIn' },
          themeName: '深色'
        }
        : {
          frontColor: '#000000',
          backgroundColor: '#ffffff',
          animation: { duration: 300, timingFunc: 'easeIn' },
          themeName: '浅色'
        };

      // 设置导航栏颜色
      wx.setNavigationBarColor({
        frontColor: navBarConfig.frontColor,
        backgroundColor: navBarConfig.backgroundColor,
        animation: navBarConfig.animation,
        success: () => {
          console.log(`设置${navBarConfig.themeName}导航栏样式成功`);
          // 设置成功后，重置更新标志
          this.globalData.navBarStyleNeedsUpdate = false;
        },
        fail: err => {
          console.error(`设置${navBarConfig.themeName}导航栏样式失败:`, err);

          // 错误处理：尝试不使用动画再次设置
          if (err && (err.errMsg || '').includes('animation')) {
            console.log('尝试不使用动画再次设置导航栏样式');
            wx.setNavigationBarColor({
              frontColor: navBarConfig.frontColor,
              backgroundColor: navBarConfig.backgroundColor,
              success: () => {
                console.log(`不使用动画设置${navBarConfig.themeName}导航栏样式成功`);
                this.globalData.navBarStyleNeedsUpdate = false;
              },
              fail: retryErr => {
                console.error(`不使用动画设置${navBarConfig.themeName}导航栏样式仍然失败:`, retryErr);
              }
            });
          }
        },
        complete: () => {
          console.log('导航栏样式更新操作完成');
        }
      });
    } catch (err) {
      console.error('更新导航栏样式时出错:', err);

      // 即使出错，也重置更新标志，避免反复尝试失败的操作
      this.globalData.navBarStyleNeedsUpdate = false;
    }
  },

  // 更新tabBar样式
  updateTabBarStyle(theme) {
    try {
      // 定义tabBar样式配置
      const tabBarConfig = theme === 'dark'
        ? {
          backgroundColor: '#2C2C2E', // 石墨色
          borderStyle: 'black',
          color: '#909090',
          selectedColor: '#60a5fa',
          themeName: '深色'
        }
        : {
          backgroundColor: '#ffffff',
          borderStyle: 'black',
          color: '#999999',
          selectedColor: '#3775F5',
          themeName: '浅色'
        };

      // 设置tabBar样式
      wx.setTabBarStyle({
        backgroundColor: tabBarConfig.backgroundColor,
        borderStyle: tabBarConfig.borderStyle,
        color: tabBarConfig.color,
        selectedColor: tabBarConfig.selectedColor,
        success: () => {
          console.log(`设置${tabBarConfig.themeName} tabBar样式成功`);
        },
        fail: err => {
          console.error(`设置${tabBarConfig.themeName} tabBar样式失败:`, err);

          // 错误处理：检查 INVALID_LOGIN 错误
          if (err && err.errMsg && err.errMsg.includes('INVALID_LOGIN')) {
            console.warn(`因登录状态无效，设置${tabBarConfig.themeName} tabBar样式失败，不再重试。`);
            // 不再进行延迟重试
          } else if (err) { // 其他错误，保持原有重试逻辑
            console.log('延迟重试设置tabBar样式');
            setTimeout(() => {
              wx.setTabBarStyle({
                backgroundColor: tabBarConfig.backgroundColor,
                borderStyle: tabBarConfig.borderStyle,
                color: tabBarConfig.color,
                selectedColor: tabBarConfig.selectedColor,
                success: () => {
                  console.log(`延迟重试设置${tabBarConfig.themeName} tabBar样式成功`);
                },
                fail: retryErr => {
                  console.error(`延迟重试设置${tabBarConfig.themeName} tabBar样式仍然失败:`, retryErr);
                }
              });
            }, 300);
          }
        }
      });
    } catch (err) {
      console.error('更新tabBar样式时出错:', err);
    }
  },

  // 获取当前主题
  getTheme() {
    return this.globalData.currentTheme;
  },

  // 登出
  logout() {
    console.log('App: 用户登出');

    // 使用认证服务进行登出
    authService.logout()
      .then(success => {
        console.log('登出成功:', success);

        // 清除其他可能与用户相关的缓存
        wx.removeStorageSync('theme_manager_cache');

        // 重置全局状态
        this.globalData.hasLogin = false;
        this.globalData.userInfo = null;

        // 可能需要通知页面进行相应更新，例如跳转到登录页
        // 考虑使用事件总线或页面栈管理来通知
        const pages = getCurrentPages();
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          // 避免在登录页重复跳转
          if (currentPage.route !== 'pages/login/phone' && currentPage.route !== 'pages/login/index') {
            wx.reLaunch({ url: '/pages/login/index' }); // 重新启动到登录页
          }
        }
      })
      .catch(error => {
        console.error('登出失败:', error);
        // 即使登出失败，也重置状态并跳转到登录页
        this.globalData.hasLogin = false;
        this.globalData.userInfo = null;

        const pages = getCurrentPages();
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          if (currentPage.route !== 'pages/login/phone' && currentPage.route !== 'pages/login/index') {
            wx.reLaunch({ url: '/pages/login/index' });
          }
        }
      });
  },

  /**
   * 检查用户是否已登录，如果未登录则跳转到登录页面
   * @param {Object} options - 配置选项
   * @param {String} options.from - 来源页面，用于登录后返回
   * @param {Boolean} options.showTransition - 是否显示过渡页面，默认为false
   * @param {Function} options.success - 已登录时的回调函数
   * @param {Function} options.fail - 未登录时的回调函数
   * @param {Boolean} options.redirect - 是否自动跳转到登录页面，默认为true
   * @returns {Promise<Boolean>} - 是否已登录
   */
  async checkLogin(options = {}) {
    // 设置默认选项
    options = {
      from: '',
      showTransition: false,
      success: null,
      fail: null,
      redirect: true,
      ...options
    };

    // 使用令牌管理器检查登录状态
    const isLoggedIn = await tokenManager.isLoggedIn();

    if (isLoggedIn) {
      // 已登录，执行成功回调
      if (typeof options.success === 'function') {
        const token = await tokenManager.getToken();
        options.success(token);
      }
      return true;
    } else {
      // 未登录，执行失败回调
      if (typeof options.fail === 'function') {
        options.fail();
      }

      // 如果不需要自动跳转，直接返回
      if (!options.redirect) {
        return false;
      }

      // 构建跳转URL
      let url = '/pages/login/phone';
      if (options.from) {
        url += `?from=${options.from}`;
      }

      if (options.showTransition) {
        // 显示过渡页面，延迟跳转
        setTimeout(() => {
          wx.navigateTo({ url });
        }, 1500);
      } else {
        // 直接跳转到手机号登录页面
        wx.navigateTo({ url });
      }

      return false;
    }
  },

  // 检查登录状态
  async checkLoginState() {
    try {
      // 使用令牌管理器检查登录状态
      const isLoggedIn = await tokenManager.isLoggedIn();

      if (isLoggedIn) {
        // 获取用户信息
        const userInfo = wx.getStorageSync('userInfo');

        if (userInfo) {
          console.log('App: 用户已登录且Token有效');
          this.globalData.hasLogin = true;
          this.globalData.userInfo = userInfo;

          // 如果有等待的回调，执行它
          if (this.loginReadyCallback) {
            // 使用 setTimeout 确保页面 onLoad 之后的逻辑能接收到回调
            setTimeout(() => {
              if (this.loginReadyCallback) { // 再次检查，防止已被清除
                this.loginReadyCallback(true, userInfo);
                this.loginReadyCallback = null; // 清除回调，防止重复执行
              }
            }, 0);
          }
        } else {
          // 如果有效的令牌但没有用户信息，尝试获取用户信息
          const userInfo = await authService.getCurrentUser(true);

          if (userInfo) {
            this.globalData.hasLogin = true;
            this.globalData.userInfo = userInfo;

            if (this.loginReadyCallback) {
              setTimeout(() => {
                if (this.loginReadyCallback) {
                  this.loginReadyCallback(true, userInfo);
                  this.loginReadyCallback = null;
                }
              }, 0);
            }
          } else {
            this._handleNotLoggedIn();
          }
        }
      } else {
        this._handleNotLoggedIn();
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      this._handleNotLoggedIn();
    }
  },

  // 处理未登录状态
  _handleNotLoggedIn() {
    console.log('App: 用户未登录或Token无效');
    this.globalData.hasLogin = false;
    this.globalData.userInfo = null;

    // 如果有等待的回调，通知未登录
    if (this.loginReadyCallback) {
      setTimeout(() => {
        if (this.loginReadyCallback) {
          this.loginReadyCallback(false, null);
          this.loginReadyCallback = null;
        }
      }, 0);
    }
  },

  // 新增 ensureLoginReady
  ensureLoginReady(callback) {
    if (typeof callback !== 'function') return; // 确保 callback 是函数

    if (this.globalData.hasLogin) {
      // 如果已经登录，异步回调以保持行为一致性
      setTimeout(() => {
        callback(true, this.globalData.userInfo);
      }, 0);
    } else {
      // 如果未登录，设置回调
      // 简单处理：后设置的回调会覆盖之前的
      this.loginReadyCallback = callback;
      // 可以再次触发一次检查，以防 app.onLaunch 比页面 onLoad 慢
      // 但要避免无限循环检查，可在 checkLoginState 中加锁
      this.checkLoginState(); // 再次检查一次
    }
  },

  // 登录成功处理
  loginSuccess(userInfo, tokenData) {
    console.log('App: 登录成功，存储用户信息');

    // 存储用户信息
    wx.setStorageSync('userInfo', userInfo);

    // 使用令牌管理器存储令牌信息
    if (tokenData && tokenData.token) {
      tokenManager.saveTokens({
        token: tokenData.token,
        refreshToken: tokenData.refreshToken,
        expiresIn: tokenData.expiresIn,
        userId: tokenData.userId || (userInfo && userInfo.id)
      });
    }

    // 更新全局状态
    this.globalData.hasLogin = true;
    this.globalData.userInfo = userInfo;

    // 执行等待的回调
    if (this.loginReadyCallback) {
      setTimeout(() => {
        if (this.loginReadyCallback) {
          this.loginReadyCallback(true, userInfo);
          this.loginReadyCallback = null;
        }
      }, 0);
    }
  }
});