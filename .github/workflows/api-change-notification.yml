name: API Change Notification

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'controllers/**/*.js'
      - 'routes/**/*.js'
      - 'swagger/**/*.js'
      - 'docs/api-spec.json'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'controllers/**/*.js'
      - 'routes/**/*.js'
      - 'swagger/**/*.js'
      - 'docs/api-spec.json'

jobs:
  detect-and-notify:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v2
      with:
        fetch-depth: 2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '14.x'
    
    - name: Install dependencies
      run: |
        npm ci
        npm install -g openapi-diff chalk commander nodemailer @slack/web-api dotenv
    
    - name: Detect API changes
      run: node scripts/detect-api-changes.js --output api-changes.json
    
    - name: Upload changes report
      uses: actions/upload-artifact@v2
      with:
        name: api-changes
        path: api-changes.json
    
    - name: Send notifications (on push to main/develop)
      if: github.event_name == 'push'
      run: node scripts/send-api-notifications.js --input api-changes.json
      env:
        SMTP_HOST: ${{ secrets.SMTP_HOST }}
        SMTP_PORT: ${{ secrets.SMTP_PORT }}
        SMTP_USER: ${{ secrets.SMTP_USER }}
        SMTP_PASS: ${{ secrets.SMTP_PASS }}
        EMAIL_FROM: ${{ secrets.EMAIL_FROM }}
        EMAIL_RECIPIENTS: ${{ secrets.EMAIL_RECIPIENTS }}
        SLACK_TOKEN: ${{ secrets.SLACK_TOKEN }}
        SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
    
    - name: Comment on PR (on pull request)
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v5
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const fs = require('fs');
          
          try {
            // 读取变更报告
            const report = JSON.parse(fs.readFileSync('api-changes.json', 'utf8'));
            
            // 如果没有变更，则不添加评论
            if (report.summary.total === 0) {
              console.log('没有API变更，不添加评论');
              return;
            }
            
            // 生成评论内容
            let comment = `## API变更检测结果\n\n`;
            comment += `### 变更摘要\n\n`;
            comment += `- 总变更数: ${report.summary.total}\n`;
            comment += `- 紧急变更: ${report.summary.urgent}\n`;
            comment += `- 警告变更: ${report.summary.warning}\n`;
            comment += `- 信息变更: ${report.summary.info}\n\n`;
            
            // 添加紧急变更详情
            if (report.summary.urgent > 0) {
              comment += `### 紧急变更\n\n`;
              for (const change of report.changes.urgent) {
                comment += `#### ${change.path}\n\n`;
                comment += `- 类型: ${change.type}\n`;
                comment += `- 消息: ${change.message}\n`;
                if (change.oldValue && change.newValue) {
                  comment += `- 旧值: \`${JSON.stringify(change.oldValue)}\`\n`;
                  comment += `- 新值: \`${JSON.stringify(change.newValue)}\`\n`;
                }
                comment += `\n`;
              }
            }
            
            // 添加警告变更详情
            if (report.summary.warning > 0) {
              comment += `### 警告变更\n\n`;
              for (const change of report.changes.warning) {
                comment += `#### ${change.path}\n\n`;
                comment += `- 类型: ${change.type}\n`;
                comment += `- 消息: ${change.message}\n`;
                if (change.oldValue && change.newValue) {
                  comment += `- 旧值: \`${JSON.stringify(change.oldValue)}\`\n`;
                  comment += `- 新值: \`${JSON.stringify(change.newValue)}\`\n`;
                }
                comment += `\n`;
              }
            }
            
            // 添加信息变更详情
            if (report.summary.info > 0) {
              comment += `### 信息变更\n\n`;
              for (const change of report.changes.info) {
                comment += `#### ${change.path}\n\n`;
                comment += `- 类型: ${change.type}\n`;
                comment += `- 消息: ${change.message}\n`;
                if (change.oldValue && change.newValue) {
                  comment += `- 旧值: \`${JSON.stringify(change.oldValue)}\`\n`;
                  comment += `- 新值: \`${JSON.stringify(change.newValue)}\`\n`;
                }
                comment += `\n`;
              }
            }
            
            // 添加评论
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
            
            // 如果有紧急变更，添加标签
            if (report.summary.urgent > 0) {
              github.rest.issues.addLabels({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                labels: ['api-breaking-change']
              });
            }
          } catch (error) {
            console.error('添加PR评论时出错:', error);
          }
