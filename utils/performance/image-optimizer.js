/**
 * 图片优化工具类
 * 提供图片压缩、格式转换、尺寸调整等功能
 */

class ImageOptimizer {
  constructor() {
    this.supportedFormats = ['webp', 'jpg', 'png'];
    this.defaultQuality = 80;
    this.maxWidth = 1920;
    this.maxHeight = 1080;

    // 检查WebP支持
    this.webpSupported = this._checkWebpSupport();

    // 获取设备信息
    this.deviceInfo = this._getDeviceInfo();
  }

  /**
   * 检查WebP支持
   */
  _checkWebpSupport() {
    try {
      return wx.canIUse('image.webp') || false;
    } catch (err) {
      console.error('检查WebP支持失败:', err);
      return false;
    }
  }

  /**
   * 获取设备信息
   */
  _getDeviceInfo() {
    try {
      const windowInfo = wx.getWindowInfo();
      const deviceInfo = wx.getDeviceInfo();
      return {
        pixelRatio: windowInfo.pixelRatio || 2,
        screenWidth: windowInfo.screenWidth || 375,
        screenHeight: windowInfo.screenHeight || 667,
        platform: deviceInfo.platform || 'unknown'
      };
    } catch (err) {
      console.error('获取设备信息失败:', err);
      return {
        pixelRatio: 2,
        screenWidth: 375,
        screenHeight: 667,
        platform: 'unknown'
      };
    }
  }

  /**
   * 优化图片URL
   * @param {string} url - 原始图片URL
   * @param {Object} options - 优化选项
   * @returns {string} 优化后的图片URL
   */
  optimizeImageUrl(url, options = {}) {
    if (!url || typeof url !== 'string') {
      return '';
    }

    // 如果是本地图片或base64，直接返回
    if (url.startsWith('/') || url.startsWith('data:') || url.startsWith('blob:')) {
      return url;
    }

    const {
      width = 0,
      height = 0,
      quality = this.defaultQuality,
      format = 'auto',
      mode = 'fit'
    } = options;

    // 检查是否为支持的CDN
    if (!this._isSupportedCDN(url)) {
      return url;
    }

    // 计算实际需要的尺寸
    const optimizedSize = this._calculateOptimalSize(width, height);

    // 构建优化参数
    const params = this._buildOptimizationParams({
      width: optimizedSize.width,
      height: optimizedSize.height,
      quality,
      format,
      mode
    });

    // 添加参数到URL
    return this._appendParamsToUrl(url, params);
  }

  /**
   * 检查是否为支持的CDN
   */
  _isSupportedCDN(url) {
    const supportedCDNs = [
      'qcloud.com',
      'aliyuncs.com',
      'amazonaws.com',
      'cloudfront.net',
      'fastly.com',
      'cdn.example.com'
    ];

    return supportedCDNs.some(cdn => url.includes(cdn));
  }

  /**
   * 计算最优尺寸
   */
  _calculateOptimalSize(width, height) {
    const { pixelRatio } = this.deviceInfo;

    let optimalWidth = width;
    let optimalHeight = height;

    // 如果指定了尺寸，考虑设备像素比
    if (width > 0) {
      optimalWidth = Math.round(width * pixelRatio);
      // 限制最大宽度
      optimalWidth = Math.min(optimalWidth, this.maxWidth);
    }

    if (height > 0) {
      optimalHeight = Math.round(height * pixelRatio);
      // 限制最大高度
      optimalHeight = Math.min(optimalHeight, this.maxHeight);
    }

    return {
      width: optimalWidth,
      height: optimalHeight
    };
  }

  /**
   * 构建优化参数
   */
  _buildOptimizationParams(options) {
    const { width, height, quality, format, mode } = options;
    const params = [];

    // 尺寸参数
    if (width > 0) params.push(`w=${width}`);
    if (height > 0) params.push(`h=${height}`);

    // 质量参数
    if (quality && quality !== this.defaultQuality) {
      params.push(`q=${quality}`);
    }

    // 格式参数
    let targetFormat = format;
    if (format === 'auto') {
      targetFormat = this.webpSupported ? 'webp' : 'jpg';
    }
    if (targetFormat && targetFormat !== 'original') {
      params.push(`format=${targetFormat}`);
    }

    // 模式参数
    if (mode && mode !== 'fit') {
      params.push(`mode=${mode}`);
    }

    return params;
  }

  /**
   * 添加参数到URL
   */
  _appendParamsToUrl(url, params) {
    if (!params || params.length === 0) {
      return url;
    }

    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}${params.join('&')}`;
  }

  /**
   * 预加载图片
   * @param {string} url - 图片URL
   * @returns {Promise} 预加载Promise
   */
  preloadImage(url) {
    return new Promise((resolve, reject) => {
      if (!url) {
        reject(new Error('图片URL不能为空'));
        return;
      }

      // 创建图片对象进行预加载
      const image = wx.createImage();

      image.onload = () => {
        resolve({
          url,
          width: image.width,
          height: image.height
        });
      };

      image.onerror = err => {
        reject(new Error(`图片预加载失败: ${err.message || '未知错误'}`));
      };

      image.src = url;
    });
  }

  /**
   * 批量预加载图片
   * @param {Array} urls - 图片URL数组
   * @param {Object} options - 选项
   * @returns {Promise} 批量预加载Promise
   */
  preloadImages(urls, options = {}) {
    const { concurrency = 3, timeout = 10000 } = options;

    if (!Array.isArray(urls) || urls.length === 0) {
      return Promise.resolve([]);
    }

    // 分批处理
    const batches = this._createBatches(urls, concurrency);

    return this._processBatches(batches, timeout);
  }

  /**
   * 创建批次
   */
  _createBatches(urls, batchSize) {
    const batches = [];
    for (let i = 0; i < urls.length; i += batchSize) {
      batches.push(urls.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 处理批次
   */
  async _processBatches(batches, timeout) {
    const results = [];

    for (const batch of batches) {
      const batchPromises = batch.map(url =>
        this._preloadWithTimeout(url, timeout)
      );

      try {
        const batchResults = await Promise.allSettled(batchPromises);
        results.push(...batchResults);
      } catch (err) {
        console.error('批次处理失败:', err);
      }
    }

    return results;
  }

  /**
   * 带超时的预加载
   */
  _preloadWithTimeout(url, timeout) {
    return Promise.race([
      this.preloadImage(url),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('预加载超时')), timeout)
      )
    ]);
  }

  /**
   * 获取图片信息
   * @param {string} url - 图片URL
   * @returns {Promise} 图片信息Promise
   */
  getImageInfo(url) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: url,
        success: res => {
          resolve({
            width: res.width,
            height: res.height,
            path: res.path,
            orientation: res.orientation,
            type: res.type
          });
        },
        fail: err => {
          reject(new Error(`获取图片信息失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  }

  /**
   * 压缩图片
   * @param {string} src - 图片路径
   * @param {Object} options - 压缩选项
   * @returns {Promise} 压缩结果Promise
   */
  compressImage(src, options = {}) {
    const {
      quality = 80,
      width,
      height,
      compressedWidth,
      compressedHeight
    } = options;

    return new Promise((resolve, reject) => {
      wx.compressImage({
        src,
        quality,
        width,
        height,
        compressedWidth,
        compressedHeight,
        success: res => {
          resolve({
            tempFilePath: res.tempFilePath,
            size: res.size || 0
          });
        },
        fail: err => {
          reject(new Error(`图片压缩失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  }
}

// 创建单例实例
const imageOptimizer = new ImageOptimizer();

export default imageOptimizer;
