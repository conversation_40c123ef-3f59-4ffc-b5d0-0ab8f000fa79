/**
 * API模块集合类型定义
 */

import { ApiResponse } from '../index';

/**
 * 分页参数接口
 */
export interface PaginationParams {
  /**
   * 页码
   */
  page?: number;

  /**
   * 每页数量
   */
  pageSize?: number;
}

/**
 * 排序参数接口
 */
export interface SortParams {
  /**
   * 排序字段
   */
  sortBy?: string;

  /**
   * 排序方向
   */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 查询参数接口
 */
export interface QueryParams extends PaginationParams, SortParams {
  /**
   * 搜索关键词
   */
  search?: string;

  /**
   * 其他参数
   */
  [key: string]: any;
}

/**
 * 主题API接口
 */
export interface ThemeApi {
  /**
   * 获取主题列表
   * @param params - 查询参数
   * @param config - 请求配置
   * @returns 请求Promise
   */
  getThemes(params?: QueryParams, config?: any): Promise<ApiResponse>;

  /**
   * 获取主题详情
   * @param id - 主题ID
   * @param config - 请求配置
   * @returns 请求Promise
   */
  getTheme(id: number, config?: any): Promise<ApiResponse>;

  /**
   * 创建主题
   * @param data - 主题数据
   * @param config - 请求配置
   * @returns 请求Promise
   */
  createTheme(data: any, config?: any): Promise<ApiResponse>;

  /**
   * 更新主题
   * @param id - 主题ID
   * @param data - 主题数据
   * @param config - 请求配置
   * @returns 请求Promise
   */
  updateTheme(id: number, data: any, config?: any): Promise<ApiResponse>;

  /**
   * 删除主题
   * @param id - 主题ID
   * @param config - 请求配置
   * @returns 请求Promise
   */
  deleteTheme(id: number, config?: any): Promise<ApiResponse>;

  /**
   * 批量删除主题
   * @param ids - 主题ID数组
   * @param config - 请求配置
   * @returns 请求Promise
   */
  batchDeleteThemes(ids: number[], config?: any): Promise<ApiResponse>;

  /**
   * 清除主题缓存
   */
  clearCache(): void;
}

/**
 * 标签API接口
 */
export interface TagApi {
  /**
   * 获取标签列表
   * @param params - 查询参数
   * @param config - 请求配置
   * @returns 请求Promise
   */
  getTags(params?: QueryParams, config?: any): Promise<ApiResponse>;

  /**
   * 获取标签详情
   * @param id - 标签ID
   * @param config - 请求配置
   * @returns 请求Promise
   */
  getTag(id: number, config?: any): Promise<ApiResponse>;

  /**
   * 创建标签
   * @param data - 标签数据
   * @param config - 请求配置
   * @returns 请求Promise
   */
  createTag(data: any, config?: any): Promise<ApiResponse>;

  /**
   * 更新标签
   * @param id - 标签ID
   * @param data - 标签数据
   * @param config - 请求配置
   * @returns 请求Promise
   */
  updateTag(id: number, data: any, config?: any): Promise<ApiResponse>;

  /**
   * 删除标签
   * @param id - 标签ID
   * @param config - 请求配置
   * @returns 请求Promise
   */
  deleteTag(id: number, config?: any): Promise<ApiResponse>;

  /**
   * 清除标签缓存
   */
  clearCache(): void;
}

/**
 * API接口
 */
export interface Api {
  /**
   * 主题API
   */
  theme: ThemeApi;

  /**
   * 标签API
   */
  tag: TagApi;

  /**
   * 学习计划API
   */
  learningPlan: any;

  /**
   * 练习API
   */
  exercise: any;

  /**
   * 观点API
   */
  insight: any;

  /**
   * 笔记API
   */
  note: any;

  /**
   * 每日内容API
   */
  dailyContent: any;

  /**
   * 认证API
   */
  auth: any;

  /**
   * 用户API
   */
  user: any;

  /**
   * 广场API
   */
  square: any;

  /**
   * 清理API
   */
  cleanup: any;

  /**
   * 清除所有缓存
   */
  clearCache(): void;

  /**
   * 清除特定实体的缓存
   * @param entity - 实体名称
   */
  clearEntityCache(entity: string): void;
}

/**
 * API模块集合
 */
declare const api: Api;

export default api;
