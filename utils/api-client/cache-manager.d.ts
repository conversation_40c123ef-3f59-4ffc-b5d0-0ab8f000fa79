/**
 * 缓存管理器类型定义
 */

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  /**
   * 缓存数据
   */
  data: T;

  /**
   * 过期时间戳
   */
  expiry: number;
}

/**
 * 缓存管理器类
 */
export class CacheManager {
  /**
   * 内存缓存
   */
  memoryCache: Map<string, CacheItem>;

  /**
   * 构造函数
   */
  constructor();

  /**
   * 生成缓存键
   * @param url - 请求URL
   * @param data - 请求数据
   * @returns 缓存键
   */
  generateKey(url: string, data?: any): string;

  /**
   * 获取缓存
   * @param key - 缓存键
   * @returns 缓存数据，如果不存在或已过期则返回null
   */
  get<T = any>(key: string): T | null;

  /**
   * 设置缓存
   * @param key - 缓存键
   * @param data - 缓存数据
   * @param ttl - 缓存时间（毫秒）
   */
  set<T = any>(key: string, data: T, ttl?: number): void;

  /**
   * 删除缓存
   * @param key - 缓存键
   */
  remove(key: string): void;

  /**
   * 清除缓存
   * @param pattern - 缓存键模式
   */
  clear(pattern?: string): void;

  /**
   * 从本地存储加载缓存
   */
  loadFromStorage(): void;

  /**
   * 保存缓存到本地存储
   */
  saveToStorage(): void;
}
