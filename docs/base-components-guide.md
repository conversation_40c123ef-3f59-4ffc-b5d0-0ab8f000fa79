# NebulaLearn UI 基础组件使用指南

## 概述

NebulaLearn UI 基础组件库提供了一套完整的、符合设计规范的基础UI组件。所有组件都遵循《AIBUBB视觉设计文档 V2.0》的设计规范，支持亮色/暗色模式切换，并经过性能优化。

## 设计原则

- **一致性**：所有组件遵循统一的设计语言和交互规范
- **可访问性**：支持WCAG AA级标准，确保良好的可访问性
- **性能优先**：组件经过优化，确保流畅的用户体验
- **主题支持**：完整支持亮色/暗色模式切换
- **响应式**：适配不同屏幕尺寸和设备

## 组件分类

### 基础展示组件

#### Button 按钮
用于触发操作的交互元素。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-button": "/components/base/button/index"
  }
}
```

**基本用法**：
```html
<!-- 主要按钮 -->
<nl-button type="primary" text="确认" bind:tap="handleConfirm"></nl-button>

<!-- 次要按钮 -->
<nl-button type="secondary" text="取消" bind:tap="handleCancel"></nl-button>

<!-- 危险按钮 -->
<nl-button type="danger" text="删除" bind:tap="handleDelete"></nl-button>

<!-- 禁用状态 -->
<nl-button type="primary" text="提交" disabled="{{true}}"></nl-button>

<!-- 加载状态 -->
<nl-button type="primary" text="提交中" loading="{{true}}"></nl-button>
```

**属性说明**：
- `type`: 按钮类型 (primary/secondary/danger/text)
- `size`: 按钮尺寸 (small/medium/large)
- `text`: 按钮文本
- `disabled`: 是否禁用
- `loading`: 是否显示加载状态
- `icon`: 图标名称
- `block`: 是否为块级按钮

#### Card 卡片
用于内容分组和展示的容器组件。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-card": "/components/base/card/index"
  }
}
```

**基本用法**：
```html
<!-- 基础卡片 -->
<nl-card title="卡片标题" subtitle="卡片副标题">
  <view>卡片内容</view>
</nl-card>

<!-- 带操作的卡片 -->
<nl-card title="学习计划" showAction="{{true}}" bind:action="handleAction">
  <view>计划内容...</view>
</nl-card>

<!-- 阴影卡片 -->
<nl-card title="重要信息" shadow="medium">
  <view>重要内容...</view>
</nl-card>
```

**属性说明**：
- `title`: 卡片标题
- `subtitle`: 卡片副标题
- `shadow`: 阴影级别 (none/small/medium/large)
- `showAction`: 是否显示操作按钮
- `actionText`: 操作按钮文本
- `padding`: 内边距级别 (none/small/medium/large)

#### Text 文本
用于文本展示的组件，支持多种样式和状态。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-text": "/components/base/text/index"
  }
}
```

**基本用法**：
```html
<!-- 标题文本 -->
<nl-text type="title" text="页面标题"></nl-text>

<!-- 正文文本 -->
<nl-text type="body" text="这是正文内容"></nl-text>

<!-- 说明文本 -->
<nl-text type="caption" text="这是说明文字" color="secondary"></nl-text>

<!-- 可截断文本 -->
<nl-text text="这是一段很长的文本..." ellipsis="{{true}}" maxLines="2"></nl-text>
```

**属性说明**：
- `type`: 文本类型 (title/subtitle/body/caption)
- `text`: 文本内容
- `color`: 文本颜色 (primary/secondary/success/warning/danger)
- `align`: 对齐方式 (left/center/right)
- `ellipsis`: 是否启用文本截断
- `maxLines`: 最大行数
- `bold`: 是否加粗
- `italic`: 是否斜体

#### Icon 图标
用于展示图标的组件。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-icon": "/components/base/icon/index"
  }
}
```

**基本用法**：
```html
<!-- 基础图标 -->
<nl-icon name="home" size="24"></nl-icon>

<!-- 彩色图标 -->
<nl-icon name="heart" color="danger" size="20"></nl-icon>

<!-- 可点击图标 -->
<nl-icon name="settings" size="24" bind:tap="handleSettings"></nl-icon>
```

**属性说明**：
- `name`: 图标名称
- `size`: 图标尺寸 (16/20/24/32)
- `color`: 图标颜色 (primary/secondary/success/warning/danger)
- `active`: 是否为激活状态

### 标签和徽章组件

#### Tag 标签
用于标记和分类的组件。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-tag": "/components/base/tag/index"
  }
}
```

**基本用法**：
```html
<!-- 基础标签 -->
<nl-tag text="默认标签"></nl-tag>

<!-- 彩色标签 -->
<nl-tag text="成功" type="success"></nl-tag>
<nl-tag text="警告" type="warning"></nl-tag>
<nl-tag text="危险" type="danger"></nl-tag>

<!-- 可关闭标签 -->
<nl-tag text="可关闭" closable="{{true}}" bind:close="handleClose"></nl-tag>

<!-- 朴素标签 -->
<nl-tag text="朴素标签" plain="{{true}}"></nl-tag>
```

**属性说明**：
- `text`: 标签文本
- `type`: 标签类型 (default/primary/success/warning/danger)
- `size`: 标签尺寸 (small/medium/large)
- `closable`: 是否可关闭
- `plain`: 是否为朴素样式
- `round`: 是否为圆角样式

#### Badge 徽章
用于显示数字或状态的小标记。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-badge": "/components/base/badge/index"
  }
}
```

**基本用法**：
```html
<!-- 数字徽章 -->
<nl-badge value="5">
  <nl-icon name="message" size="24"></nl-icon>
</nl-badge>

<!-- 圆点徽章 -->
<nl-badge dot="{{true}}">
  <nl-icon name="notification" size="24"></nl-icon>
</nl-badge>

<!-- 文本徽章 -->
<nl-badge value="new" type="danger">
  <view class="content">内容</view>
</nl-badge>
```

**属性说明**：
- `value`: 徽章内容（数字或文本）
- `max`: 最大数字显示
- `dot`: 是否显示为圆点
- `type`: 徽章类型 (default/primary/success/warning/danger)
- `position`: 徽章位置 (top-right/top-left/bottom-right/bottom-left)

### 分割和布局组件

#### Divider 分割线
用于内容分割的组件。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-divider": "/components/base/divider/index"
  }
}
```

**基本用法**：
```html
<!-- 水平分割线 -->
<nl-divider></nl-divider>

<!-- 带文字的分割线 -->
<nl-divider text="或者" position="center"></nl-divider>

<!-- 虚线分割线 -->
<nl-divider type="dashed"></nl-divider>

<!-- 垂直分割线 -->
<nl-divider direction="vertical"></nl-divider>
```

**属性说明**：
- `direction`: 分割线方向 (horizontal/vertical)
- `type`: 分割线类型 (solid/dashed/dotted)
- `text`: 分割线文字
- `position`: 文字位置 (left/center/right)
- `color`: 分割线颜色

### 性能优化组件

#### OptimizedImage 优化图片
支持懒加载、WebP格式、CDN优化的图片组件。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-optimized-image": "/components/base/optimized-image/index"
  }
}
```

**基本用法**：
```html
<!-- 基础用法 -->
<nl-optimized-image 
  src="{{imageUrl}}" 
  width="200" 
  height="200"
  mode="aspectFill">
</nl-optimized-image>

<!-- 禁用优化 -->
<nl-optimized-image 
  src="{{imageUrl}}" 
  useCdn="{{false}}"
  webp="{{false}}">
</nl-optimized-image>
```

**属性说明**：
- `src`: 图片URL
- `width`: 图片宽度
- `height`: 图片高度
- `mode`: 图片模式
- `lazyLoad`: 是否懒加载
- `webp`: 是否使用WebP格式
- `useCdn`: 是否使用CDN优化

#### VirtualList 虚拟列表
用于高效渲染大量数据的列表组件。

**引入方式**：
```json
{
  "usingComponents": {
    "nl-virtual-list": "/components/base/virtual-list/index"
  }
}
```

**基本用法**：
```html
<nl-virtual-list 
  items="{{listData}}" 
  itemHeight="100"
  bind:itemtap="handleItemTap"
  bind:loadmore="handleLoadMore">
  <template name="item">
    <view class="list-item">
      <text>{{item.title}}</text>
    </view>
  </template>
</nl-virtual-list>
```

**属性说明**：
- `items`: 列表数据
- `itemHeight`: 项目高度
- `bufferSize`: 缓冲区大小
- `loading`: 是否正在加载
- `noMore`: 是否没有更多数据

## 主题切换

所有基础组件都支持主题切换。在应用中使用主题管理器：

```javascript
// 切换到暗色模式
const themeManager = require('/utils/theme-manager');
themeManager.setTheme('dark');

// 切换到亮色模式
themeManager.setTheme('light');
```

## 最佳实践

1. **性能优化**：
   - 使用虚拟列表处理长列表
   - 使用优化图片组件处理图片
   - 避免频繁的setData调用

2. **可访问性**：
   - 为交互元素添加适当的aria标签
   - 确保颜色对比度符合标准
   - 支持键盘导航

3. **响应式设计**：
   - 使用rpx单位确保适配
   - 考虑不同屏幕尺寸的布局
   - 测试在不同设备上的表现

4. **组件组合**：
   - 合理组合基础组件构建复杂UI
   - 保持组件的单一职责
   - 使用插槽提供灵活性

## 常见问题

**Q: 如何自定义组件样式？**
A: 使用`customClass`属性或在页面中定义样式覆盖。

**Q: 组件在暗色模式下显示异常？**
A: 检查是否正确引入了主题变量，确保使用CSS变量定义颜色。

**Q: 虚拟列表性能仍然不佳？**
A: 检查项目高度设置是否正确，考虑减少缓冲区大小或优化项目内容。

## 更新日志

- **v1.3.0**: 添加优化图片和虚拟列表组件
- **v1.2.0**: 完善主题切换支持
- **v1.1.0**: 添加标签和徽章组件
- **v1.0.0**: 基础组件发布
