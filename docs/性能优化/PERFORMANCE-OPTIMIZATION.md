# 前端性能优化指南

本文档专注于AIBUBB微信小程序前端的性能优化策略和实施方案。

## 概述

前端性能优化是提升用户体验的关键因素。我们从以下几个维度进行优化：
- **渲染性能**: Canvas动画、组件渲染、页面切换
- **资源加载**: 图片优化、代码分包、缓存策略
- **内存管理**: 对象池、垃圾回收、内存泄漏防护
- **用户交互**: 响应速度、动画流畅度、触摸反馈

## Canvas动画性能优化

### 1. 离屏渲染技术
```javascript
// 使用离屏Canvas预渲染复杂图形
const offscreenCanvas = wx.createOffscreenCanvas();
const offscreenCtx = offscreenCanvas.getContext('2d');

// 预渲染泡泡纹理
function prerenderBubbleTexture() {
  // 在离屏Canvas上绘制泡泡
  offscreenCtx.beginPath();
  offscreenCtx.arc(50, 50, 40, 0, Math.PI * 2);
  offscreenCtx.fillStyle = 'rgba(100, 150, 255, 0.8)';
  offscreenCtx.fill();

  return offscreenCanvas;
}
```

### 2. 对象池管理
```javascript
// 泡泡对象池，避免频繁创建销毁对象
class BubblePool {
  constructor(size = 100) {
    this.pool = [];
    this.activeObjects = [];

    // 预创建对象
    for (let i = 0; i < size; i++) {
      this.pool.push(this.createBubble());
    }
  }

  getBubble() {
    return this.pool.pop() || this.createBubble();
  }

  releaseBubble(bubble) {
    bubble.reset();
    this.pool.push(bubble);
  }
}
```

### 3. 渲染优化策略
- **视窗裁剪**: 只渲染可见区域的元素
- **帧率控制**: 根据设备性能动态调整帧率
- **批量绘制**: 合并相似的绘制操作

## 组件渲染优化

### 1. 虚拟列表实现
```javascript
// 长列表虚拟化，只渲染可见项
Component({
  data: {
    visibleItems: [],
    startIndex: 0,
    endIndex: 10
  },

  updateVisibleItems(scrollTop) {
    const itemHeight = 100;
    const containerHeight = 600;

    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      this.data.allItems.length
    );

    this.setData({
      visibleItems: this.data.allItems.slice(startIndex, endIndex),
      startIndex,
      endIndex
    });
  }
});
```

### 2. 组件懒加载
```javascript
// 组件按需加载
const LazyComponent = {
  attached() {
    // 使用Intersection Observer检测组件是否进入视窗
    this.observer = wx.createIntersectionObserver(this);
    this.observer.observe('.lazy-component', (res) => {
      if (res.intersectionRatio > 0) {
        this.loadComponent();
        this.observer.disconnect();
      }
    });
  }
};
```

## 图片资源优化

### 1. 图片格式优化
```javascript
// 自动选择最优图片格式
function getOptimizedImageUrl(originalUrl, width, height) {
  const devicePixelRatio = wx.getSystemInfoSync().pixelRatio;
  const actualWidth = width * devicePixelRatio;
  const actualHeight = height * devicePixelRatio;

  // 支持WebP的设备使用WebP格式
  const supportsWebP = wx.canIUse('image.webp');
  const format = supportsWebP ? 'webp' : 'jpg';

  return `${originalUrl}?w=${actualWidth}&h=${actualHeight}&f=${format}&q=80`;
}
```

### 2. 图片懒加载
```javascript
// 图片懒加载组件
Component({
  properties: {
    src: String,
    lazyLoad: {
      type: Boolean,
      value: true
    }
  },

  attached() {
    if (this.data.lazyLoad) {
      this.setupLazyLoad();
    }
  },

  setupLazyLoad() {
    this.observer = wx.createIntersectionObserver(this);
    this.observer.observe('.lazy-image', (res) => {
      if (res.intersectionRatio > 0) {
        this.setData({ shouldLoad: true });
        this.observer.disconnect();
      }
    });
  }
});
```

## 内存管理优化

### 1. 内存泄漏防护
```javascript
// 页面卸载时清理资源
Page({
  onUnload() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer);
    }

    // 清理事件监听
    if (this.observer) {
      this.observer.disconnect();
    }

    // 清理Canvas上下文
    if (this.canvasContext) {
      this.canvasContext = null;
    }
  }
});
```

### 2. 智能缓存策略
```javascript
// LRU缓存实现
class LRUCache {
  constructor(capacity) {
    this.capacity = capacity;
    this.cache = new Map();
  }

  get(key) {
    if (this.cache.has(key)) {
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}
```

## 代码分包优化

### 1. 分包策略
```javascript
// app.json 分包配置
{
  "pages": [
    "pages/index/index",
    "pages/learn/index",
    "pages/square/index",
    "pages/profile/index"
  ],
  "subPackages": [
    {
      "root": "packages/content",
      "pages": [
        "pages/note-detail/index",
        "pages/note-edit/index"
      ]
    },
    {
      "root": "packages/learning",
      "pages": [
        "pages/create-plan/index",
        "pages/plan-detail/index"
      ]
    }
  ],
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["packages/content"]
    }
  }
}
```

### 2. 按需加载
```javascript
// 动态导入组件
function loadComponent(componentName) {
  return new Promise((resolve) => {
    wx.loadSubpackage({
      name: componentName,
      success: () => {
        resolve();
      },
      fail: (err) => {
        console.error('分包加载失败:', err);
      }
    });
  });
}
```

## 性能监控与分析

### 1. 性能监控工具
```javascript
// 性能监控类
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      fps: 0,
      memory: 0,
      renderTime: 0,
      networkRequests: []
    };
  }

  // 监控FPS
  startFPSMonitor() {
    let lastTime = Date.now();
    let frames = 0;

    const loop = () => {
      frames++;
      const currentTime = Date.now();

      if (currentTime - lastTime >= 1000) {
        this.metrics.fps = frames;
        frames = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(loop);
    };

    loop();
  }

  // 监控内存使用
  checkMemoryUsage() {
    const performance = wx.getPerformance();
    if (performance && performance.memory) {
      this.metrics.memory = performance.memory.usedJSHeapSize;
    }
  }

  // 生成性能报告
  generateReport() {
    return {
      timestamp: Date.now(),
      fps: this.metrics.fps,
      memory: this.metrics.memory,
      score: this.calculateScore()
    };
  }

  calculateScore() {
    let score = 100;

    // FPS评分
    if (this.metrics.fps < 30) score -= 30;
    else if (this.metrics.fps < 50) score -= 15;

    // 内存评分
    if (this.metrics.memory > 50 * 1024 * 1024) score -= 20;

    return Math.max(0, score);
  }
}
```

### 2. 性能预警系统
```javascript
// 性能预警
class PerformanceAlert {
  constructor(thresholds = {}) {
    this.thresholds = {
      minFPS: 30,
      maxMemory: 50 * 1024 * 1024,
      maxRenderTime: 16,
      ...thresholds
    };
  }

  checkPerformance(metrics) {
    const alerts = [];

    if (metrics.fps < this.thresholds.minFPS) {
      alerts.push({
        type: 'fps',
        message: `FPS过低: ${metrics.fps}`,
        suggestion: '考虑减少动画复杂度或降低渲染频率'
      });
    }

    if (metrics.memory > this.thresholds.maxMemory) {
      alerts.push({
        type: 'memory',
        message: `内存使用过高: ${(metrics.memory / 1024 / 1024).toFixed(2)}MB`,
        suggestion: '检查是否存在内存泄漏，清理不必要的对象'
      });
    }

    return alerts;
  }
}
```

## 小程序特性优化

### 1. 预加载策略
```javascript
// 页面预加载
Page({
  onLoad() {
    // 预加载下一个可能访问的页面
    wx.preloadPage({
      url: '/pages/learn/index'
    });
  },

  onShow() {
    // 预加载关键数据
    this.preloadCriticalData();
  },

  preloadCriticalData() {
    // 预加载用户常用的学习计划
    wx.request({
      url: '/api/plans/recent',
      success: (res) => {
        // 缓存到本地存储
        wx.setStorageSync('recentPlans', res.data);
      }
    });
  }
});
```

### 2. 启动优化
```javascript
// app.js 启动优化
App({
  onLaunch() {
    // 异步初始化非关键服务
    this.initializeServices();

    // 预热关键组件
    this.preloadCriticalComponents();
  },

  async initializeServices() {
    // 延迟初始化统计服务
    setTimeout(() => {
      this.initAnalytics();
    }, 1000);

    // 延迟初始化推送服务
    setTimeout(() => {
      this.initPushService();
    }, 2000);
  },

  preloadCriticalComponents() {
    // 预加载首页需要的组件
    const criticalComponents = [
      '/components/bubble-canvas/index',
      '/components/base/button/index'
    ];

    criticalComponents.forEach(component => {
      // 预加载组件代码
      require(component);
    });
  }
});
```

## 最佳实践总结

### 1. 开发阶段
- 使用性能监控工具实时检测性能问题
- 定期进行性能测试，建立性能基准
- 代码审查时关注性能影响

### 2. 测试阶段
- 在不同设备上测试性能表现
- 模拟弱网络环境测试
- 长时间使用测试内存泄漏

### 3. 生产阶段
- 监控关键性能指标
- 收集用户反馈的性能问题
- 定期优化和更新

## 性能优化检查清单

- [ ] Canvas动画帧率稳定在30fps以上
- [ ] 长列表使用虚拟化技术
- [ ] 图片使用懒加载和格式优化
- [ ] 组件按需加载，避免过度渲染
- [ ] 内存使用控制在合理范围内
- [ ] 代码分包合理，主包体积控制
- [ ] 关键路径优化，启动速度快
- [ ] 性能监控系统正常运行

---

**文档版本**: v2.0
**最后更新**: 2025年1月
**维护者**: 前端性能团队