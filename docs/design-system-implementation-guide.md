# NebulaLearn 设计系统实施指南

## 概述

本指南基于《AIBUBB视觉设计文档 V2.0》，为开发团队提供设计系统的具体实施方法和最佳实践。确保所有开发工作都能严格遵循统一的设计规范。

## 设计系统架构

### 设计Token层级
```
设计Token
├── 基础Token (Base Tokens)
│   ├── 颜色 (Colors)
│   ├── 字体 (Typography)
│   ├── 间距 (Spacing)
│   └── 圆角 (Border Radius)
├── 语义Token (Semantic Tokens)
│   ├── 主题色 (Theme Colors)
│   ├── 状态色 (Status Colors)
│   └── 功能色 (Functional Colors)
└── 组件Token (Component Tokens)
    ├── 按钮 (Button)
    ├── 卡片 (Card)
    └── 表单 (Form)
```

## 色彩系统实施

### 1. 色彩变量定义

在 `styles/variables.wxss` 中定义所有色彩变量：

```css
/* 基础色彩 */
:root {
  /* 主色调 */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-500: #3775f5;
  --primary-600: #2563eb;
  --primary-900: #1e3a8a;
  
  /* 中性色 */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-500: #737373;
  --neutral-900: #171717;
  
  /* 语义色彩 */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --danger-500: #ef4444;
}

/* 暗色模式 */
[data-theme="dark"] {
  --primary-50: #1e3a8a;
  --primary-100: #2563eb;
  --primary-500: #60a5fa;
  --primary-600: #93c5fd;
  --primary-900: #dbeafe;
  
  --neutral-50: #171717;
  --neutral-100: #262626;
  --neutral-500: #a3a3a3;
  --neutral-900: #fafafa;
}
```

### 2. 色彩使用规范

**主色调使用**：
- `--primary-500`: 主要按钮、链接、重要图标
- `--primary-100`: 主色调的浅色背景
- `--primary-900`: 主色调的深色文本

**中性色使用**：
- `--neutral-900`: 主要文本
- `--neutral-500`: 次要文本
- `--neutral-100`: 分割线、边框
- `--neutral-50`: 背景色

**状态色使用**：
- `--success-500`: 成功状态
- `--warning-500`: 警告状态
- `--danger-500`: 错误状态

### 3. 色彩对比度检查

确保所有文本与背景的对比度符合WCAG AA标准：

```javascript
// 色彩对比度检查工具
function checkContrast(foreground, background) {
  const ratio = calculateContrastRatio(foreground, background);
  return {
    ratio,
    passAA: ratio >= 4.5,
    passAAA: ratio >= 7
  };
}
```

## 字体排版实施

### 1. 字体层级定义

```css
:root {
  /* 字体大小 */
  --font-size-xs: 24rpx;    /* 12px */
  --font-size-sm: 28rpx;    /* 14px */
  --font-size-base: 32rpx;  /* 16px */
  --font-size-lg: 36rpx;    /* 18px */
  --font-size-xl: 40rpx;    /* 20px */
  --font-size-2xl: 48rpx;   /* 24px */
  --font-size-3xl: 60rpx;   /* 30px */
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}
```

### 2. 排版组件使用

```html
<!-- 页面标题 -->
<view class="text-3xl font-bold text-neutral-900">页面标题</view>

<!-- 章节标题 -->
<view class="text-xl font-semibold text-neutral-900">章节标题</view>

<!-- 正文内容 -->
<view class="text-base font-normal text-neutral-700 leading-normal">
  正文内容...
</view>

<!-- 说明文字 -->
<view class="text-sm font-normal text-neutral-500">说明文字</view>
```

### 3. 响应式排版

```css
/* 响应式字体大小 */
.responsive-title {
  font-size: var(--font-size-xl);
}

@media (max-width: 750rpx) {
  .responsive-title {
    font-size: var(--font-size-lg);
  }
}
```

## 间距系统实施

### 1. 间距变量定义

```css
:root {
  /* 基础间距单位 */
  --space-1: 8rpx;    /* 4px */
  --space-2: 16rpx;   /* 8px */
  --space-3: 24rpx;   /* 12px */
  --space-4: 32rpx;   /* 16px */
  --space-5: 40rpx;   /* 20px */
  --space-6: 48rpx;   /* 24px */
  --space-8: 64rpx;   /* 32px */
  --space-10: 80rpx;  /* 40px */
  --space-12: 96rpx;  /* 48px */
  --space-16: 128rpx; /* 64px */
}
```

### 2. 间距使用规范

**内边距 (Padding)**：
- 组件内部间距使用 `--space-4` (16px)
- 紧凑布局使用 `--space-2` (8px)
- 宽松布局使用 `--space-6` (24px)

**外边距 (Margin)**：
- 组件间距使用 `--space-4` (16px)
- 章节间距使用 `--space-8` (32px)
- 页面边距使用 `--space-6` (24px)

```css
/* 卡片组件间距示例 */
.card {
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.card-title {
  margin-bottom: var(--space-3);
}

.card-content {
  margin-bottom: var(--space-2);
}
```

## 组件选择决策树

### 按钮组件选择

```
需要按钮？
├── 主要操作 → nl-button type="primary"
├── 次要操作 → nl-button type="secondary"
├── 危险操作 → nl-button type="danger"
├── 文本链接 → nl-button type="text"
└── 图标按钮 → nl-icon + 点击事件
```

### 文本组件选择

```
需要显示文本？
├── 页面标题 → nl-text type="title"
├── 章节标题 → nl-text type="subtitle"
├── 正文内容 → nl-text type="body"
├── 说明文字 → nl-text type="caption"
└── 长文本 → nl-text ellipsis="true"
```

### 布局组件选择

```
需要布局容器？
├── 内容分组 → nl-card
├── 列表展示 → nl-virtual-list (大量数据)
├── 内容分割 → nl-divider
└── 标签分类 → nl-tag
```

## 主题切换实施

### 1. 主题管理器使用

```javascript
// utils/theme-manager.js
const ThemeManager = {
  // 设置主题
  setTheme(theme) {
    wx.setStorageSync('theme', theme);
    this.applyTheme(theme);
  },
  
  // 应用主题
  applyTheme(theme) {
    const app = getApp();
    app.globalData.theme = theme;
    
    // 更新页面主题
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.setData) {
        page.setData({ theme });
      }
    });
  },
  
  // 获取当前主题
  getCurrentTheme() {
    return wx.getStorageSync('theme') || 'light';
  }
};
```

### 2. 页面主题适配

```javascript
// 页面中使用主题
Page({
  data: {
    theme: 'light'
  },
  
  onLoad() {
    const theme = ThemeManager.getCurrentTheme();
    this.setData({ theme });
  },
  
  // 切换主题
  toggleTheme() {
    const newTheme = this.data.theme === 'light' ? 'dark' : 'light';
    ThemeManager.setTheme(newTheme);
    this.setData({ theme: newTheme });
  }
});
```

### 3. 组件主题适配

```html
<!-- 根据主题应用不同样式 -->
<view class="container" data-theme="{{theme}}">
  <nl-card title="标题" class="theme-card">
    <view class="content">内容</view>
  </nl-card>
</view>
```

```css
/* 主题样式 */
.theme-card {
  background-color: var(--neutral-50);
  color: var(--neutral-900);
}

[data-theme="dark"] .theme-card {
  background-color: var(--neutral-900);
  color: var(--neutral-50);
}
```

## 性能优化指南

### 1. CSS变量优化

```css
/* 避免深层嵌套的CSS变量 */
/* ❌ 不推荐 */
.component {
  --local-color: var(--global-color);
}
.component .child {
  color: var(--local-color);
}

/* ✅ 推荐 */
.component .child {
  color: var(--global-color);
}
```

### 2. 样式复用

```css
/* 创建通用样式类 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
```

### 3. 条件样式加载

```javascript
// 只在需要时加载特定主题样式
if (theme === 'dark') {
  wx.loadFontFace({
    family: 'custom-font-dark',
    source: 'url("https://example.com/font-dark.woff2")'
  });
}
```

## 质量检查清单

### 设计规范检查
- [ ] 使用了正确的色彩变量
- [ ] 字体大小符合层级规范
- [ ] 间距使用了标准变量
- [ ] 组件选择符合决策树

### 可访问性检查
- [ ] 色彩对比度符合WCAG AA标准
- [ ] 交互元素有适当的触摸区域
- [ ] 文本大小适合阅读
- [ ] 支持主题切换

### 性能检查
- [ ] 避免了不必要的样式重复
- [ ] 使用了高效的CSS选择器
- [ ] 图片使用了优化组件
- [ ] 长列表使用了虚拟列表

### 兼容性检查
- [ ] 在不同设备上测试正常
- [ ] 亮色/暗色模式都正常显示
- [ ] 不同屏幕尺寸适配良好
- [ ] 微信小程序版本兼容

## 常见问题解决

**Q: CSS变量在某些设备上不生效？**
A: 检查微信小程序版本，确保支持CSS变量。提供降级方案。

**Q: 主题切换后部分组件样式异常？**
A: 检查组件是否正确监听了主题变化事件，确保及时更新样式。

**Q: 设计稿与实现效果有差异？**
A: 对照设计Token检查，确保使用了正确的变量值。

## 工具和资源

- **设计Token检查器**: `utils/design-token-checker.js`
- **色彩对比度工具**: `utils/contrast-checker.js`
- **主题预览工具**: `pages/test/theme-test`
- **组件展示页面**: `pages/test/form-components`

通过遵循本指南，确保所有开发工作都能严格符合NebulaLearn设计系统的要求，提供一致、优质的用户体验。
