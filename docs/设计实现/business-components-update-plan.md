# 业务组件更新实施计划

## 概述

根据优先级工作计划，第三优先级任务是业务组件更新，当前完成度为75%。需要重点完成的组件包括：
1. 计划创建流程组件（学习计划相关）
2. 菜单列表组件（用户中心相关）

本文档提供了这两个组件的详细实施计划，包括组件设计、开发步骤和测试方案。

## 1. 计划创建流程组件

### 1.1 组件需求分析

**功能需求**：
- 提供学习计划创建的分步引导界面
- 支持主题选择、标签选择、计划周期设置等功能
- 支持预览和编辑功能
- 提供表单验证和错误提示
- 支持保存草稿和提交功能

**技术需求**：
- 符合NebulaLearn UI设计规范
- 支持亮色/暗色模式
- 良好的移动端适配
- 高性能渲染和交互

### 1.2 组件设计

**组件结构**：
```
components/
  business/
    plan-creator/
      index.js       - 组件逻辑
      index.wxml     - 组件模板
      index.wxss     - 组件样式
      index.json     - 组件配置
```

**组件接口设计**：
```javascript
/**
 * 计划创建流程组件
 * 属性：
 * - visible: 是否显示组件
 * - initialData: 初始数据（用于编辑模式）
 * - mode: 模式（'create'或'edit'）
 * 
 * 事件：
 * - submit: 提交事件，返回创建的计划数据
 * - cancel: 取消事件
 * - draft: 保存草稿事件，返回草稿数据
 */
```

**组件状态设计**：
```javascript
data: {
  // 步骤控制
  currentStep: 0, // 当前步骤
  steps: ['主题选择', '标签选择', '计划设置', '预览确认'],
  
  // 表单数据
  formData: {
    title: '',
    description: '',
    themeId: '',
    themeName: '',
    themeColor: '',
    tags: [],
    duration: 7, // 默认7天
    dailyContentCount: 3, // 默认每天3个内容
    isPublic: true
  },
  
  // UI状态
  isLoading: false,
  errors: {},
  availableThemes: [],
  availableTags: [],
  
  // 动画状态
  animationVisible: false
}
```

### 1.3 开发步骤

#### 步骤1：创建基础组件结构（1天）

1. 创建组件目录和文件
2. 实现基础UI框架
3. 实现步骤导航和切换逻辑

#### 步骤2：实现主题选择步骤（1天）

1. 实现主题列表展示
2. 实现主题选择和预览功能
3. 实现主题搜索功能

#### 步骤3：实现标签选择步骤（1天）

1. 实现标签列表展示
2. 实现标签选择、取消选择功能
3. 实现标签搜索和分类功能

#### 步骤4：实现计划设置步骤（1天）

1. 实现计划基本信息表单
2. 实现计划周期和内容数量设置
3. 实现表单验证

#### 步骤5：实现预览确认步骤（1天）

1. 实现计划预览界面
2. 实现编辑功能
3. 实现提交和保存草稿功能

#### 步骤6：优化和测试（1天）

1. 实现亮色/暗色模式适配
2. 优化移动端适配
3. 进行单元测试和集成测试

### 1.4 组件实现示例

**组件模板示例**：
```html
<!-- 计划创建流程组件 -->
<view class="plan-creator {{animationVisible ? 'visible' : ''}}" wx:if="{{visible}}">
  <!-- 顶部导航 -->
  <view class="creator-header">
    <view class="step-nav">
      <view 
        wx:for="{{steps}}" 
        wx:key="index"
        class="step-item {{currentStep >= index ? 'active' : ''}}"
        bindtap="handleStepClick"
        data-step="{{index}}"
      >
        <view class="step-number">{{index + 1}}</view>
        <view class="step-name">{{item}}</view>
      </view>
    </view>
    
    <view class="header-actions">
      <view class="close-btn" bindtap="handleCancel">×</view>
    </view>
  </view>
  
  <!-- 步骤内容 -->
  <view class="creator-content">
    <!-- 步骤1：主题选择 -->
    <view class="step-content" hidden="{{currentStep !== 0}}">
      <view class="step-title">选择学习计划主题</view>
      
      <view class="themes-grid">
        <view 
          wx:for="{{availableThemes}}" 
          wx:key="id"
          class="theme-item {{formData.themeId === item.id ? 'selected' : ''}}"
          style="background-color: {{item.color}};"
          bindtap="handleThemeSelect"
          data-theme="{{item}}"
        >
          <view class="theme-name">{{item.name}}</view>
          <view class="theme-check" wx:if="{{formData.themeId === item.id}}">✓</view>
        </view>
      </view>
    </view>
    
    <!-- 其他步骤内容... -->
  </view>
  
  <!-- 底部按钮 -->
  <view class="creator-footer">
    <button 
      class="btn-secondary" 
      bindtap="handlePrevStep" 
      hidden="{{currentStep === 0}}"
    >上一步</button>
    
    <button 
      class="btn-primary" 
      bindtap="{{currentStep < steps.length - 1 ? 'handleNextStep' : 'handleSubmit'}}"
      loading="{{isLoading}}"
    >
      {{currentStep < steps.length - 1 ? '下一步' : '创建计划'}}
    </button>
  </view>
</view>
```

**组件逻辑示例**：
```javascript
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        if (newVal) {
          this.setData({ animationVisible: true });
          this._loadInitialData();
        } else {
          this.setData({ animationVisible: false });
        }
      }
    },
    initialData: {
      type: Object,
      value: null
    },
    mode: {
      type: String,
      value: 'create' // 'create' 或 'edit'
    }
  },
  
  data: {
    // 步骤控制
    currentStep: 0,
    steps: ['主题选择', '标签选择', '计划设置', '预览确认'],
    
    // 表单数据
    formData: {
      title: '',
      description: '',
      themeId: '',
      themeName: '',
      themeColor: '',
      tags: [],
      duration: 7,
      dailyContentCount: 3,
      isPublic: true
    },
    
    // UI状态
    isLoading: false,
    errors: {},
    availableThemes: [],
    availableTags: [],
    
    // 动画状态
    animationVisible: false
  },
  
  methods: {
    // 加载初始数据
    _loadInitialData() {
      this.setData({ isLoading: true });
      
      // 加载主题数据
      this._loadThemes()
        .then(() => {
          // 如果是编辑模式，加载初始数据
          if (this.properties.mode === 'edit' && this.properties.initialData) {
            this._initFormData(this.properties.initialData);
          }
          
          this.setData({ isLoading: false });
        })
        .catch(err => {
          console.error('加载初始数据失败', err);
          this.setData({ isLoading: false });
        });
    },
    
    // 加载主题数据
    _loadThemes() {
      return new Promise((resolve, reject) => {
        // 模拟API请求
        setTimeout(() => {
          this.setData({
            availableThemes: [
              { id: 1, name: '人际沟通', color: '#3B82F6' },
              { id: 2, name: '职场技能', color: '#10B981' },
              { id: 3, name: '情绪管理', color: '#F59E0B' },
              { id: 4, name: '领导力', color: '#8B5CF6' }
            ]
          });
          resolve();
        }, 500);
      });
    },
    
    // 初始化表单数据（编辑模式）
    _initFormData(initialData) {
      this.setData({
        formData: { ...this.data.formData, ...initialData }
      });
    },
    
    // 处理主题选择
    handleThemeSelect(e) {
      const theme = e.currentTarget.dataset.theme;
      
      this.setData({
        'formData.themeId': theme.id,
        'formData.themeName': theme.name,
        'formData.themeColor': theme.color
      });
    },
    
    // 处理步骤点击
    handleStepClick(e) {
      const step = e.currentTarget.dataset.step;
      
      // 只允许点击已完成的步骤
      if (step < this.data.currentStep) {
        this.setData({ currentStep: step });
      }
    },
    
    // 处理下一步
    handleNextStep() {
      // 验证当前步骤
      if (!this._validateCurrentStep()) {
        return;
      }
      
      // 如果是标签选择步骤，需要加载标签数据
      if (this.data.currentStep === 0) {
        this._loadTags();
      }
      
      this.setData({
        currentStep: this.data.currentStep + 1
      });
    },
    
    // 处理上一步
    handlePrevStep() {
      this.setData({
        currentStep: this.data.currentStep - 1
      });
    },
    
    // 验证当前步骤
    _validateCurrentStep() {
      const { currentStep, formData } = this.data;
      const errors = {};
      
      switch (currentStep) {
        case 0: // 主题选择
          if (!formData.themeId) {
            errors.theme = '请选择一个主题';
          }
          break;
        case 1: // 标签选择
          if (!formData.tags || formData.tags.length === 0) {
            errors.tags = '请至少选择一个标签';
          }
          break;
        case 2: // 计划设置
          if (!formData.title) {
            errors.title = '请输入计划标题';
          }
          if (formData.duration < 1) {
            errors.duration = '计划周期必须大于0';
          }
          break;
      }
      
      this.setData({ errors });
      
      return Object.keys(errors).length === 0;
    },
    
    // 处理提交
    handleSubmit() {
      // 验证所有步骤
      if (!this._validateAllSteps()) {
        return;
      }
      
      this.setData({ isLoading: true });
      
      // 模拟API请求
      setTimeout(() => {
        this.setData({ isLoading: false });
        
        // 触发提交事件
        this.triggerEvent('submit', { plan: this.data.formData });
        
        // 重置表单
        this._resetForm();
      }, 1000);
    },
    
    // 验证所有步骤
    _validateAllSteps() {
      // 实现所有步骤的验证逻辑
      return true;
    },
    
    // 重置表单
    _resetForm() {
      this.setData({
        currentStep: 0,
        formData: {
          title: '',
          description: '',
          themeId: '',
          themeName: '',
          themeColor: '',
          tags: [],
          duration: 7,
          dailyContentCount: 3,
          isPublic: true
        },
        errors: {}
      });
    },
    
    // 处理取消
    handleCancel() {
      this.triggerEvent('cancel');
      this._resetForm();
    },
    
    // 处理保存草稿
    handleSaveDraft() {
      this.triggerEvent('draft', { plan: this.data.formData });
    }
  }
});
```

### 1.5 测试计划

**单元测试**：
- 测试组件初始化
- 测试步骤切换逻辑
- 测试表单验证逻辑
- 测试主题和标签选择逻辑

**集成测试**：
- 测试与API的交互
- 测试与其他组件的交互

**UI测试**：
- 测试亮色/暗色模式
- 测试不同屏幕尺寸的适配
- 测试动画和过渡效果

## 2. 菜单列表组件

### 2.1 组件需求分析

**功能需求**：
- 提供用户中心的菜单列表
- 支持图标、文本、徽章等元素
- 支持分组和分割线
- 支持点击事件和跳转
- 支持自定义样式和主题

**技术需求**：
- 符合NebulaLearn UI设计规范
- 支持亮色/暗色模式
- 良好的移动端适配
- 高性能渲染和交互

### 2.2 组件设计

**组件结构**：
```
components/
  business/
    menu-list/
      index.js       - 组件逻辑
      index.wxml     - 组件模板
      index.wxss     - 组件样式
      index.json     - 组件配置
```

**组件接口设计**：
```javascript
/**
 * 菜单列表组件
 * 属性：
 * - items: 菜单项数组
 * - theme: 主题（'light'或'dark'）
 * - customStyle: 自定义样式
 * 
 * 事件：
 * - itemclick: 菜单项点击事件，返回点击的菜单项
 */
```

**菜单项数据结构**：
```javascript
{
  id: 'unique-id',
  type: 'item', // 'item', 'group', 'divider'
  icon: 'icon-name', // 图标名称
  text: '菜单项文本',
  badge: {
    type: 'dot', // 'dot', 'number', 'text'
    value: 5, // 徽章值
    color: 'red' // 徽章颜色
  },
  arrow: true, // 是否显示箭头
  disabled: false, // 是否禁用
  url: '/pages/some-page/index', // 跳转链接
  children: [] // 子菜单项（用于分组）
}
```

### 2.3 开发步骤

#### 步骤1：创建基础组件结构（0.5天）

1. 创建组件目录和文件
2. 实现基础UI框架
3. 实现菜单项渲染逻辑

#### 步骤2：实现菜单项功能（0.5天）

1. 实现图标和文本渲染
2. 实现徽章渲染
3. 实现箭头和禁用状态

#### 步骤3：实现分组和分割线（0.5天）

1. 实现分组标题渲染
2. 实现分组内容渲染
3. 实现分割线渲染

#### 步骤4：实现交互功能（0.5天）

1. 实现点击事件处理
2. 实现页面跳转功能
3. 实现禁用状态处理

#### 步骤5：优化和测试（0.5天）

1. 实现亮色/暗色模式适配
2. 优化移动端适配
3. 进行单元测试和集成测试

### 2.4 组件实现示例

**组件模板示例**：
```html
<!-- 菜单列表组件 -->
<view class="menu-list menu-list-{{theme}}" style="{{customStyle}}">
  <block wx:for="{{items}}" wx:key="id">
    <!-- 菜单项 -->
    <view 
      wx:if="{{item.type === 'item' || !item.type}}"
      class="menu-item {{item.disabled ? 'disabled' : ''}}"
      bindtap="handleItemClick"
      data-item="{{item}}"
    >
      <!-- 图标 -->
      <view class="menu-item-icon" wx:if="{{item.icon}}">
        <nl-icon name="{{item.icon}}" size="medium"></nl-icon>
      </view>
      
      <!-- 文本 -->
      <view class="menu-item-text">{{item.text}}</view>
      
      <!-- 徽章 -->
      <view class="menu-item-badge" wx:if="{{item.badge}}">
        <nl-badge 
          type="{{item.badge.type}}" 
          value="{{item.badge.value}}"
          color="{{item.badge.color}}"
        ></nl-badge>
      </view>
      
      <!-- 箭头 -->
      <view class="menu-item-arrow" wx:if="{{item.arrow !== false}}">
        <nl-icon name="arrow-right" size="small"></nl-icon>
      </view>
    </view>
    
    <!-- 分组 -->
    <view wx:elif="{{item.type === 'group'}}">
      <!-- 分组标题 -->
      <view class="menu-group-title" wx:if="{{item.text}}">
        {{item.text}}
      </view>
      
      <!-- 分组内容 -->
      <view class="menu-group-content">
        <block wx:for="{{item.children}}" wx:key="id" wx:for-item="subItem">
          <view 
            class="menu-item {{subItem.disabled ? 'disabled' : ''}}"
            bindtap="handleItemClick"
            data-item="{{subItem}}"
          >
            <!-- 图标 -->
            <view class="menu-item-icon" wx:if="{{subItem.icon}}">
              <nl-icon name="{{subItem.icon}}" size="medium"></nl-icon>
            </view>
            
            <!-- 文本 -->
            <view class="menu-item-text">{{subItem.text}}</view>
            
            <!-- 徽章 -->
            <view class="menu-item-badge" wx:if="{{subItem.badge}}">
              <nl-badge 
                type="{{subItem.badge.type}}" 
                value="{{subItem.badge.value}}"
                color="{{subItem.badge.color}}"
              ></nl-badge>
            </view>
            
            <!-- 箭头 -->
            <view class="menu-item-arrow" wx:if="{{subItem.arrow !== false}}">
              <nl-icon name="arrow-right" size="small"></nl-icon>
            </view>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 分割线 -->
    <view wx:elif="{{item.type === 'divider'}}" class="menu-divider"></view>
  </block>
</view>
```

**组件逻辑示例**：
```javascript
Component({
  properties: {
    items: {
      type: Array,
      value: []
    },
    theme: {
      type: String,
      value: 'light' // 'light' 或 'dark'
    },
    customStyle: {
      type: String,
      value: ''
    }
  },
  
  methods: {
    /**
     * 处理菜单项点击
     */
    handleItemClick(e) {
      const item = e.currentTarget.dataset.item;
      
      // 如果菜单项禁用，不处理点击
      if (item.disabled) {
        return;
      }
      
      // 触发点击事件
      this.triggerEvent('itemclick', { item });
      
      // 如果有URL，跳转到对应页面
      if (item.url) {
        wx.navigateTo({
          url: item.url,
          fail: err => {
            console.error('页面跳转失败', err);
          }
        });
      }
    }
  }
});
```

### 2.5 测试计划

**单元测试**：
- 测试组件初始化
- 测试菜单项渲染
- 测试点击事件处理

**集成测试**：
- 测试与页面跳转的交互
- 测试与其他组件的交互

**UI测试**：
- 测试亮色/暗色模式
- 测试不同屏幕尺寸的适配
- 测试各种菜单项组合的显示效果

## 3. 实施时间表

| 任务 | 开始日期 | 结束日期 | 负责人 |
|------|----------|----------|--------|
| 计划创建流程组件 - 基础结构 | 2025-06-10 | 2025-06-10 | 开发团队A |
| 计划创建流程组件 - 主题选择 | 2025-06-11 | 2025-06-11 | 开发团队A |
| 计划创建流程组件 - 标签选择 | 2025-06-12 | 2025-06-12 | 开发团队A |
| 计划创建流程组件 - 计划设置 | 2025-06-13 | 2025-06-13 | 开发团队A |
| 计划创建流程组件 - 预览确认 | 2025-06-14 | 2025-06-14 | 开发团队A |
| 计划创建流程组件 - 优化测试 | 2025-06-15 | 2025-06-15 | 开发团队A |
| 菜单列表组件 - 全部任务 | 2025-06-10 | 2025-06-12 | 开发团队B |
| 集成测试与文档 | 2025-06-13 | 2025-06-13 | 开发团队A/B |

## 4. 风险与应对策略

| 风险 | 影响 | 可能性 | 应对策略 |
|------|------|--------|----------|
| API接口变更 | 高 | 中 | 与后端团队保持沟通，使用适配器模式隔离API变化 |
| 性能问题 | 中 | 低 | 实施性能监控，优化渲染和交互逻辑 |
| 兼容性问题 | 中 | 低 | 在多种设备上进行测试，实现优雅降级 |
| 进度延误 | 高 | 低 | 合理规划时间，设置缓冲期，必要时调整范围 |

## 5. 验收标准

1. **功能完整性**：
   - 组件实现所有需求功能
   - 组件接口符合设计规范
   - 组件交互流畅自然

2. **代码质量**：
   - 代码结构清晰，注释完整
   - 无明显bug和性能问题
   - 通过所有单元测试和集成测试

3. **UI/UX质量**：
   - 符合NebulaLearn UI设计规范
   - 支持亮色/暗色模式
   - 在不同设备上显示正常

4. **文档完整性**：
   - 提供详细的使用文档
   - 提供示例代码
   - 提供测试报告
