#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始清理Docker资源...${NC}"

# 停止所有运行中的容器
echo -e "${YELLOW}停止所有容器...${NC}"
docker stop $(docker ps -a -q) 2>/dev/null || true

# 删除所有容器
echo -e "${YELLOW}删除所有容器...${NC}"
docker rm $(docker ps -a -q) 2>/dev/null || true

# 删除所有标签为none的镜像
echo -e "${YELLOW}删除未标记的镜像...${NC}"
docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true

# 删除特定的镜像
echo -e "${YELLOW}删除nebulalearn相关镜像...${NC}"
docker rmi $(docker images | grep nebulalearn | awk '{print $3}') 2>/dev/null || true

# 清理系统
echo -e "${YELLOW}执行系统清理...${NC}"
docker system prune -a -f

# 使用正确的项目名称启动容器
echo -e "${YELLOW}使用aibubb项目名称重新启动容器...${NC}"
docker-compose -p aibubb up -d

echo -e "${GREEN}清理完成!${NC}"
echo -e "${GREEN}检查运行中的容器...${NC}"
docker ps 