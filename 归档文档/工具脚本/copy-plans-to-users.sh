#!/bin/bash

# 为所有已注册但没有学习计划的用户复制系统默认学习计划
# 此脚本在Docker环境中执行

echo "===== 开始为现有用户复制默认学习计划 ====="

# 确保后端容器正在运行
if ! docker ps | grep -q aibubb-backend; then
  echo "错误: 后端容器不在运行状态，请先启动容器"
  echo "可以使用命令: docker-compose up -d"
  exit 1
fi

# 创建主脚本文件
MAIN_SCRIPT="/tmp/copy_plans_script.js"
cat > ${MAIN_SCRIPT} << 'EOF'
/**
 * 为所有已注册但没有学习计划的用户复制系统默认学习计划
 * 内嵌版本，避免依赖外部模块
 */

// 获取数据库模型
const { User, LearningPlan, Tag, PlanTag, DailyContent, sequelize } = require('./models');
const logger = require('./config/logger');

// 复制系统默认计划到用户
async function copySystemDefaultPlanToUser(userId) {
  // 创建事务
  const t = await sequelize.transaction();
  
  try {
    console.log(`复制系统默认计划给用户 ${userId} - 开始`);
    
    // 查找系统默认学习计划
    const defaultPlan = await LearningPlan.findOne({
      where: { is_system_default: true },
      transaction: t
    });

    if (!defaultPlan) {
      console.log('未找到系统默认学习计划');
      await t.rollback();
      throw new Error('系统默认学习计划不存在');
    }
    
    console.log(`找到系统默认计划，ID: ${defaultPlan.id}, 标题: ${defaultPlan.title}`);

    // 复制学习计划基本信息
    const userPlan = await LearningPlan.create({
      user_id: userId,
      theme_id: defaultPlan.theme_id,
      title: defaultPlan.title,
      description: defaultPlan.description,
      target_days: defaultPlan.target_days || 7,
      status: 'not_started',
      progress: 0,
      completed_days: 0,
      start_date: new Date(),
      end_date: new Date(Date.now() + (defaultPlan.target_days || 7) * 24 * 60 * 60 * 1000),
      is_current: true, // 设为当前计划
      is_system_default: false // 用户计划不是系统默认
    }, { transaction: t });
    
    console.log(`创建用户计划成功，ID: ${userPlan.id}`);

    // 获取默认计划的标签
    const defaultTags = await Tag.findAll({
      where: { plan_id: defaultPlan.id },
      transaction: t
    });
    
    console.log(`找到 ${defaultTags.length} 个标签需要复制`);

    // 复制标签（创建新标签并建立关联）
    if (defaultTags && defaultTags.length > 0) {
      // 创建新标签
      const createdTags = [];
      for (const tag of defaultTags) {
        const newTag = await Tag.create({
          name: tag.name,
          plan_id: userPlan.id,
          category_id: tag.category_id,
          relevance_score: tag.relevance_score || 1.0,
          weight: tag.weight || 1.0,
          is_verified: tag.is_verified || false,
          sort_order: tag.sort_order || 0
        }, { transaction: t });
        createdTags.push(newTag);
      }

      // 创建标签关联
      for (let i = 0; i < createdTags.length; i++) {
        await PlanTag.create({
          plan_id: userPlan.id,
          tag_id: createdTags[i].id,
          relevance_score: defaultTags[i].relevance_score || 1.0,
          weight: defaultTags[i].weight || 1.0,
          is_primary: i < 3, // 前三个标签设为主要标签
          sort_order: defaultTags[i].sort_order || i
        }, { transaction: t });
      }
      
      console.log(`复制 ${defaultTags.length} 个标签并创建关联成功`);
    }

    // 获取默认计划的每日内容
    const dailyContents = await DailyContent.findAll({
      where: { plan_id: defaultPlan.id },
      transaction: t
    });
    
    console.log(`找到 ${dailyContents ? dailyContents.length : 0} 个每日内容需要复制`);

    // 复制每日内容
    if (dailyContents && dailyContents.length > 0) {
      for (const content of dailyContents) {
        await DailyContent.create({
          plan_id: userPlan.id,
          day_number: content.day_number || 1,
          title: content.title || '每日学习',
          content: content.content || '内容待添加',
          is_completed: false
        }, { transaction: t });
      }
      console.log(`复制 ${dailyContents.length} 个每日内容成功`);
    }

    // 提交事务
    await t.commit();
    console.log(`复制系统默认计划给用户 ${userId} - 成功`);
    
    return userPlan;
  } catch (error) {
    // 回滚事务
    if (t && !t.finished) {
      await t.rollback();
    }
    console.error(`复制系统默认计划给用户 ${userId} 失败: ${error.message}`);
    throw error;
  }
}

// 主函数
async function copyDefaultPlanToExistingUsers() {
  console.log('开始为现有用户复制默认学习计划...');
  
  try {
    logger.info('开始为现有用户复制默认学习计划');
    
    // 用于记录处理结果
    const results = {
      totalUsers: 0,
      usersWithoutPlans: 0,
      successCount: 0,
      failCount: 0,
      failedUsers: []
    };

    // 获取所有用户
    const users = await User.findAll();
    results.totalUsers = users.length;
    console.log(`找到 ${users.length} 个用户`);
    logger.info(`找到 ${users.length} 个用户`);

    // 遍历所有用户，检查是否有学习计划
    for (const user of users) {
      const userId = user.id;
      
      try {
        // 检查用户是否已有学习计划
        const existingPlans = await LearningPlan.findAll({
          where: { user_id: userId }
        });

        if (existingPlans.length === 0) {
          // 用户没有学习计划，复制默认计划
          results.usersWithoutPlans++;
          console.log(`用户 ${userId} 没有学习计划，正在复制默认计划...`);
          logger.info(`用户 ${userId} 没有学习计划，正在复制默认计划`);

          try {
            // 复制系统默认计划给用户
            await copySystemDefaultPlanToUser(userId);
            console.log(`成功为用户 ${userId} 复制默认学习计划`);
            logger.info(`成功为用户 ${userId} 复制默认学习计划`);
            results.successCount++;
          } catch (error) {
            console.error(`为用户 ${userId} 复制默认计划失败: ${error.message}`);
            logger.error(`为用户 ${userId} 复制默认计划失败: ${error.message}`);
            results.failCount++;
            results.failedUsers.push(userId);
          }
        } else {
          console.log(`用户 ${userId} 已有 ${existingPlans.length} 个学习计划，跳过`);
          logger.info(`用户 ${userId} 已有 ${existingPlans.length} 个学习计划，跳过`);
        }
      } catch (error) {
        console.error(`处理用户 ${userId} 时出错: ${error.message}`);
        logger.error(`处理用户 ${userId} 时出错: ${error.message}`);
        results.failCount++;
        results.failedUsers.push(userId);
      }
    }

    // 打印处理结果
    console.log('\n处理结果汇总:');
    console.log(`总用户数: ${results.totalUsers}`);
    console.log(`没有学习计划的用户数: ${results.usersWithoutPlans}`);
    console.log(`成功复制默认计划数: ${results.successCount}`);
    console.log(`失败数: ${results.failCount}`);
    
    if (results.failCount > 0) {
      console.log('失败的用户:', results.failedUsers);
    }

    logger.info(`处理完成。总用户: ${results.totalUsers}, 无计划用户: ${results.usersWithoutPlans}, 成功: ${results.successCount}, 失败: ${results.failCount}`);
    
    console.log('\n处理完成!');
  } catch (error) {
    console.error(`批量处理失败: ${error.message}`);
    logger.error(`批量处理失败: ${error.message}`);
  }
}

// 执行主函数
copyDefaultPlanToExistingUsers()
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
EOF

# 将主脚本复制到容器内
docker cp ${MAIN_SCRIPT} aibubb-backend:/usr/src/app/temp_script.js

# 在容器内执行脚本
docker exec -it aibubb-backend node /usr/src/app/temp_script.js

# 删除容器内的临时脚本
docker exec -it aibubb-backend rm /usr/src/app/temp_script.js

# 删除本地临时脚本
rm ${MAIN_SCRIPT}

echo ""
echo "===== 脚本执行完成 =====" 