/**
 * API变更检测脚本
 *
 * 该脚本用于检测API变更，包括：
 * 1. 比较OpenAPI规范文件，检测API变更
 * 2. 分析Git提交记录，检测API相关文件的变更
 * 3. 通过静态分析代码，检测API相关代码的变更
 *
 * 使用方法：
 * node scripts/detect-api-changes.js [options]
 *
 * 选项：
 * --spec-only: 只检测OpenAPI规范文件的变更
 * --git-only: 只检测Git提交记录的变更
 * --code-only: 只检测代码静态分析的变更
 * --output <file>: 将变更报告输出到指定文件
 * --format <json|markdown|html>: 指定输出格式
 *
 * 示例：
 * node scripts/detect-api-changes.js --output changes.json --format json
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { program } = require('commander');
const openapiDiff = require('openapi-diff');
const chalk = require('chalk');

// 定义命令行选项
program
  .option('--spec-only', '只检测OpenAPI规范文件的变更')
  .option('--git-only', '只检测Git提交记录的变更')
  .option('--code-only', '只检测代码静态分析的变更')
  .option('--output <file>', '将变更报告输出到指定文件')
  .option('--format <format>', '指定输出格式（json、markdown、html）', 'json')
  .parse(process.argv);

const options = program.opts();

// 定义变更类型
const CHANGE_TYPES = {
  BREAKING: 'BREAKING',
  NON_BREAKING: 'NON_BREAKING',
  INFO: 'INFO'
};

// 定义变更级别
const CHANGE_LEVELS = {
  [CHANGE_TYPES.BREAKING]: 'urgent',
  [CHANGE_TYPES.NON_BREAKING]: 'warning',
  [CHANGE_TYPES.INFO]: 'info'
};

// 定义API相关文件路径模式
const API_FILE_PATTERNS = [
  'controllers/**/*.js',
  'routes/**/*.js',
  'swagger/**/*.js',
  'docs/api-spec.json'
];

// 定义OpenAPI规范文件路径
const OPENAPI_SPEC_PATH = 'docs/api-spec.json';

// 定义变更报告文件路径
const CHANGES_REPORT_PATH = 'docs/api-changes.json';

/**
 * 检测OpenAPI规范文件的变更
 * @returns {Promise<Array>} 变更列表
 */
async function detectSpecChanges() {
  console.log(chalk.blue('检测OpenAPI规范文件的变更...'));

  try {
    // 获取当前的OpenAPI规范文件
    const currentSpec = JSON.parse(fs.readFileSync(OPENAPI_SPEC_PATH, 'utf8'));

    // 获取上一个版本的OpenAPI规范文件
    let previousSpec;
    try {
      // 尝试从Git中获取上一个版本的规范文件
      const previousSpecContent = execSync(`git show HEAD~1:${OPENAPI_SPEC_PATH}`, { encoding: 'utf8' });
      previousSpec = JSON.parse(previousSpecContent);
    } catch (error) {
      console.log(chalk.yellow('无法从Git获取上一个版本的规范文件，使用备份文件'));

      // 尝试使用备份文件
      const backupPath = `${OPENAPI_SPEC_PATH}.bak`;
      if (fs.existsSync(backupPath)) {
        previousSpec = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
      } else {
        console.log(chalk.yellow('没有找到备份文件，无法比较变更'));
        return [];
      }
    }

    // 使用openapi-diff比较规范文件
    const diffResult = await openapiDiff.diffSpecs(previousSpec, currentSpec);

    // 解析变更结果
    const changes = [];

    // 处理破坏性变更
    if (diffResult.breakingDifferences && diffResult.breakingDifferences.length > 0) {
      for (const diff of diffResult.breakingDifferences) {
        changes.push({
          type: CHANGE_TYPES.BREAKING,
          level: CHANGE_LEVELS[CHANGE_TYPES.BREAKING],
          path: diff.path,
          message: diff.message,
          oldValue: diff.oldValue,
          newValue: diff.newValue
        });
      }
    }

    // 处理非破坏性变更
    if (diffResult.nonBreakingDifferences && diffResult.nonBreakingDifferences.length > 0) {
      for (const diff of diffResult.nonBreakingDifferences) {
        changes.push({
          type: CHANGE_TYPES.NON_BREAKING,
          level: CHANGE_LEVELS[CHANGE_TYPES.NON_BREAKING],
          path: diff.path,
          message: diff.message,
          oldValue: diff.oldValue,
          newValue: diff.newValue
        });
      }
    }

    // 处理信息性变更
    if (diffResult.unclassifiedDifferences && diffResult.unclassifiedDifferences.length > 0) {
      for (const diff of diffResult.unclassifiedDifferences) {
        changes.push({
          type: CHANGE_TYPES.INFO,
          level: CHANGE_LEVELS[CHANGE_TYPES.INFO],
          path: diff.path,
          message: diff.message,
          oldValue: diff.oldValue,
          newValue: diff.newValue
        });
      }
    }

    // 备份当前规范文件
    fs.copyFileSync(OPENAPI_SPEC_PATH, `${OPENAPI_SPEC_PATH}.bak`);

    console.log(chalk.green(`检测到 ${changes.length} 个规范变更`));
    return changes;
  } catch (error) {
    console.error(chalk.red('检测OpenAPI规范文件变更时出错:'), error);
    return [];
  }
}

/**
 * 检测Git提交记录中API相关文件的变更
 * @returns {Array} 变更列表
 */
function detectGitChanges() {
  console.log(chalk.blue('检测Git提交记录中API相关文件的变更...'));

  try {
    // 获取最近一次提交中修改的文件
    const changedFiles = execSync('git diff --name-only HEAD~1 HEAD', { encoding: 'utf8' })
      .split('\n')
      .filter(file => file.trim() !== '');

    // 过滤出API相关文件
    const apiChangedFiles = changedFiles.filter(file => API_FILE_PATTERNS.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
      return regex.test(file);
    }));

    // 获取文件变更详情
    const changes = [];
    for (const file of apiChangedFiles) {
      // 获取文件变更内容
      const diffContent = execSync(`git diff HEAD~1 HEAD -- "${file}"`, { encoding: 'utf8' });

      // 解析变更内容
      const addedLines = diffContent.split('\n').filter(line => line.startsWith('+')).length;
      const removedLines = diffContent.split('\n').filter(line => line.startsWith('-')).length;

      // 判断变更类型
      let type = CHANGE_TYPES.INFO;
      if (file.includes('controllers/') || file.includes('routes/')) {
        // 控制器和路由文件的变更可能是破坏性的
        if (removedLines > addedLines) {
          type = CHANGE_TYPES.BREAKING;
        } else if (removedLines > 0) {
          type = CHANGE_TYPES.NON_BREAKING;
        }
      }

      changes.push({
        type,
        level: CHANGE_LEVELS[type],
        path: file,
        message: `文件变更: 添加 ${addedLines} 行, 删除 ${removedLines} 行`,
        addedLines,
        removedLines,
        diffContent
      });
    }

    console.log(chalk.green(`检测到 ${changes.length} 个Git变更`));
    return changes;
  } catch (error) {
    console.error(chalk.red('检测Git提交记录变更时出错:'), error);
    return [];
  }
}

/**
 * 生成变更报告
 * @param {Array} changes 变更列表
 * @param {string} format 输出格式
 * @returns {string} 变更报告
 */
function generateReport(changes, format = 'json') {
  // 按变更级别分组
  const groupedChanges = {
    urgent: changes.filter(change => change.level === 'urgent'),
    warning: changes.filter(change => change.level === 'warning'),
    info: changes.filter(change => change.level === 'info')
  };

  // 生成报告
  const report = {
    generatedAt: new Date().toISOString(),
    summary: {
      total: changes.length,
      urgent: groupedChanges.urgent.length,
      warning: groupedChanges.warning.length,
      info: groupedChanges.info.length
    },
    changes: groupedChanges
  };

  // 根据格式输出
  switch (format) {
    case 'json':
      return JSON.stringify(report, null, 2);
    case 'markdown':
      return generateMarkdownReport(report);
    case 'html':
      return generateHtmlReport(report);
    default:
      return JSON.stringify(report, null, 2);
  }
}

/**
 * 生成Markdown格式的变更报告
 * @param {Object} report 变更报告对象
 * @returns {string} Markdown格式的变更报告
 */
function generateMarkdownReport(report) {
  let markdown = '# API变更报告\n\n';
  markdown += `生成时间: ${new Date(report.generatedAt).toLocaleString()}\n\n`;

  markdown += '## 变更摘要\n\n';
  markdown += `- 总变更数: ${report.summary.total}\n`;
  markdown += `- 紧急变更: ${report.summary.urgent}\n`;
  markdown += `- 警告变更: ${report.summary.warning}\n`;
  markdown += `- 信息变更: ${report.summary.info}\n\n`;

  if (report.summary.urgent > 0) {
    markdown += '## 紧急变更\n\n';
    for (const change of report.changes.urgent) {
      markdown += `### ${change.path}\n\n`;
      markdown += `- 类型: ${change.type}\n`;
      markdown += `- 消息: ${change.message}\n`;
      if (change.oldValue && change.newValue) {
        markdown += `- 旧值: \`${JSON.stringify(change.oldValue)}\`\n`;
        markdown += `- 新值: \`${JSON.stringify(change.newValue)}\`\n`;
      }
      markdown += '\n';
    }
  }

  if (report.summary.warning > 0) {
    markdown += '## 警告变更\n\n';
    for (const change of report.changes.warning) {
      markdown += `### ${change.path}\n\n`;
      markdown += `- 类型: ${change.type}\n`;
      markdown += `- 消息: ${change.message}\n`;
      if (change.oldValue && change.newValue) {
        markdown += `- 旧值: \`${JSON.stringify(change.oldValue)}\`\n`;
        markdown += `- 新值: \`${JSON.stringify(change.newValue)}\`\n`;
      }
      markdown += '\n';
    }
  }

  if (report.summary.info > 0) {
    markdown += '## 信息变更\n\n';
    for (const change of report.changes.info) {
      markdown += `### ${change.path}\n\n`;
      markdown += `- 类型: ${change.type}\n`;
      markdown += `- 消息: ${change.message}\n`;
      if (change.oldValue && change.newValue) {
        markdown += `- 旧值: \`${JSON.stringify(change.oldValue)}\`\n`;
        markdown += `- 新值: \`${JSON.stringify(change.newValue)}\`\n`;
      }
      markdown += '\n';
    }
  }

  return markdown;
}

/**
 * 生成HTML格式的变更报告
 * @param {Object} report 变更报告对象
 * @returns {string} HTML格式的变更报告
 */
function generateHtmlReport(report) {
  // 简单实现，实际项目中可以使用模板引擎
  let html = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>API变更报告</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #333; }
    h2 { color: #666; margin-top: 20px; }
    h3 { color: #999; }
    .urgent { color: #d9534f; }
    .warning { color: #f0ad4e; }
    .info { color: #5bc0de; }
    .summary { margin: 20px 0; }
    .change { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
  </style>
</head>
<body>
  <h1>API变更报告</h1>
  <p>生成时间: ${new Date(report.generatedAt).toLocaleString()}</p>
  
  <div class="summary">
    <h2>变更摘要</h2>
    <p>总变更数: ${report.summary.total}</p>
    <p>紧急变更: <span class="urgent">${report.summary.urgent}</span></p>
    <p>警告变更: <span class="warning">${report.summary.warning}</span></p>
    <p>信息变更: <span class="info">${report.summary.info}</span></p>
  </div>`;

  if (report.summary.urgent > 0) {
    html += '<h2 class="urgent">紧急变更</h2>';
    for (const change of report.changes.urgent) {
      html += `<div class="change urgent">
        <h3>${change.path}</h3>
        <p>类型: ${change.type}</p>
        <p>消息: ${change.message}</p>`;
      if (change.oldValue && change.newValue) {
        html += `<p>旧值: <code>${JSON.stringify(change.oldValue)}</code></p>
        <p>新值: <code>${JSON.stringify(change.newValue)}</code></p>`;
      }
      html += '</div>';
    }
  }

  if (report.summary.warning > 0) {
    html += '<h2 class="warning">警告变更</h2>';
    for (const change of report.changes.warning) {
      html += `<div class="change warning">
        <h3>${change.path}</h3>
        <p>类型: ${change.type}</p>
        <p>消息: ${change.message}</p>`;
      if (change.oldValue && change.newValue) {
        html += `<p>旧值: <code>${JSON.stringify(change.oldValue)}</code></p>
        <p>新值: <code>${JSON.stringify(change.newValue)}</code></p>`;
      }
      html += '</div>';
    }
  }

  if (report.summary.info > 0) {
    html += '<h2 class="info">信息变更</h2>';
    for (const change of report.changes.info) {
      html += `<div class="change info">
        <h3>${change.path}</h3>
        <p>类型: ${change.type}</p>
        <p>消息: ${change.message}</p>`;
      if (change.oldValue && change.newValue) {
        html += `<p>旧值: <code>${JSON.stringify(change.oldValue)}</code></p>
        <p>新值: <code>${JSON.stringify(change.newValue)}</code></p>`;
      }
      html += '</div>';
    }
  }

  html += '</body></html>';
  return html;
}

/**
 * 主函数
 */
async function main() {
  console.log(chalk.green('开始检测API变更...'));

  let allChanges = [];

  // 检测OpenAPI规范文件的变更
  if (!options.gitOnly && !options.codeOnly) {
    const specChanges = await detectSpecChanges();
    allChanges = allChanges.concat(specChanges);
  }

  // 检测Git提交记录的变更
  if (!options.specOnly && !options.codeOnly) {
    const gitChanges = detectGitChanges();
    allChanges = allChanges.concat(gitChanges);
  }

  // 检测代码静态分析的变更
  if (!options.specOnly && !options.gitOnly) {
    // 代码静态分析的变更检测暂未实现
    console.log(chalk.yellow('代码静态分析的变更检测暂未实现'));
  }

  // 生成变更报告
  if (allChanges.length > 0) {
    const report = generateReport(allChanges, options.format);

    // 输出变更报告
    if (options.output) {
      fs.writeFileSync(options.output, report);
      console.log(chalk.green(`变更报告已保存到 ${options.output}`));
    } else {
      fs.writeFileSync(CHANGES_REPORT_PATH, report);
      console.log(chalk.green(`变更报告已保存到 ${CHANGES_REPORT_PATH}`));
    }

    // 输出变更摘要
    const urgentCount = allChanges.filter(change => change.level === 'urgent').length;
    const warningCount = allChanges.filter(change => change.level === 'warning').length;
    const infoCount = allChanges.filter(change => change.level === 'info').length;

    console.log(chalk.green('\n变更摘要:'));
    console.log(chalk.green(`- 总变更数: ${allChanges.length}`));
    console.log(chalk.red(`- 紧急变更: ${urgentCount}`));
    console.log(chalk.yellow(`- 警告变更: ${warningCount}`));
    console.log(chalk.blue(`- 信息变更: ${infoCount}`));

    // 如果有紧急变更，返回非零状态码
    if (urgentCount > 0) {
      process.exit(1);
    }
  } else {
    console.log(chalk.green('未检测到API变更'));
  }
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red('执行过程中出错:'), error);
  process.exit(1);
});
