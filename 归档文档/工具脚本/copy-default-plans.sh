#!/bin/bash

# 为所有已注册但没有学习计划的用户复制系统默认学习计划
# 支持本地和Docker环境执行

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查参数
MODE="docker"
if [ "$1" == "local" ]; then
  MODE="local"
fi

echo -e "${GREEN}===== 开始为现有用户复制默认学习计划 =====${NC}"
echo "运行模式: $MODE"

# Docker模式运行
if [ "$MODE" == "docker" ]; then
  echo -e "${YELLOW}使用Docker模式运行脚本...${NC}"
  
  # 确保后端容器正在运行
  if ! docker ps | grep -q aibubb-backend; then
    echo -e "${RED}错误: 后端容器不在运行状态，请先启动容器${NC}"
    echo "可以使用命令: docker-compose up -d"
    exit 1
  fi
  
  # 在Docker容器中运行脚本
  docker exec -it aibubb-backend node /usr/src/app/scripts/copy-default-plan-to-existing-users.js
  
  echo -e "${GREEN}脚本执行完成!${NC}"
else
  # 本地模式运行
  echo -e "${YELLOW}使用本地模式运行脚本...${NC}"
  
  # 切换到后端目录
  cd "$(dirname "$0")/../backend" || exit
  
  # 执行脚本
  node scripts/copy-default-plan-to-existing-users.js
  
  echo -e "${GREEN}脚本执行完成!${NC}"
fi

exit 0 