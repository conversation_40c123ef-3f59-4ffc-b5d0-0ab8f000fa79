# AIBUBB项目环境配置管理指南

## 概述

本文档介绍了AIBUBB项目前端的环境配置管理方案，该方案基于微信小程序的环境版本（`envVersion`）自动选择对应的配置，解决了之前在`app.js`中硬编码`apiBaseUrl`和`isTestMode`的问题。

## 环境配置文件

环境配置集中在`utils/env-config.js`文件中管理，该文件提供了以下功能：

1. 根据微信小程序环境版本（`develop`、`trial`、`release`）自动选择对应的配置
2. 提供统一的配置获取接口`getEnvConfig()`
3. 支持扩展更多环境相关的配置项

## 配置项说明

当前环境配置包含以下核心项：

| 配置项 | 说明 | 示例值 |
|-------|------|-------|
| `apiBaseUrl` | API服务器基础URL | `http://localhost:3010/mock-api/v1` |
| `isTestMode` | 是否为测试模式 | `true` / `false` |
| `logLevel` | 日志级别 | `debug` / `info` / `error` |
| `envVersion` | 当前环境版本 | `develop` / `trial` / `release` |
| `isProduction` | 是否为生产环境 | `true` / `false` |

## 环境版本说明

微信小程序提供了三种环境版本：

1. **开发版（develop）**：在开发者工具中预览时的环境
2. **体验版（trial）**：上传为体验版时的环境
3. **正式版（release）**：发布到线上的正式环境

我们的配置方案会根据这三种环境自动选择对应的配置。

## 使用方法

### 在应用启动时初始化

在`app.js`的`onLaunch`方法中，我们调用`initEnvConfig()`方法初始化环境配置：

```javascript
onLaunch() {
  // 初始化环境配置
  this.initEnvConfig();
  
  // 其他初始化...
}
```

初始化方法会将环境配置应用到`globalData`中：

```javascript
initEnvConfig() {
  // 获取当前环境配置
  const envConfig = getEnvConfig();
  
  // 将环境配置应用到全局数据
  this.globalData.apiBaseUrl = envConfig.apiBaseUrl;
  this.globalData.isTestMode = envConfig.isTestMode;
  this.globalData.envVersion = envConfig.envVersion;
}
```

### 在其他模块中使用

其他模块可以通过导入`env-config.js`来获取环境配置：

```javascript
const { getEnvConfig } = require('./utils/env-config');

// 获取当前环境配置
const envConfig = getEnvConfig();

// 使用配置
console.log(`当前API地址: ${envConfig.apiBaseUrl}`);
console.log(`是否测试模式: ${envConfig.isTestMode}`);
```

## 修改配置

如需修改环境配置，请编辑`utils/env-config.js`文件中的`ENV_CONFIG`对象：

```javascript
// 环境配置映射
const ENV_CONFIG = {
  // 开发环境 (工具内预览)
  develop: {
    apiBaseUrl: 'http://localhost:3010/mock-api/v1',
    isTestMode: true,
    logLevel: 'debug'
  },
  
  // 体验版环境
  trial: {
    apiBaseUrl: 'https://api-test.aibubb.com/api/v1',
    isTestMode: true,
    logLevel: 'info'
  },
  
  // 正式环境
  release: {
    apiBaseUrl: 'https://api.aibubb.com/api/v1',
    isTestMode: false,
    logLevel: 'error'
  }
};
```

## 注意事项

1. **不要在`app.js`中硬编码环境配置**：所有环境相关的配置都应该在`utils/env-config.js`中管理
2. **添加新配置项**：如需添加新的环境相关配置项，请在`ENV_CONFIG`中为所有环境都添加该项
3. **日志输出**：环境配置初始化时会输出当前环境信息，便于调试
4. **本地调试**：在开发者工具中可以通过"编译模式"下拉菜单切换环境进行测试

## 扩展

未来可以考虑扩展以下功能：

1. 支持更多环境配置项（如缓存策略、功能开关等）
2. 添加环境配置的运行时验证
3. 实现配置的热更新机制

---

文档更新日期：{{current_date}}
