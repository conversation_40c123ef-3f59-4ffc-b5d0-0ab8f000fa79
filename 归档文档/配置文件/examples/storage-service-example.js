/**
 * 本地存储服务使用示例
 */

// 导入存储服务
import { storage } from '../utils/storage';
import { StorageService } from '../utils/storage-service';

// 创建主题设置专用存储实例
const themeStorage = new StorageService({
  prefix: 'theme',
  defaultExpiry: 90 * 24 * 60 * 60 * 1000, // 90天
  enableLogging: true
});

// 创建用户数据专用存储实例
const userStorage = new StorageService({
  prefix: 'user',
  defaultExpiry: 7 * 24 * 60 * 60 * 1000, // 7天
  enableLogging: true
});

// 创建API缓存专用存储实例
const apiCacheStorage = new StorageService({
  prefix: 'api_cache',
  defaultExpiry: 10 * 60 * 1000, // 10分钟
  enableLogging: true
});

/**
 * 主题设置相关函数
 */

// 保存主题模式
export function saveThemeMode(mode) {
  return themeStorage.set('mode', mode);
}

// 获取主题模式
export function getThemeMode() {
  return themeStorage.get('mode', 'light');
}

// 保存界面样式
export function saveInterfaceStyle(style) {
  return themeStorage.set('interfaceStyle', style);
}

// 获取界面样式
export function getInterfaceStyle() {
  return themeStorage.get('interfaceStyle', 'bubble');
}

/**
 * 用户数据相关函数
 */

// 保存用户信息
export function saveUserInfo(userInfo) {
  return userStorage.set('info', userInfo);
}

// 获取用户信息
export function getUserInfo() {
  return userStorage.get('info', null);
}

// 保存用户设置
export function saveUserSettings(settings) {
  return userStorage.set('settings', settings);
}

// 获取用户设置
export function getUserSettings() {
  return userStorage.get('settings', {
    allowDataCollection: true,
    showNotifications: true
  });
}

// 清除用户数据
export function clearUserData() {
  return userStorage.clear();
}

/**
 * API缓存相关函数
 */

// 缓存API响应
export function cacheApiResponse(endpoint, params, data, ttl = null) {
  const key = `${endpoint}_${JSON.stringify(params)}`;
  return apiCacheStorage.set(key, data, { expiry: ttl });
}

// 获取缓存的API响应
export function getCachedApiResponse(endpoint, params) {
  const key = `${endpoint}_${JSON.stringify(params)}`;
  return apiCacheStorage.get(key);
}

// 清除API缓存
export function clearApiCache() {
  return apiCacheStorage.clear();
}

/**
 * 通用存储函数
 */

// 保存临时数据
export function saveTempData(key, data, expiryMs = 60 * 60 * 1000) {
  return storage.set(`temp_${key}`, data, { expiry: expiryMs });
}

// 获取临时数据
export function getTempData(key, defaultValue = null) {
  return storage.get(`temp_${key}`, defaultValue);
}

// 清除所有临时数据
export function clearAllTempData() {
  // 获取所有键
  const info = storage.getInfo();
  if (!info || !info.keys) return false;

  // 筛选出临时数据键
  const tempKeys = info.keys.filter(key =>
    key.startsWith('app_temp_')
  );

  // 删除所有临时数据
  tempKeys.forEach(key => {
    // 移除前缀
    const shortKey = key.replace('app_', '');
    storage.remove(shortKey);
  });

  return true;
}

/**
 * 使用示例
 */
function usageExample() {
  // 保存主题设置
  saveThemeMode('dark');
  saveInterfaceStyle('star');

  // 保存用户信息
  saveUserInfo({
    id: '12345',
    nickname: '测试用户',
    avatarUrl: 'https://example.com/avatar.png'
  });

  // 保存用户设置
  saveUserSettings({
    allowDataCollection: false,
    showNotifications: true
  });

  // 缓存API响应
  cacheApiResponse('/themes', { page: 1 }, {
    data: [{ id: 1, name: '主题1' }, { id: 2, name: '主题2' }],
    total: 10
  });

  // 获取缓存的API响应
  const cachedThemes = getCachedApiResponse('/themes', { page: 1 });
  console.log('缓存的主题数据:', cachedThemes);

  // 获取用户设置
  const settings = getUserSettings();
  console.log('用户设置:', settings);

  // 清除API缓存
  clearApiCache();

  // 保存临时数据
  saveTempData('lastSearch', '编程', 30 * 60 * 1000); // 30分钟

  // 获取临时数据
  const lastSearch = getTempData('lastSearch', '');
  console.log('上次搜索:', lastSearch);
}
