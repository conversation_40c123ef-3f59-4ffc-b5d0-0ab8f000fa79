# Cursor MCP 配置指南

## 什么是 MCP？

MCP（Model Control Protocol）是 Cursor 提供的一种机制，允许您扩展 IDE 功能，连接外部服务和工具，如数据库、第三方 API 或自定义命令行工具。

## 配置文件位置

MCP 配置可以在两个位置定义：
- 全局配置：`~/.cursor/mcp.json`（适用于所有项目）
- 项目配置：`.cursor/mcp.json`（仅适用于当前项目）

## 配置结构

MCP 配置必须遵循以下规则：

1. **顶层键名**：所有 MCP 服务必须定义在顶层的 `"mcpServers"` 对象内
2. **服务名称**：每个服务作为 `mcpServers` 对象的一个键值对存在

```json
{
  "mcpServers": {
    "service_name": {
      // 服务配置
    }
  }
}
```

## 服务类型与配置格式

### 1. URL 类型服务

适用于 HTTP 服务端点：

```json
{
  "mcpServers": {
    "my_api_service": {
      "url": "https://example.com/api/service"
    }
  }
}
```

### 2. 命令行（CLI）类型服务

适用于本地运行的命令行程序：

```json
{
  "mcpServers": {
    "my_cli_service": {
      "command": "执行命令",
      "args": ["参数1", "参数2", "..."],
      "env": {
        "环境变量1": "值1",
        "环境变量2": "值2"
      }
    }
  }
}
```

**注意**：
- 使用 `"args"` 字段定义命令行参数（不是 `"arguments"`）
- 使用 `"env"` 字段定义环境变量（不是 `"environment"`）

## 常见配置示例

### NPX 工具配置

```json
{
  "mcpServers": {
    "my_npx_tool": {
      "command": "npx",
      "args": ["-y", "工具包名称"]
    }
  }
}
```

### Node.js 脚本配置

```json
{
  "mcpServers": {
    "my_node_script": {
      "command": "node",
      "args": ["/path/to/script.js"],
      "env": {
        "NODE_ENV": "production",
        "API_KEY": "your-api-key"
      }
    }
  }
}
```

### 数据库服务配置

```json
{
  "mcpServers": {
    "mysql_service": {
      "command": "node",
      "args": ["/path/to/mysql-mcp-server.js"],
      "env": {
        "DATABASE_URL": "mysql://username:password@hostname:port/database"
      }
    }
  }
}
```

## 常见错误与解决方案

1. **服务未显示**
   - 检查服务是否配置在 `"mcpServers"` 对象内（不是 `"servers"` 或其他键名）
   - 确认使用了正确的字段名：`args`（而非 `arguments`）和 `env`（而非 `environment`）

2. **连接失败**
   - 对于 URL 类型，确保 URL 可访问且格式正确
   - 对于 CLI 类型，确保指定的命令和文件路径存在

3. **环境变量问题**
   - 确保环境变量在 `"env"` 对象内定义
   - 检查环境变量值是否正确（密码、路径等）

## 完整配置示例

```json
{
  "mcpServers": {
    "figma_service": {
      "url": "https://mcp.example.com/figma/service"
    },
    "npm_package": {
      "command": "npx",
      "args": ["-y", "@org/package-name"]
    },
    "database_service": {
      "command": "node",
      "args": ["/absolute/path/to/db-server.js"],
      "env": {
        "DATABASE_URL": "mysql://user:pass@host:3306/dbname",
        "API_KEY": "your-secret-key"
      }
    }
  }
}
```

## 常见配置错误示例

以下是一个错误的配置示例：

```json
{
  "mcpServers": {
    // 正确配置...
  },
  "servers": [  // ❌ 错误：Cursor 不识别 "servers" 数组
    {
      "name": "my_service",  // ❌ 错误：使用了不必要的 "name" 字段
      "type": "command",     // ❌ 错误：使用了不必要的 "type" 字段
      "command": "node",
      "arguments": [         // ❌ 错误：应使用 "args" 而非 "arguments"
        "/path/to/script.js"
      ],
      "environment": {       // ❌ 错误：应使用 "env" 而非 "environment"
        "API_KEY": "value"
      }
    }
  ]
}
```

正确的配置应该是：

```json
{
  "mcpServers": {
    "my_service": {          // ✅ 正确：直接使用服务名作为键
      "command": "node",
      "args": [              // ✅ 正确：使用 "args"
        "/path/to/script.js"
      ],
      "env": {               // ✅ 正确：使用 "env"
        "API_KEY": "value"
      }
    }
  }
}
```

## 修改配置后的操作

1. 保存配置文件
2. 重启 Cursor 以应用更改
3. 验证服务是否出现在 MCP 设置页面或侧边栏中

按照此指南配置 MCP 服务，您应该能够顺利地将外部工具与 Cursor 集成，并利用它们的功能来增强您的开发体验。 