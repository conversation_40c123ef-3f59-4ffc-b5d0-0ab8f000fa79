# API版本差异文档

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-09 |
| 最后更新 | 2025-05-09 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档详细分析了AIBUBB项目中V1和V2版本API的差异，帮助开发人员理解两个版本之间的主要变化，并为API迁移提供参考。AIBUBB项目计划彻底升级到V2版本，本文档将帮助开发人员了解升级过程中需要注意的关键差异。

## 2. 架构差异

### 2.1 实现方式差异

| 特性 | V1版本 | V2版本 |
|------|--------|--------|
| 控制器实现 | 直接实现，无依赖注入 | 使用依赖注入，分离业务逻辑和数据访问 |
| 架构风格 | 混合架构，部分模块使用分层 | 统一的分层架构（控制器-服务-仓库） |
| 错误处理 | 基础错误处理，不统一 | 增强的错误处理，统一格式 |
| 参数验证 | 基础验证，部分控制器内实现 | 增强的验证，使用中间件和验证器 |
| 文档化 | 部分API有Swagger注释 | 所有API都有完整的Swagger注释 |

### 2.2 功能差异

| 功能 | V1版本 | V2版本 |
|------|--------|--------|
| 软删除 | ❌ 不支持 | ✅ 支持，包括软删除和恢复功能 |
| 批量操作 | ❌ 不支持 | ✅ 支持，包括批量软删除、恢复和永久删除 |
| 已删除资源查询 | ❌ 不支持 | ✅ 支持，可以查询已软删除的资源 |
| 定期清理机制 | ❌ 不支持 | ✅ 支持，可以定期清理已软删除的资源 |
| 级联软删除 | ❌ 不支持 | ✅ 支持，删除父资源时级联软删除子资源 |

### 2.3 响应格式差异

| 特性 | V1版本 | V2版本 |
|------|--------|--------|
| 成功响应 | `{ "success": true, "data": {...} }` | `{ "success": true, "data": {...} }` |
| 错误响应 | 不统一，部分返回`{ "success": false, "message": "..." }` | 统一格式：`{ "success": false, "error": { "code": "...", "message": "...", "details": {...} } }` |
| 软删除信息 | 不适用 | 包含`deletedAt`字段，表示软删除时间 |
| 分页信息 | 部分API返回`{ "total", "page", "pageSize" }` | 所有列表API统一返回`{ "total", "page", "pageSize", "totalPages" }` |

## 3. API端点差异

下表列出了主要模块中V1和V2版本API的差异：

### 3.1 认证模块

| 端点 | V1版本 | V2版本 | 差异说明 |
|------|--------|--------|----------|
| `POST /api/v{version}/auth/login` | ✅ 支持 | ✅ 支持 | V2版本增加了刷新令牌功能 |
| `GET /api/v{version}/auth/user` | ✅ 支持 | ✅ 支持 | V2版本返回更详细的用户信息 |
| `POST /api/v{version}/auth/refresh-token` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持刷新访问令牌 |
| `POST /api/v{version}/auth/logout` | ✅ 支持 | ✅ 支持 | V2版本支持撤销刷新令牌 |

### 3.2 标签模块

| 端点 | V1版本 | V2版本 | 差异说明 |
|------|--------|--------|----------|
| `GET /api/v{version}/tags` | ✅ 支持 | ✅ 支持 | V2版本支持更多过滤和排序选项 |
| `GET /api/v{version}/tags/:id` | ✅ 支持 | ✅ 支持 | V2版本返回更详细的标签信息 |
| `POST /api/v{version}/tags` | ✅ 支持 | ✅ 支持 | 功能相同 |
| `PUT /api/v{version}/tags/:id` | ✅ 支持 | ✅ 支持 | 功能相同 |
| `DELETE /api/v{version}/tags/:id` | ✅ 支持 | ✅ 支持 | V1版本永久删除，V2版本也支持永久删除 |
| `DELETE /api/v{version}/tags/:id/soft-delete` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持软删除 |
| `PUT /api/v{version}/tags/:id/restore` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持恢复已删除的标签 |
| `GET /api/v{version}/tags/deleted` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持查询已删除的标签 |
| `POST /api/v{version}/batch/tags/soft-delete` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持批量软删除 |
| `POST /api/v{version}/batch/tags/restore` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持批量恢复 |

### 3.3 学习计划模块

| 端点 | V1版本 | V2版本 | 差异说明 |
|------|--------|--------|----------|
| `GET /api/v{version}/learning-plans` | ✅ 支持 | ✅ 支持 | V2版本支持更多过滤和排序选项 |
| `GET /api/v{version}/learning-plans/:id` | ✅ 支持 | ✅ 支持 | V2版本返回更详细的学习计划信息 |
| `POST /api/v{version}/learning-plans` | ✅ 支持 | ✅ 支持 | 功能相同 |
| `PUT /api/v{version}/learning-plans/:id` | ✅ 支持 | ✅ 支持 | 功能相同 |
| `DELETE /api/v{version}/learning-plans/:id` | ✅ 支持 | ✅ 支持 | V1版本永久删除，V2版本也支持永久删除 |
| `DELETE /api/v{version}/learning-plans/:id/soft-delete` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持软删除 |
| `PUT /api/v{version}/learning-plans/:id/restore` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持恢复已删除的学习计划 |
| `GET /api/v{version}/learning-plans/deleted` | ❌ 不支持 | ✅ 支持 | V2版本新增，支持查询已删除的学习计划 |
| `PUT /api/v{version}/learning-plans/:id/activate` | ✅ 支持 | ✅ 支持 | 功能相同 |

## 4. 请求和响应示例

### 4.1 软删除功能示例

#### V2版本软删除标签

请求：
```http
DELETE /api/v2/tags/123/soft-delete
Authorization: Bearer {token}
```

响应：
```json
{
  "success": true,
  "message": "标签已软删除",
  "data": {
    "id": 123,
    "deletedAt": "2025-05-09T10:00:00Z"
  }
}
```

#### V2版本恢复已删除标签

请求：
```http
PUT /api/v2/tags/123/restore
Authorization: Bearer {token}
```

响应：
```json
{
  "success": true,
  "message": "标签已恢复",
  "data": {
    "id": 123,
    "deletedAt": null
  }
}
```

### 4.2 批量操作示例

#### V2版本批量软删除标签

请求：
```http
POST /api/v2/batch/tags/soft-delete
Authorization: Bearer {token}
Content-Type: application/json

{
  "ids": [123, 124, 125]
}
```

响应：
```json
{
  "success": true,
  "message": "标签已批量软删除",
  "data": {
    "successCount": 3,
    "failedCount": 0,
    "results": [
      { "id": 123, "success": true },
      { "id": 124, "success": true },
      { "id": 125, "success": true }
    ]
  }
}
```

## 5. 迁移指南

### 5.1 从V1迁移到V2的步骤

1. **更新API端点**：将API调用从`/api/v1/`更新为`/api/v2/`
2. **适配响应格式**：处理V2版本中可能存在的`deletedAt`字段
3. **利用新功能**：使用软删除、批量操作等V2版本特有功能
4. **更新错误处理**：适配V2版本统一的错误响应格式
5. **更新分页处理**：处理V2版本统一的分页信息格式

### 5.2 迁移注意事项

1. **数据一致性**：确保迁移过程中数据一致性，特别是已删除资源的处理
2. **权限检查**：V2版本可能有更严格的权限检查，确保客户端有正确的权限
3. **测试覆盖**：全面测试迁移后的功能，确保所有功能正常工作
4. **监控和日志**：加强监控和日志记录，及时发现和解决迁移问题
5. **回滚计划**：准备回滚计划，以应对迁移过程中可能出现的问题

## 6. 结论

V2版本API相比V1版本有显著改进，包括架构优化、功能增强和响应格式统一。通过彻底升级到V2版本，AIBUBB项目将获得更好的可维护性、更丰富的功能和更一致的用户体验。本文档提供了详细的版本差异分析和迁移指南，帮助开发人员顺利完成从V1到V2版本的迁移。
