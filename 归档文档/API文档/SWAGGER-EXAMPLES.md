# Swagger 注释示例

本文档提供了在AIBUBB项目中使用Swagger注释的示例，以便开发人员可以参考这些示例为API端点添加文档。

## 控制器方法注释示例

### 获取列表API

```javascript
/**
 * @swagger
 * /api/v1/tags/{tagId}/notes:
 *   get:
 *     summary: 获取标签下的笔记列表
 *     description: 获取指定标签下的所有笔记，支持分页和状态过滤
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tagId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 标签ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, published, archived]
 *         description: 笔记状态过滤
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 成功获取笔记列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     notes:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Note'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 获取详情API

```javascript
/**
 * @swagger
 * /api/v1/notes/{id}:
 *   get:
 *     summary: 获取笔记详情
 *     description: 根据ID获取笔记的详细信息
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 笔记ID
 *     responses:
 *       200:
 *         description: 成功获取笔记详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/NoteDetail'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 创建资源API

```javascript
/**
 * @swagger
 * /api/v1/notes:
 *   post:
 *     summary: 创建笔记
 *     description: 创建一个新的笔记
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tagId
 *               - title
 *               - content
 *             properties:
 *               tagId:
 *                 type: integer
 *                 description: 标签ID
 *               title:
 *                 type: string
 *                 maxLength: 100
 *                 description: 笔记标题
 *               content:
 *                 type: string
 *                 description: 笔记内容
 *               imageUrl:
 *                 type: string
 *                 format: uri
 *                 description: 配图URL
 *               isPublic:
 *                 type: boolean
 *                 default: false
 *                 description: 是否公开
 *     responses:
 *       201:
 *         description: 笔记创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Note'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 更新资源API

```javascript
/**
 * @swagger
 * /api/v1/notes/{id}:
 *   put:
 *     summary: 更新笔记
 *     description: 更新指定ID的笔记
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 笔记ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 maxLength: 100
 *                 description: 笔记标题
 *               content:
 *                 type: string
 *                 description: 笔记内容
 *               imageUrl:
 *                 type: string
 *                 format: uri
 *                 description: 配图URL
 *               tagId:
 *                 type: integer
 *                 description: 标签ID
 *               isPublic:
 *                 type: boolean
 *                 description: 是否公开
 *     responses:
 *       200:
 *         description: 笔记更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 笔记已更新
 *                 data:
 *                   $ref: '#/components/schemas/Note'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 删除资源API

```javascript
/**
 * @swagger
 * /api/v1/notes/{id}:
 *   delete:
 *     summary: 删除笔记
 *     description: 删除指定ID的笔记
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 笔记ID
 *     responses:
 *       200:
 *         description: 笔记删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 笔记已删除
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

## 模型定义示例

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     Note:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 笔记ID
 *         title:
 *           type: string
 *           description: 笔记标题
 *         content:
 *           type: string
 *           description: 笔记内容
 *         imageUrl:
 *           type: string
 *           format: uri
 *           description: 配图URL
 *         status:
 *           type: string
 *           enum: [draft, published, archived]
 *           description: 笔记状态
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         tag:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *               description: 标签ID
 *             name:
 *               type: string
 *               description: 标签名称
 *
 *     NoteDetail:
 *       allOf:
 *         - $ref: '#/components/schemas/Note'
 *         - type: object
 *           properties:
 *             updatedAt:
 *               type: string
 *               format: date-time
 *               description: 更新时间
 *             likesCount:
 *               type: integer
 *               description: 点赞数
 *             commentsCount:
 *               type: integer
 *               description: 评论数
 *             isOwner:
 *               type: boolean
 *               description: 是否为笔记所有者
 *             user:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   description: 用户ID
 *                 nickname:
 *                   type: string
 *                   description: 用户昵称
 *                 avatarUrl:
 *                   type: string
 *                   format: uri
 *                   description: 用户头像URL
 *
 *     Pagination:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: 总记录数
 *         page:
 *           type: integer
 *           description: 当前页码
 *         pageSize:
 *           type: integer
 *           description: 每页记录数
 *         totalPages:
 *           type: integer
 *           description: 总页数
 */
```

## 通用响应定义示例

```javascript
/**
 * @swagger
 * components:
 *   responses:
 *     BadRequest:
 *       description: 请求参数错误
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               success:
 *                 type: boolean
 *                 example: false
 *               error:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                     example: BAD_REQUEST
 *                   message:
 *                     type: string
 *                     example: 请求参数错误
 *                   details:
 *                     type: object
 *                     example: { "title": "标题不能为空" }
 *
 *     Unauthorized:
 *       description: 未授权访问
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               success:
 *                 type: boolean
 *                 example: false
 *               error:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                     example: UNAUTHORIZED
 *                   message:
 *                     type: string
 *                     example: 未授权访问
 *
 *     NotFound:
 *       description: 资源不存在
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               success:
 *                 type: boolean
 *                 example: false
 *               error:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                     example: NOT_FOUND
 *                   message:
 *                     type: string
 *                     example: 资源不存在
 *
 *     ServerError:
 *       description: 服务器内部错误
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               success:
 *                 type: boolean
 *                 example: false
 *               error:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                     example: SERVER_ERROR
 *                   message:
 *                     type: string
 *                     example: 服务器内部错误
 */
```

## 安全定义示例

```javascript
/**
 * @swagger
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */
```

## 标签分组示例

```javascript
/**
 * @swagger
 * tags:
 *   - name: Auth
 *     description: 认证相关API
 *   - name: LearningPlans
 *     description: 学习计划相关API
 *   - name: Tags
 *     description: 标签相关API
 *   - name: Notes
 *     description: 笔记相关API
 *   - name: Exercises
 *     description: 练习相关API
 *   - name: Insights
 *     description: 观点相关API
 *   - name: Square
 *     description: 广场相关API
 *   - name: Statistics
 *     description: 统计相关API
 */
```

## 完整路由文件示例

```javascript
const express = require('express');
const { body, query } = require('express-validator');
const noteController = require('../controllers/noteV2.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @swagger
 * /api/v1/tags/{tagId}/notes:
 *   get:
 *     summary: 获取标签下的笔记列表
 *     description: 获取指定标签下的所有笔记，支持分页和状态过滤
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tagId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 标签ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, published, archived]
 *         description: 笔记状态过滤
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 成功获取笔记列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     notes:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Note'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/tags/:tagId/notes',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  noteController.getNotesByTagId
);

/**
 * @swagger
 * /api/v1/notes/user:
 *   get:
 *     summary: 获取当前用户的笔记列表
 *     description: 获取当前登录用户的所有笔记，支持分页
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 成功获取笔记列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     notes:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Note'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/notes/user',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  noteController.getUserNotes
);

// 其他路由定义...

module.exports = router;
```

## 使用说明

1. 在控制器文件中，为每个方法添加Swagger注释，描述API端点的详细信息。
2. 在模型文件中，添加模型定义的Swagger注释。
3. 在主应用文件中，添加通用响应、安全定义和标签分组的Swagger注释。
4. 确保所有API端点都有完整的文档，包括URL、HTTP方法、请求参数、响应格式和状态码。
5. 使用`$ref`引用通用组件，避免重复定义。
6. 为每个API端点指定适当的标签，以便在Swagger UI中进行分组。
7. 为每个API端点指定安全要求，明确哪些API需要认证。

通过遵循这些示例和指南，可以确保AIBUBB项目的API文档完整、一致且易于理解。
