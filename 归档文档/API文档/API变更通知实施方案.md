# API变更通知实施方案

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-10 |
| 最后更新 | 2025-05-10 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档详细描述了AIBUBB项目API变更通知的实施方案，包括通知机制设计、变更检测实现、通知发送方式和集成流程。通过实施本方案，我们将确保API变更及时通知到相关方，减少因API变更导致的问题，提高开发效率和系统稳定性。

## 2. 通知机制设计

### 2.1 通知渠道

经过对多种通知渠道的研究和比较，我们选择了以下通知渠道组合：

1. **邮件通知**：通过邮件发送API变更通知，适合正式通知和详细说明
2. **Slack通知**：通过Slack发送API变更通知，适合即时通知和讨论
3. **系统内通知**：通过系统内通知中心发送API变更通知，适合开发人员在系统内查看

### 2.2 通知内容

API变更通知的内容包括以下方面：

1. **变更摘要**：简要说明变更内容，如"添加了新的端点"、"修改了参数格式"等
2. **变更详情**：详细说明变更内容，包括变更前后的对比
3. **变更影响**：说明变更可能产生的影响，如"需要更新客户端代码"等
4. **变更时间**：说明变更的时间，包括计划时间和实际时间
5. **变更原因**：说明变更的原因，如"修复安全漏洞"、"提高性能"等
6. **变更负责人**：说明变更的负责人，方便联系和咨询
7. **变更文档**：提供变更相关的文档链接，如API文档、变更说明等

### 2.3 通知级别

根据变更的影响范围和紧急程度，我们定义了以下通知级别：

1. **信息级**：影响较小的变更，如添加新的可选参数、增加新的响应字段等
2. **警告级**：有一定影响的变更，如修改参数格式、修改响应格式等
3. **紧急级**：影响较大的变更，如删除端点、修改端点路径、修改必填参数等

### 2.4 通知频率

为避免通知过多导致疲劳，我们定义了以下通知频率：

1. **实时通知**：紧急级变更立即发送通知
2. **每日摘要**：警告级变更每日汇总发送通知
3. **每周摘要**：信息级变更每周汇总发送通知

## 3. 变更检测实现

### 3.1 检测方法

我们将使用以下方法检测API变更：

1. **OpenAPI规范比较**：比较新旧OpenAPI规范文件，检测API变更
2. **Git提交分析**：分析Git提交记录，检测API相关文件的变更
3. **代码静态分析**：通过静态分析代码，检测API相关代码的变更

### 3.2 检测工具

我们将使用以下工具实现API变更检测：

1. **openapi-diff**：用于比较OpenAPI规范文件，检测API变更
2. **git-diff**：用于分析Git提交记录，检测文件变更
3. **eslint**：用于静态分析JavaScript代码，检测API相关代码变更

### 3.3 检测流程

API变更检测的流程如下：

1. **触发检测**：在代码提交、合并请求或定时任务触发时执行检测
2. **获取规范**：获取新旧OpenAPI规范文件
3. **比较规范**：使用openapi-diff比较新旧规范，生成变更报告
4. **分析变更**：分析变更报告，确定变更类型和影响
5. **生成通知**：根据变更类型和影响生成通知内容
6. **发送通知**：根据通知级别和频率发送通知

## 4. 通知发送实现

### 4.1 邮件通知

邮件通知的实现方案如下：

```javascript
// 邮件通知服务
const nodemailer = require('nodemailer');

class EmailNotificationService {
  constructor(config) {
    this.transporter = nodemailer.createTransport(config.smtp);
    this.from = config.from;
    this.recipients = config.recipients;
  }

  async sendNotification(notification) {
    const { subject, content, level } = notification;
    
    // 根据级别设置邮件优先级
    const priority = level === 'urgent' ? 'high' : 'normal';
    
    // 发送邮件
    await this.transporter.sendMail({
      from: this.from,
      to: this.recipients.join(','),
      subject: `[API变更-${level.toUpperCase()}] ${subject}`,
      html: content,
      priority
    });
  }
}

module.exports = EmailNotificationService;
```

### 4.2 Slack通知

Slack通知的实现方案如下：

```javascript
// Slack通知服务
const { WebClient } = require('@slack/web-api');

class SlackNotificationService {
  constructor(config) {
    this.client = new WebClient(config.token);
    this.channel = config.channel;
  }

  async sendNotification(notification) {
    const { subject, content, level } = notification;
    
    // 根据级别设置消息颜色
    const color = level === 'urgent' ? '#FF0000' : level === 'warning' ? '#FFA500' : '#00FF00';
    
    // 发送Slack消息
    await this.client.chat.postMessage({
      channel: this.channel,
      text: `[API变更-${level.toUpperCase()}] ${subject}`,
      attachments: [
        {
          color,
          text: content,
          footer: 'API变更通知系统'
        }
      ]
    });
  }
}

module.exports = SlackNotificationService;
```

### 4.3 系统内通知

系统内通知的实现方案如下：

```javascript
// 系统内通知服务
const NotificationModel = require('../models/notification.model');

class SystemNotificationService {
  constructor(config) {
    this.config = config;
  }

  async sendNotification(notification) {
    const { subject, content, level, recipients } = notification;
    
    // 创建系统内通知
    await NotificationModel.create({
      title: `[API变更-${level.toUpperCase()}] ${subject}`,
      content,
      level,
      recipients,
      type: 'api-change',
      createdAt: new Date()
    });
  }
}

module.exports = SystemNotificationService;
```

### 4.4 通知管理器

通知管理器用于统一管理各种通知服务，实现方案如下：

```javascript
// 通知管理器
class NotificationManager {
  constructor(config) {
    this.services = {};
    this.config = config;
  }

  registerService(name, service) {
    this.services[name] = service;
  }

  async sendNotification(notification) {
    const { level } = notification;
    
    // 根据级别和配置决定使用哪些通知服务
    const serviceNames = this.config.levelServices[level] || [];
    
    // 发送通知
    for (const name of serviceNames) {
      if (this.services[name]) {
        await this.services[name].sendNotification(notification);
      }
    }
  }
}

module.exports = NotificationManager;
```

## 5. 集成流程

### 5.1 CI/CD集成

将API变更检测和通知集成到CI/CD流程中，实现方案如下：

```yaml
# .github/workflows/api-change-notification.yml
name: API Change Notification

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'controllers/**/*.js'
      - 'routes/**/*.js'
      - 'swagger/**/*.js'
      - 'docs/api-spec.json'

jobs:
  detect-and-notify:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 2
    - name: Use Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '14.x'
    - name: Install dependencies
      run: npm ci
    - name: Detect API changes
      run: node scripts/detect-api-changes.js
    - name: Send notifications
      if: ${{ success() }}
      run: node scripts/send-api-notifications.js
      env:
        SMTP_HOST: ${{ secrets.SMTP_HOST }}
        SMTP_PORT: ${{ secrets.SMTP_PORT }}
        SMTP_USER: ${{ secrets.SMTP_USER }}
        SMTP_PASS: ${{ secrets.SMTP_PASS }}
        SLACK_TOKEN: ${{ secrets.SLACK_TOKEN }}
        SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
```

### 5.2 Git钩子集成

将API变更检测和通知集成到Git钩子中，实现方案如下：

```bash
#!/bin/sh
# .git/hooks/pre-push

# 检测API变更
node scripts/detect-api-changes.js

# 如果检测到变更，提示开发者
if [ $? -eq 1 ]; then
  echo "检测到API变更，请确认是否需要通知相关方"
  read -p "是否发送API变更通知？(y/n) " answer
  if [ "$answer" = "y" ]; then
    node scripts/send-api-notifications.js
  fi
fi

exit 0
```

### 5.3 定时任务集成

将API变更检测和通知集成到定时任务中，实现方案如下：

```javascript
// scripts/schedule-api-change-detection.js
const cron = require('node-cron');
const { exec } = require('child_process');

// 每天凌晨2点执行API变更检测和通知
cron.schedule('0 2 * * *', () => {
  console.log('执行API变更检测和通知...');
  
  // 执行检测脚本
  exec('node scripts/detect-api-changes.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`检测错误: ${error}`);
      return;
    }
    
    // 如果检测到变更，发送通知
    if (stdout.includes('API变更')) {
      exec('node scripts/send-api-notifications.js', (error, stdout, stderr) => {
        if (error) {
          console.error(`通知错误: ${error}`);
          return;
        }
        console.log('通知发送成功');
      });
    }
  });
});
```

## 6. 实施步骤

### 6.1 准备工作

1. **安装依赖**：安装所需的依赖包，如nodemailer、@slack/web-api、openapi-diff等
2. **配置环境**：配置邮件服务器、Slack令牌等环境变量
3. **创建目录**：创建scripts目录，用于存放脚本文件

### 6.2 实现脚本

1. **实现检测脚本**：创建scripts/detect-api-changes.js，实现API变更检测
2. **实现通知脚本**：创建scripts/send-api-notifications.js，实现API变更通知
3. **实现定时任务**：创建scripts/schedule-api-change-detection.js，实现定时检测和通知

### 6.3 集成配置

1. **配置CI/CD**：创建.github/workflows/api-change-notification.yml，配置CI/CD流程
2. **配置Git钩子**：创建.git/hooks/pre-push，配置Git钩子
3. **配置定时任务**：在package.json中添加定时任务脚本

### 6.4 测试验证

1. **单元测试**：编写单元测试，测试检测和通知功能
2. **集成测试**：进行集成测试，测试整个流程
3. **模拟变更**：模拟API变更，验证通知功能

## 7. 最佳实践

1. **变更分类**：根据变更类型和影响范围分类，避免过多通知
2. **通知模板**：使用标准化的通知模板，提高通知质量
3. **通知订阅**：允许开发者订阅感兴趣的API变更通知
4. **通知归档**：保存通知历史，方便查询和追溯
5. **通知反馈**：收集通知反馈，持续改进通知机制

## 8. 结论

通过实施API变更通知机制，AIBUBB项目将能够及时通知相关方API的变更情况，减少因API变更导致的问题，提高开发效率和系统稳定性。本方案涵盖了通知机制设计、变更检测实现、通知发送方式和集成流程，提供了详细的实施步骤和最佳实践。
