# Swagger注释标准

## 1. 概述

本文档定义了AIBUBB项目中使用Swagger注释的标准和最佳实践，旨在确保API文档的一致性、完整性和准确性。遵循这些标准将使API文档更加清晰、易于理解，并减少文档与实际实现之间的差异。

## 2. 基本原则

1. **完整性**：每个API端点都必须有完整的Swagger注释
2. **准确性**：注释必须与实际实现一致
3. **一致性**：所有API端点的注释格式应保持一致
4. **可读性**：注释应清晰易读，使用简洁明了的语言
5. **示例性**：提供有意义的请求和响应示例

## 3. 注释结构

### 3.1 路由注释

每个API路由应包含以下注释部分：

1. **路径和方法**：定义API的URL路径和HTTP方法
2. **摘要和描述**：简要和详细描述API的功能
3. **标签**：用于分组API
4. **参数**：路径参数、查询参数、请求体等
5. **响应**：不同状态码的响应格式
6. **安全要求**：认证要求

### 3.2 模型注释

数据模型应包含以下注释部分：

1. **模型名称**：定义模型的名称
2. **属性**：模型的所有属性及其类型
3. **示例**：模型的示例值
4. **描述**：模型的描述和用途

## 4. 注释模板

### 4.1 GET请求模板

```javascript
/**
 * @swagger
 * /api/v1/{path}:
 *   get:
 *     summary: 简短描述（一句话）
 *     description: 详细描述（可多行）
 *     tags: [标签名称]
 *     parameters:
 *       - in: path
 *         name: 参数名
 *         required: true
 *         schema:
 *           type: 参数类型
 *         description: 参数描述
 *       - in: query
 *         name: 参数名
 *         schema:
 *           type: 参数类型
 *         description: 参数描述
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功响应描述
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     // 响应数据属性
 *       400:
 *         $ref: '#/components/responses/BadRequestError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 4.2 POST请求模板

```javascript
/**
 * @swagger
 * /api/v1/{path}:
 *   post:
 *     summary: 简短描述（一句话）
 *     description: 详细描述（可多行）
 *     tags: [标签名称]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [必填字段1, 必填字段2]
 *             properties:
 *               字段1:
 *                 type: 类型
 *                 description: 描述
 *                 example: 示例值
 *               字段2:
 *                 type: 类型
 *                 description: 描述
 *                 example: 示例值
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     // 响应数据属性
 *       400:
 *         $ref: '#/components/responses/BadRequestError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 4.3 PUT请求模板

```javascript
/**
 * @swagger
 * /api/v1/{path}/{id}:
 *   put:
 *     summary: 简短描述（一句话）
 *     description: 详细描述（可多行）
 *     tags: [标签名称]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 资源ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               字段1:
 *                 type: 类型
 *                 description: 描述
 *                 example: 示例值
 *               字段2:
 *                 type: 类型
 *                 description: 描述
 *                 example: 示例值
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     // 响应数据属性
 *       400:
 *         $ref: '#/components/responses/BadRequestError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 4.4 DELETE请求模板

```javascript
/**
 * @swagger
 * /api/v1/{path}/{id}:
 *   delete:
 *     summary: 简短描述（一句话）
 *     description: 详细描述（可多行）
 *     tags: [标签名称]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 资源ID
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 资源已成功删除
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 4.5 模型定义模板

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     模型名称:
 *       type: object
 *       properties:
 *         属性1:
 *           type: 类型
 *           description: 描述
 *           example: 示例值
 *         属性2:
 *           type: 类型
 *           description: 描述
 *           example: 示例值
 *       required:
 *         - 必填属性1
 *         - 必填属性2
 */
```

## 5. 数据类型

使用以下数据类型：

| Swagger类型 | JavaScript类型 | 描述 |
|------------|---------------|------|
| `string` | String | 字符串 |
| `number` | Number | 数字（包括整数和浮点数） |
| `integer` | Number | 整数 |
| `boolean` | Boolean | 布尔值 |
| `array` | Array | 数组 |
| `object` | Object | 对象 |

对于特殊格式的字符串，使用`format`属性：

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           example: 123e4567-e89b-12d3-a456-************
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *         birthDate:
 *           type: string
 *           format: date
 *           example: 1990-01-01
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: 2023-01-01T12:00:00Z
 */
```

## 6. 常见格式

### 6.1 日期和时间

- 日期：`YYYY-MM-DD`（例如：`2023-01-01`）
- 日期时间：`YYYY-MM-DDTHH:MM:SSZ`（例如：`2023-01-01T12:00:00Z`）

### 6.2 分页参数

```javascript
/**
 * @swagger
 * /api/v1/resources:
 *   get:
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码（从1开始）
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页记录数
 */
```

### 6.3 分页响应

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     PaginatedResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         data:
 *           type: object
 *           properties:
 *             items:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Resource'
 *             pagination:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   example: 100
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 pageSize:
 *                   type: integer
 *                   example: 20
 *                 totalPages:
 *                   type: integer
 *                   example: 5
 */
```

### 6.4 错误响应

```javascript
/**
 * @swagger
 * components:
 *   responses:
 *     BadRequestError:
 *       description: 请求参数错误
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               success:
 *                 type: boolean
 *                 example: false
 *               error:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                     example: BAD_REQUEST
 *                   message:
 *                     type: string
 *                     example: 请求参数错误
 *                   details:
 *                     type: object
 *                     example:
 *                       name: 名称不能为空
 */
```

## 7. 安全定义

### 7.1 JWT认证

```javascript
/**
 * @swagger
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */
```

### 7.2 API密钥认证

```javascript
/**
 * @swagger
 * components:
 *   securitySchemes:
 *     apiKey:
 *       type: apiKey
 *       in: header
 *       name: X-API-KEY
 */
```

## 8. 标签分组

```javascript
/**
 * @swagger
 * tags:
 *   - name: Auth
 *     description: 认证相关API
 *   - name: Users
 *     description: 用户相关API
 *   - name: LearningPlans
 *     description: 学习计划相关API
 *   - name: Tags
 *     description: 标签相关API
 *   - name: Notes
 *     description: 笔记相关API
 *   - name: Exercises
 *     description: 练习相关API
 *   - name: Insights
 *     description: 观点相关API
 *   - name: Square
 *     description: 广场相关API
 *   - name: Statistics
 *     description: 统计相关API
 */
```

## 9. 完整示例

### 9.1 GET请求示例

```javascript
/**
 * @swagger
 * /api/v1/learning-plans/{id}:
 *   get:
 *     summary: 获取学习计划详情
 *     description: 根据ID获取学习计划的详细信息，包括标签和进度
 *     tags: [LearningPlans]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 学习计划ID
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取学习计划详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 101
 *                     themeId:
 *                       type: integer
 *                       example: 1
 *                     themeName:
 *                       type: string
 *                       example: 人际沟通
 *                     title:
 *                       type: string
 *                       example: 提升与伴侣的沟通能力
 *                     description:
 *                       type: string
 *                       example: 我希望能更好地与伴侣沟通，减少误解和冲突
 *                     targetDays:
 *                       type: integer
 *                       example: 14
 *                     completedDays:
 *                       type: integer
 *                       example: 5
 *                     progress:
 *                       type: integer
 *                       example: 35
 *                     status:
 *                       type: string
 *                       enum: [not_started, in_progress, completed, paused]
 *                       example: in_progress
 *                     startDate:
 *                       type: string
 *                       format: date
 *                       example: 2023-06-15
 *                     endDate:
 *                       type: string
 *                       format: date
 *                       example: 2023-06-29
 *                     isCurrent:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-06-15T08:00:00Z
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

### 9.2 POST请求示例

```javascript
/**
 * @swagger
 * /api/v1/learning-plans:
 *   post:
 *     summary: 创建学习计划
 *     description: 创建新的学习计划，包括主题、标题、描述和目标天数
 *     tags: [LearningPlans]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [themeId, title, targetDays]
 *             properties:
 *               themeId:
 *                 type: integer
 *                 description: 主题ID
 *                 example: 1
 *               title:
 *                 type: string
 *                 description: 学习计划标题
 *                 example: 提升与伴侣的沟通能力
 *               description:
 *                 type: string
 *                 description: 学习计划描述
 *                 example: 我希望能更好地与伴侣沟通，减少误解和冲突
 *               targetDays:
 *                 type: integer
 *                 description: 目标天数
 *                 minimum: 1
 *                 maximum: 90
 *                 example: 14
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: 学习计划创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 101
 *                     themeId:
 *                       type: integer
 *                       example: 1
 *                     title:
 *                       type: string
 *                       example: 提升与伴侣的沟通能力
 *                     description:
 *                       type: string
 *                       example: 我希望能更好地与伴侣沟通，减少误解和冲突
 *                     targetDays:
 *                       type: integer
 *                       example: 14
 *                     status:
 *                       type: string
 *                       example: not_started
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-06-15T08:00:00Z
 *       400:
 *         $ref: '#/components/responses/BadRequestError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

## 10. 注释检查清单

在提交代码前，请确保API注释满足以下要求：

1. **基本信息**
   - [ ] 路径和方法正确
   - [ ] 摘要和描述清晰明了
   - [ ] 标签分组适当

2. **参数**
   - [ ] 所有路径参数都已定义
   - [ ] 所有查询参数都已定义
   - [ ] 所有请求体参数都已定义
   - [ ] 必填参数已标记
   - [ ] 参数类型和格式正确
   - [ ] 参数描述清晰

3. **响应**
   - [ ] 成功响应已定义
   - [ ] 常见错误响应已定义
   - [ ] 响应格式与实际返回一致
   - [ ] 响应示例有意义

4. **安全**
   - [ ] 安全要求已定义
   - [ ] 认证方式正确

5. **一致性**
   - [ ] 注释格式与项目标准一致
   - [ ] 命名风格一致
   - [ ] 示例风格一致

## 11. 最佳实践

1. **保持简洁**：注释应简洁明了，避免冗余
2. **使用引用**：使用`$ref`引用通用组件，避免重复
3. **提供示例**：为所有参数和响应提供有意义的示例
4. **保持同步**：修改API实现时同步更新注释
5. **使用枚举**：对于有限选项的字段，使用枚举
6. **描述限制**：描述参数的限制，如最小值、最大值、格式等
7. **分组合理**：使用标签合理分组API
8. **版本控制**：在注释中包含API版本信息

## 12. 工具和资源

1. **Swagger编辑器**：[Swagger Editor](https://editor.swagger.io/)
2. **Swagger UI**：访问项目的`/api-docs`路径
3. **ReDoc**：访问项目的`/redoc`路径
4. **Swagger规范**：[OpenAPI 3.0规范](https://swagger.io/specification/)
5. **VSCode插件**：[Swagger Viewer](https://marketplace.visualstudio.com/items?itemName=Arjun.swagger-viewer)

## 13. 结论

遵循本文档中的Swagger注释标准和最佳实践，将确保AIBUBB项目的API文档清晰、准确、一致，提高开发效率和API使用体验。所有开发人员都应该熟悉这些标准，并在日常开发中严格遵循。
