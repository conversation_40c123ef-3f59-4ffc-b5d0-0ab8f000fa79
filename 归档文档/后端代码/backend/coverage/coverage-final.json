{"/Users/<USER>/xiangmu/AIBUBB/backend/controllers/ai.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/ai.controller.js", "statementMap": {"0": {"start": {"line": 1, "column": 18}, "end": {"line": 1, "column": 51}}, "1": {"start": {"line": 2, "column": 20}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 42}}, "3": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 36}}, "4": {"start": {"line": 15, "column": 19}, "end": {"line": 33, "column": 1}}, "5": {"start": {"line": 16, "column": 2}, "end": {"line": 32, "column": 3}}, "6": {"start": {"line": 17, "column": 18}, "end": {"line": 17, "column": 43}}, "7": {"start": {"line": 20, "column": 26}, "end": {"line": 20, "column": 66}}, "8": {"start": {"line": 22, "column": 4}, "end": {"line": 29, "column": 7}}, "9": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 52}}, "10": {"start": {"line": 40, "column": 21}, "end": {"line": 47, "column": 1}}, "11": {"start": {"line": 41, "column": 2}, "end": {"line": 46, "column": 3}}, "12": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 32}}, "13": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 55}}, "14": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 54}}, "15": {"start": {"line": 54, "column": 26}, "end": {"line": 87, "column": 1}}, "16": {"start": {"line": 55, "column": 2}, "end": {"line": 86, "column": 3}}, "17": {"start": {"line": 56, "column": 44}, "end": {"line": 56, "column": 52}}, "18": {"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, "19": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 54}}, "20": {"start": {"line": 63, "column": 21}, "end": {"line": 70, "column": 5}}, "21": {"start": {"line": 73, "column": 17}, "end": {"line": 73, "column": 55}}, "22": {"start": {"line": 75, "column": 4}, "end": {"line": 79, "column": 7}}, "23": {"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 5}}, "24": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 67}}, "25": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 59}}, "26": {"start": {"line": 94, "column": 36}, "end": {"line": 124, "column": 1}}, "27": {"start": {"line": 95, "column": 2}, "end": {"line": 123, "column": 3}}, "28": {"start": {"line": 96, "column": 68}, "end": {"line": 96, "column": 76}}, "29": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": 5}}, "30": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 54}}, "31": {"start": {"line": 103, "column": 21}, "end": {"line": 108, "column": 5}}, "32": {"start": {"line": 111, "column": 24}, "end": {"line": 111, "column": 77}}, "33": {"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 7}}, "34": {"start": {"line": 119, "column": 4}, "end": {"line": 121, "column": 5}}, "35": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 77}}, "36": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 69}}, "37": {"start": {"line": 132, "column": 31}, "end": {"line": 148, "column": 1}}, "38": {"start": {"line": 134, "column": 38}, "end": {"line": 134, "column": 43}}, "39": {"start": {"line": 135, "column": 42}, "end": {"line": 135, "column": 47}}, "40": {"start": {"line": 138, "column": 21}, "end": {"line": 138, "column": 77}}, "41": {"start": {"line": 139, "column": 25}, "end": {"line": 139, "column": 89}}, "42": {"start": {"line": 140, "column": 20}, "end": {"line": 140, "column": 47}}, "43": {"start": {"line": 142, "column": 2}, "end": {"line": 147, "column": 4}}, "44": {"start": {"line": 150, "column": 0}, "end": {"line": 155, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 19}, "end": {"line": 15, "column": 20}}, "loc": {"start": {"line": 15, "column": 39}, "end": {"line": 33, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 22}}, "loc": {"start": {"line": 40, "column": 41}, "end": {"line": 47, "column": 1}}, "line": 40}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 54, "column": 26}, "end": {"line": 54, "column": 27}}, "loc": {"start": {"line": 54, "column": 46}, "end": {"line": 87, "column": 1}}, "line": 54}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 94, "column": 36}, "end": {"line": 94, "column": 37}}, "loc": {"start": {"line": 94, "column": 56}, "end": {"line": 124, "column": 1}}, "line": 94}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 132, "column": 31}, "end": {"line": 132, "column": 32}}, "loc": {"start": {"line": 132, "column": 47}, "end": {"line": 148, "column": 1}}, "line": 132}}, "branchMap": {"0": {"loc": {"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, "type": "if", "locations": [{"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, {"start": {}, "end": {}}], "line": 58}, "1": {"loc": {"start": {"line": 65, "column": 19}, "end": {"line": 65, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 19}, "end": {"line": 65, "column": 30}}, {"start": {"line": 65, "column": 34}, "end": {"line": 65, "column": 36}}], "line": 65}, "2": {"loc": {"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 24}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 19}}, {"start": {"line": 67, "column": 23}, "end": {"line": 67, "column": 24}}], "line": 67}, "3": {"loc": {"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 5}}, "type": "if", "locations": [{"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 5}}, {"start": {}, "end": {}}], "line": 82}, "4": {"loc": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 169}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 18}}, {"start": {"line": 82, "column": 22}, "end": {"line": 82, "column": 169}}], "line": 82}, "5": {"loc": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": 5}}, "type": "if", "locations": [{"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": 5}}, {"start": {}, "end": {}}], "line": 98}, "6": {"loc": {"start": {"line": 105, "column": 15}, "end": {"line": 105, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 15}, "end": {"line": 105, "column": 22}}, {"start": {"line": 105, "column": 26}, "end": {"line": 105, "column": 28}}], "line": 105}, "7": {"loc": {"start": {"line": 106, "column": 25}, "end": {"line": 106, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 25}, "end": {"line": 106, "column": 42}}, {"start": {"line": 106, "column": 46}, "end": {"line": 106, "column": 54}}], "line": 106}, "8": {"loc": {"start": {"line": 107, "column": 24}, "end": {"line": 107, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 24}, "end": {"line": 107, "column": 50}}, {"start": {"line": 107, "column": 54}, "end": {"line": 107, "column": 55}}], "line": 107}, "9": {"loc": {"start": {"line": 119, "column": 4}, "end": {"line": 121, "column": 5}}, "type": "if", "locations": [{"start": {"line": 119, "column": 4}, "end": {"line": 121, "column": 5}}, {"start": {}, "end": {}}], "line": 119}, "10": {"loc": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 169}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 18}}, {"start": {"line": 119, "column": 22}, "end": {"line": 119, "column": 169}}], "line": 119}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/batchOperation.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/batchOperation.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 62}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 36}}, "4": {"start": {"line": 20, "column": 28}, "end": {"line": 47, "column": 1}}, "5": {"start": {"line": 21, "column": 2}, "end": {"line": 46, "column": 3}}, "6": {"start": {"line": 22, "column": 37}, "end": {"line": 22, "column": 45}}, "7": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}, "8": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 52}}, "9": {"start": {"line": 29, "column": 26}, "end": {"line": 29, "column": 73}}, "10": {"start": {"line": 33, "column": 4}, "end": {"line": 37, "column": 5}}, "11": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 63}}, "12": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 60}}, "13": {"start": {"line": 39, "column": 4}, "end": {"line": 42, "column": 7}}, "14": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 48}}, "15": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 61}}, "16": {"start": {"line": 54, "column": 25}, "end": {"line": 76, "column": 1}}, "17": {"start": {"line": 55, "column": 2}, "end": {"line": 75, "column": 3}}, "18": {"start": {"line": 56, "column": 20}, "end": {"line": 56, "column": 28}}, "19": {"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, "20": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 52}}, "21": {"start": {"line": 63, "column": 26}, "end": {"line": 63, "column": 73}}, "22": {"start": {"line": 66, "column": 19}, "end": {"line": 66, "column": 60}}, "23": {"start": {"line": 68, "column": 4}, "end": {"line": 71, "column": 7}}, "24": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 47}}, "25": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 58}}, "26": {"start": {"line": 83, "column": 29}, "end": {"line": 110, "column": 1}}, "27": {"start": {"line": 84, "column": 2}, "end": {"line": 109, "column": 3}}, "28": {"start": {"line": 85, "column": 37}, "end": {"line": 85, "column": 45}}, "29": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "30": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 52}}, "31": {"start": {"line": 92, "column": 27}, "end": {"line": 92, "column": 75}}, "32": {"start": {"line": 96, "column": 4}, "end": {"line": 100, "column": 5}}, "33": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 64}}, "34": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 62}}, "35": {"start": {"line": 102, "column": 4}, "end": {"line": 105, "column": 7}}, "36": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 48}}, "37": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 62}}, "38": {"start": {"line": 117, "column": 26}, "end": {"line": 139, "column": 1}}, "39": {"start": {"line": 118, "column": 2}, "end": {"line": 138, "column": 3}}, "40": {"start": {"line": 119, "column": 20}, "end": {"line": 119, "column": 28}}, "41": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, "42": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 52}}, "43": {"start": {"line": 126, "column": 27}, "end": {"line": 126, "column": 75}}, "44": {"start": {"line": 129, "column": 19}, "end": {"line": 129, "column": 62}}, "45": {"start": {"line": 131, "column": 4}, "end": {"line": 134, "column": 7}}, "46": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": 47}}, "47": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 59}}, "48": {"start": {"line": 146, "column": 29}, "end": {"line": 170, "column": 1}}, "49": {"start": {"line": 147, "column": 2}, "end": {"line": 169, "column": 3}}, "50": {"start": {"line": 148, "column": 20}, "end": {"line": 148, "column": 28}}, "51": {"start": {"line": 149, "column": 19}, "end": {"line": 149, "column": 30}}, "52": {"start": {"line": 151, "column": 4}, "end": {"line": 153, "column": 5}}, "53": {"start": {"line": 152, "column": 6}, "end": {"line": 152, "column": 52}}, "54": {"start": {"line": 156, "column": 24}, "end": {"line": 156, "column": 66}}, "55": {"start": {"line": 159, "column": 19}, "end": {"line": 159, "column": 70}}, "56": {"start": {"line": 161, "column": 4}, "end": {"line": 165, "column": 7}}, "57": {"start": {"line": 167, "column": 4}, "end": {"line": 167, "column": 48}}, "58": {"start": {"line": 168, "column": 4}, "end": {"line": 168, "column": 62}}, "59": {"start": {"line": 177, "column": 26}, "end": {"line": 201, "column": 1}}, "60": {"start": {"line": 178, "column": 2}, "end": {"line": 200, "column": 3}}, "61": {"start": {"line": 179, "column": 20}, "end": {"line": 179, "column": 28}}, "62": {"start": {"line": 180, "column": 19}, "end": {"line": 180, "column": 30}}, "63": {"start": {"line": 182, "column": 4}, "end": {"line": 184, "column": 5}}, "64": {"start": {"line": 183, "column": 6}, "end": {"line": 183, "column": 52}}, "65": {"start": {"line": 187, "column": 24}, "end": {"line": 187, "column": 66}}, "66": {"start": {"line": 190, "column": 19}, "end": {"line": 190, "column": 67}}, "67": {"start": {"line": 192, "column": 4}, "end": {"line": 196, "column": 7}}, "68": {"start": {"line": 198, "column": 4}, "end": {"line": 198, "column": 47}}, "69": {"start": {"line": 199, "column": 4}, "end": {"line": 199, "column": 59}}, "70": {"start": {"line": 208, "column": 32}, "end": {"line": 232, "column": 1}}, "71": {"start": {"line": 209, "column": 2}, "end": {"line": 231, "column": 3}}, "72": {"start": {"line": 210, "column": 20}, "end": {"line": 210, "column": 28}}, "73": {"start": {"line": 211, "column": 19}, "end": {"line": 211, "column": 30}}, "74": {"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}, "75": {"start": {"line": 214, "column": 6}, "end": {"line": 214, "column": 52}}, "76": {"start": {"line": 218, "column": 27}, "end": {"line": 218, "column": 72}}, "77": {"start": {"line": 221, "column": 19}, "end": {"line": 221, "column": 76}}, "78": {"start": {"line": 223, "column": 4}, "end": {"line": 227, "column": 7}}, "79": {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 48}}, "80": {"start": {"line": 230, "column": 4}, "end": {"line": 230, "column": 65}}, "81": {"start": {"line": 239, "column": 29}, "end": {"line": 263, "column": 1}}, "82": {"start": {"line": 240, "column": 2}, "end": {"line": 262, "column": 3}}, "83": {"start": {"line": 241, "column": 20}, "end": {"line": 241, "column": 28}}, "84": {"start": {"line": 242, "column": 19}, "end": {"line": 242, "column": 30}}, "85": {"start": {"line": 244, "column": 4}, "end": {"line": 246, "column": 5}}, "86": {"start": {"line": 245, "column": 6}, "end": {"line": 245, "column": 52}}, "87": {"start": {"line": 249, "column": 27}, "end": {"line": 249, "column": 72}}, "88": {"start": {"line": 252, "column": 19}, "end": {"line": 252, "column": 73}}, "89": {"start": {"line": 254, "column": 4}, "end": {"line": 258, "column": 7}}, "90": {"start": {"line": 260, "column": 4}, "end": {"line": 260, "column": 47}}, "91": {"start": {"line": 261, "column": 4}, "end": {"line": 261, "column": 62}}, "92": {"start": {"line": 265, "column": 0}, "end": {"line": 274, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 20, "column": 28}, "end": {"line": 20, "column": 29}}, "loc": {"start": {"line": 20, "column": 48}, "end": {"line": 47, "column": 1}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 54, "column": 25}, "end": {"line": 54, "column": 26}}, "loc": {"start": {"line": 54, "column": 45}, "end": {"line": 76, "column": 1}}, "line": 54}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 83, "column": 29}, "end": {"line": 83, "column": 30}}, "loc": {"start": {"line": 83, "column": 49}, "end": {"line": 110, "column": 1}}, "line": 83}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 117, "column": 26}, "end": {"line": 117, "column": 27}}, "loc": {"start": {"line": 117, "column": 46}, "end": {"line": 139, "column": 1}}, "line": 117}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 146, "column": 29}, "end": {"line": 146, "column": 30}}, "loc": {"start": {"line": 146, "column": 49}, "end": {"line": 170, "column": 1}}, "line": 146}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 177, "column": 26}, "end": {"line": 177, "column": 27}}, "loc": {"start": {"line": 177, "column": 46}, "end": {"line": 201, "column": 1}}, "line": 177}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 208, "column": 32}, "end": {"line": 208, "column": 33}}, "loc": {"start": {"line": 208, "column": 52}, "end": {"line": 232, "column": 1}}, "line": 208}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 239, "column": 29}, "end": {"line": 239, "column": 30}}, "loc": {"start": {"line": 239, "column": 49}, "end": {"line": 263, "column": 1}}, "line": 239}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 17}, "end": {"line": 22, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 22, "column": 27}, "end": {"line": 22, "column": 32}}], "line": 22}, "1": {"loc": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}, "type": "if", "locations": [{"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}, {"start": {}, "end": {}}], "line": 24}, "2": {"loc": {"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 27}}, {"start": {"line": 24, "column": 31}, "end": {"line": 24, "column": 47}}], "line": 24}, "3": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 37, "column": 5}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 37, "column": 5}}, {"start": {"line": 35, "column": 11}, "end": {"line": 37, "column": 5}}], "line": 33}, "4": {"loc": {"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, "type": "if", "locations": [{"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, {"start": {}, "end": {}}], "line": 58}, "5": {"loc": {"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 27}}, {"start": {"line": 58, "column": 31}, "end": {"line": 58, "column": 47}}], "line": 58}, "6": {"loc": {"start": {"line": 85, "column": 17}, "end": {"line": 85, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 85, "column": 27}, "end": {"line": 85, "column": 32}}], "line": 85}, "7": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, {"start": {}, "end": {}}], "line": 87}, "8": {"loc": {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 27}}, {"start": {"line": 87, "column": 31}, "end": {"line": 87, "column": 47}}], "line": 87}, "9": {"loc": {"start": {"line": 96, "column": 4}, "end": {"line": 100, "column": 5}}, "type": "if", "locations": [{"start": {"line": 96, "column": 4}, "end": {"line": 100, "column": 5}}, {"start": {"line": 98, "column": 11}, "end": {"line": 100, "column": 5}}], "line": 96}, "10": {"loc": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, {"start": {}, "end": {}}], "line": 121}, "11": {"loc": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 27}}, {"start": {"line": 121, "column": 31}, "end": {"line": 121, "column": 47}}], "line": 121}, "12": {"loc": {"start": {"line": 151, "column": 4}, "end": {"line": 153, "column": 5}}, "type": "if", "locations": [{"start": {"line": 151, "column": 4}, "end": {"line": 153, "column": 5}}, {"start": {}, "end": {}}], "line": 151}, "13": {"loc": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 27}}, {"start": {"line": 151, "column": 31}, "end": {"line": 151, "column": 47}}], "line": 151}, "14": {"loc": {"start": {"line": 182, "column": 4}, "end": {"line": 184, "column": 5}}, "type": "if", "locations": [{"start": {"line": 182, "column": 4}, "end": {"line": 184, "column": 5}}, {"start": {}, "end": {}}], "line": 182}, "15": {"loc": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 27}}, {"start": {"line": 182, "column": 31}, "end": {"line": 182, "column": 47}}], "line": 182}, "16": {"loc": {"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}, "type": "if", "locations": [{"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}, {"start": {}, "end": {}}], "line": 213}, "17": {"loc": {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 27}}, {"start": {"line": 213, "column": 31}, "end": {"line": 213, "column": 47}}], "line": 213}, "18": {"loc": {"start": {"line": 244, "column": 4}, "end": {"line": 246, "column": 5}}, "type": "if", "locations": [{"start": {"line": 244, "column": 4}, "end": {"line": 246, "column": 5}}, {"start": {}, "end": {}}], "line": 244}, "19": {"loc": {"start": {"line": 244, "column": 8}, "end": {"line": 244, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 244, "column": 8}, "end": {"line": 244, "column": 27}}, {"start": {"line": 244, "column": 31}, "end": {"line": 244, "column": 47}}], "line": 244}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/cleanup.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/cleanup.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 62}}, "3": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 36}}, "4": {"start": {"line": 15, "column": 23}, "end": {"line": 15, "column": 68}}, "5": {"start": {"line": 22, "column": 25}, "end": {"line": 29, "column": 1}}, "6": {"start": {"line": 23, "column": 2}, "end": {"line": 28, "column": 3}}, "7": {"start": {"line": 24, "column": 19}, "end": {"line": 24, "column": 45}}, "8": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 44}}, "9": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 58}}, "10": {"start": {"line": 36, "column": 28}, "end": {"line": 75, "column": 1}}, "11": {"start": {"line": 37, "column": 2}, "end": {"line": 74, "column": 3}}, "12": {"start": {"line": 38, "column": 22}, "end": {"line": 38, "column": 30}}, "13": {"start": {"line": 41, "column": 4}, "end": {"line": 47, "column": 5}}, "14": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 61}}, "15": {"start": {"line": 49, "column": 4}, "end": {"line": 55, "column": 5}}, "16": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 64}}, "17": {"start": {"line": 57, "column": 4}, "end": {"line": 63, "column": 5}}, "18": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 64}}, "19": {"start": {"line": 66, "column": 26}, "end": {"line": 66, "column": 64}}, "20": {"start": {"line": 68, "column": 4}, "end": {"line": 71, "column": 7}}, "21": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 61}}, "22": {"start": {"line": 82, "column": 19}, "end": {"line": 131, "column": 1}}, "23": {"start": {"line": 83, "column": 2}, "end": {"line": 130, "column": 3}}, "24": {"start": {"line": 84, "column": 41}, "end": {"line": 84, "column": 49}}, "25": {"start": {"line": 87, "column": 4}, "end": {"line": 119, "column": 5}}, "26": {"start": {"line": 89, "column": 6}, "end": {"line": 95, "column": 7}}, "27": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 63}}, "28": {"start": {"line": 97, "column": 21}, "end": {"line": 97, "column": 88}}, "29": {"start": {"line": 99, "column": 6}, "end": {"line": 102, "column": 9}}, "30": {"start": {"line": 105, "column": 21}, "end": {"line": 105, "column": 60}}, "31": {"start": {"line": 107, "column": 6}, "end": {"line": 118, "column": 7}}, "32": {"start": {"line": 108, "column": 8}, "end": {"line": 111, "column": 11}}, "33": {"start": {"line": 113, "column": 8}, "end": {"line": 117, "column": 11}}, "34": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, "35": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 53}}, "36": {"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}, "37": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 55}}, "38": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 52}}, "39": {"start": {"line": 138, "column": 25}, "end": {"line": 149, "column": 1}}, "40": {"start": {"line": 139, "column": 2}, "end": {"line": 148, "column": 3}}, "41": {"start": {"line": 140, "column": 4}, "end": {"line": 140, "column": 62}}, "42": {"start": {"line": 142, "column": 4}, "end": {"line": 145, "column": 7}}, "43": {"start": {"line": 147, "column": 4}, "end": {"line": 147, "column": 58}}, "44": {"start": {"line": 156, "column": 24}, "end": {"line": 166, "column": 1}}, "45": {"start": {"line": 157, "column": 2}, "end": {"line": 165, "column": 3}}, "46": {"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 63}}, "47": {"start": {"line": 160, "column": 4}, "end": {"line": 162, "column": 7}}, "48": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": 57}}, "49": {"start": {"line": 168, "column": 0}, "end": {"line": 174, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 22, "column": 25}, "end": {"line": 22, "column": 26}}, "loc": {"start": {"line": 22, "column": 45}, "end": {"line": 29, "column": 1}}, "line": 22}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 28}, "end": {"line": 36, "column": 29}}, "loc": {"start": {"line": 36, "column": 48}, "end": {"line": 75, "column": 1}}, "line": 36}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 20}}, "loc": {"start": {"line": 82, "column": 39}, "end": {"line": 131, "column": 1}}, "line": 82}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 138, "column": 25}, "end": {"line": 138, "column": 26}}, "loc": {"start": {"line": 138, "column": 45}, "end": {"line": 149, "column": 1}}, "line": 138}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 156, "column": 24}, "end": {"line": 156, "column": 25}}, "loc": {"start": {"line": 156, "column": 44}, "end": {"line": 166, "column": 1}}, "line": 156}}, "branchMap": {"0": {"loc": {"start": {"line": 41, "column": 4}, "end": {"line": 47, "column": 5}}, "type": "if", "locations": [{"start": {"line": 41, "column": 4}, "end": {"line": 47, "column": 5}}, {"start": {}, "end": {}}], "line": 41}, "1": {"loc": {"start": {"line": 41, "column": 8}, "end": {"line": 45, "column": 5}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 45}}, {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 48}}, {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 33}}, {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 35}}], "line": 41}, "2": {"loc": {"start": {"line": 49, "column": 4}, "end": {"line": 55, "column": 5}}, "type": "if", "locations": [{"start": {"line": 49, "column": 4}, "end": {"line": 55, "column": 5}}, {"start": {}, "end": {}}], "line": 49}, "3": {"loc": {"start": {"line": 49, "column": 8}, "end": {"line": 53, "column": 5}}, "type": "binary-expr", "locations": [{"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 41}}, {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 44}}, {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 30}}, {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 32}}], "line": 49}, "4": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 63, "column": 5}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 63, "column": 5}}, {"start": {}, "end": {}}], "line": 57}, "5": {"loc": {"start": {"line": 57, "column": 8}, "end": {"line": 61, "column": 5}}, "type": "binary-expr", "locations": [{"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 47}}, {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 50}}, {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 41}}, {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 47}}], "line": 57}, "6": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 119, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 119, "column": 5}}, {"start": {"line": 103, "column": 11}, "end": {"line": 119, "column": 5}}], "line": 87}, "7": {"loc": {"start": {"line": 89, "column": 6}, "end": {"line": 95, "column": 7}}, "type": "if", "locations": [{"start": {"line": 89, "column": 6}, "end": {"line": 95, "column": 7}}, {"start": {}, "end": {}}], "line": 89}, "8": {"loc": {"start": {"line": 89, "column": 10}, "end": {"line": 93, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 10}, "end": {"line": 89, "column": 37}}, {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 40}}, {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 25}}, {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": 27}}], "line": 89}, "9": {"loc": {"start": {"line": 107, "column": 6}, "end": {"line": 118, "column": 7}}, "type": "if", "locations": [{"start": {"line": 107, "column": 6}, "end": {"line": 118, "column": 7}}, {"start": {"line": 112, "column": 13}, "end": {"line": 118, "column": 7}}], "line": 107}, "10": {"loc": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, {"start": {}, "end": {}}], "line": 121}, "11": {"loc": {"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}, {"start": {}, "end": {}}], "line": 125}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0, 0, 0], "2": [0, 0], "3": [0, 0, 0, 0], "4": [0, 0], "5": [0, 0, 0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0, 0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/dailyContentV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/dailyContentV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 62}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 36}}, "4": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 78}}, "5": {"start": {"line": 21, "column": 24}, "end": {"line": 36, "column": 1}}, "6": {"start": {"line": 22, "column": 2}, "end": {"line": 35, "column": 3}}, "7": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 33}}, "8": {"start": {"line": 24, "column": 19}, "end": {"line": 24, "column": 30}}, "9": {"start": {"line": 27, "column": 21}, "end": {"line": 27, "column": 78}}, "10": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 50}}, "11": {"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": 5}}, "12": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 57}}, "13": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 57}}, "14": {"start": {"line": 43, "column": 23}, "end": {"line": 58, "column": 1}}, "15": {"start": {"line": 44, "column": 2}, "end": {"line": 57, "column": 3}}, "16": {"start": {"line": 45, "column": 19}, "end": {"line": 45, "column": 29}}, "17": {"start": {"line": 46, "column": 19}, "end": {"line": 46, "column": 30}}, "18": {"start": {"line": 49, "column": 20}, "end": {"line": 49, "column": 75}}, "19": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 45}}, "20": {"start": {"line": 53, "column": 4}, "end": {"line": 55, "column": 5}}, "21": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 57}}, "22": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 56}}, "23": {"start": {"line": 65, "column": 22}, "end": {"line": 81, "column": 1}}, "24": {"start": {"line": 66, "column": 2}, "end": {"line": 80, "column": 3}}, "25": {"start": {"line": 67, "column": 19}, "end": {"line": 67, "column": 29}}, "26": {"start": {"line": 68, "column": 19}, "end": {"line": 68, "column": 30}}, "27": {"start": {"line": 69, "column": 23}, "end": {"line": 69, "column": 31}}, "28": {"start": {"line": 72, "column": 27}, "end": {"line": 72, "column": 90}}, "29": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 52}}, "30": {"start": {"line": 76, "column": 4}, "end": {"line": 78, "column": 5}}, "31": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 57}}, "32": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 55}}, "33": {"start": {"line": 88, "column": 26}, "end": {"line": 108, "column": 1}}, "34": {"start": {"line": 89, "column": 2}, "end": {"line": 107, "column": 3}}, "35": {"start": {"line": 90, "column": 19}, "end": {"line": 90, "column": 29}}, "36": {"start": {"line": 91, "column": 19}, "end": {"line": 91, "column": 30}}, "37": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 60}}, "38": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 7}}, "39": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 48}}, "40": {"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, "41": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 57}}, "42": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 59}}, "43": {"start": {"line": 115, "column": 23}, "end": {"line": 139, "column": 1}}, "44": {"start": {"line": 116, "column": 2}, "end": {"line": 138, "column": 3}}, "45": {"start": {"line": 117, "column": 19}, "end": {"line": 117, "column": 29}}, "46": {"start": {"line": 118, "column": 19}, "end": {"line": 118, "column": 30}}, "47": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 57}}, "48": {"start": {"line": 123, "column": 4}, "end": {"line": 125, "column": 7}}, "49": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 47}}, "50": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, "51": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 57}}, "52": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, "53": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 58}}, "54": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 56}}, "55": {"start": {"line": 146, "column": 27}, "end": {"line": 162, "column": 1}}, "56": {"start": {"line": 147, "column": 2}, "end": {"line": 161, "column": 3}}, "57": {"start": {"line": 148, "column": 23}, "end": {"line": 148, "column": 33}}, "58": {"start": {"line": 149, "column": 40}, "end": {"line": 149, "column": 49}}, "59": {"start": {"line": 150, "column": 19}, "end": {"line": 150, "column": 30}}, "60": {"start": {"line": 153, "column": 19}, "end": {"line": 153, "column": 95}}, "61": {"start": {"line": 155, "column": 4}, "end": {"line": 155, "column": 44}}, "62": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, "63": {"start": {"line": 158, "column": 6}, "end": {"line": 158, "column": 57}}, "64": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 60}}, "65": {"start": {"line": 164, "column": 0}, "end": {"line": 171, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 25}}, "loc": {"start": {"line": 21, "column": 44}, "end": {"line": 36, "column": 1}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 43, "column": 23}, "end": {"line": 43, "column": 24}}, "loc": {"start": {"line": 43, "column": 43}, "end": {"line": 58, "column": 1}}, "line": 43}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 65, "column": 22}, "end": {"line": 65, "column": 23}}, "loc": {"start": {"line": 65, "column": 42}, "end": {"line": 81, "column": 1}}, "line": 65}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 88, "column": 26}, "end": {"line": 88, "column": 27}}, "loc": {"start": {"line": 88, "column": 46}, "end": {"line": 108, "column": 1}}, "line": 88}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 115, "column": 23}, "end": {"line": 115, "column": 24}}, "loc": {"start": {"line": 115, "column": 43}, "end": {"line": 139, "column": 1}}, "line": 115}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 146, "column": 27}, "end": {"line": 146, "column": 28}}, "loc": {"start": {"line": 146, "column": 47}, "end": {"line": 162, "column": 1}}, "line": 146}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": 5}}, "type": "if", "locations": [{"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": 5}}, {"start": {}, "end": {}}], "line": 31}, "1": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 55, "column": 5}}, "type": "if", "locations": [{"start": {"line": 53, "column": 4}, "end": {"line": 55, "column": 5}}, {"start": {}, "end": {}}], "line": 53}, "2": {"loc": {"start": {"line": 76, "column": 4}, "end": {"line": 78, "column": 5}}, "type": "if", "locations": [{"start": {"line": 76, "column": 4}, "end": {"line": 78, "column": 5}}, {"start": {}, "end": {}}], "line": 76}, "3": {"loc": {"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, "type": "if", "locations": [{"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, {"start": {}, "end": {}}], "line": 102}, "4": {"loc": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, "type": "if", "locations": [{"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, {"start": {}, "end": {}}], "line": 129}, "5": {"loc": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, "type": "if", "locations": [{"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, {"start": {}, "end": {}}], "line": 133}, "6": {"loc": {"start": {"line": 149, "column": 12}, "end": {"line": 149, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 149, "column": 19}, "end": {"line": 149, "column": 20}}], "line": 149}, "7": {"loc": {"start": {"line": 149, "column": 22}, "end": {"line": 149, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 149, "column": 33}, "end": {"line": 149, "column": 35}}], "line": 149}, "8": {"loc": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, "type": "if", "locations": [{"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, {"start": {}, "end": {}}], "line": 157}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0], "7": [0], "8": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/deadLetterQueue.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/deadLetterQueue.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 60}}, "1": {"start": {"line": 6, "column": 18}, "end": {"line": 6, "column": 67}}, "2": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 42}}, "3": {"start": {"line": 14, "column": 0}, "end": {"line": 50, "column": 2}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 49, "column": 3}}, "5": {"start": {"line": 16, "column": 56}, "end": {"line": 16, "column": 65}}, "6": {"start": {"line": 17, "column": 19}, "end": {"line": 17, "column": 37}}, "7": {"start": {"line": 20, "column": 18}, "end": {"line": 20, "column": 20}}, "8": {"start": {"line": 21, "column": 4}, "end": {"line": 23, "column": 5}}, "9": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 28}}, "10": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}, "11": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 34}}, "12": {"start": {"line": 29, "column": 28}, "end": {"line": 34, "column": 6}}, "13": {"start": {"line": 37, "column": 4}, "end": {"line": 43, "column": 7}}, "14": {"start": {"line": 45, "column": 4}, "end": {"line": 47, "column": 7}}, "15": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 72}}, "16": {"start": {"line": 57, "column": 0}, "end": {"line": 77, "column": 2}}, "17": {"start": {"line": 58, "column": 2}, "end": {"line": 76, "column": 3}}, "18": {"start": {"line": 59, "column": 19}, "end": {"line": 59, "column": 29}}, "19": {"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 51}}, "20": {"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": 5}}, "21": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 59}}, "22": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 19}}, "23": {"start": {"line": 71, "column": 4}, "end": {"line": 74, "column": 7}}, "24": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 75}}, "25": {"start": {"line": 84, "column": 0}, "end": {"line": 115, "column": 2}}, "26": {"start": {"line": 85, "column": 2}, "end": {"line": 114, "column": 3}}, "27": {"start": {"line": 86, "column": 19}, "end": {"line": 86, "column": 29}}, "28": {"start": {"line": 89, "column": 17}, "end": {"line": 89, "column": 51}}, "29": {"start": {"line": 91, "column": 4}, "end": {"line": 93, "column": 5}}, "30": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 59}}, "31": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 5}}, "32": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 61}}, "33": {"start": {"line": 101, "column": 35}, "end": {"line": 101, "column": 74}}, "34": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 50}}, "35": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 76}}, "36": {"start": {"line": 109, "column": 4}, "end": {"line": 112, "column": 7}}, "37": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 73}}, "38": {"start": {"line": 122, "column": 0}, "end": {"line": 153, "column": 2}}, "39": {"start": {"line": 123, "column": 2}, "end": {"line": 152, "column": 3}}, "40": {"start": {"line": 124, "column": 19}, "end": {"line": 124, "column": 29}}, "41": {"start": {"line": 127, "column": 17}, "end": {"line": 127, "column": 51}}, "42": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, "43": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 59}}, "44": {"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}, "45": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 56}}, "46": {"start": {"line": 139, "column": 4}, "end": {"line": 142, "column": 7}}, "47": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 40}}, "48": {"start": {"line": 147, "column": 4}, "end": {"line": 150, "column": 7}}, "49": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 73}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 29}, "end": {"line": 14, "column": 30}}, "loc": {"start": {"line": 14, "column": 49}, "end": {"line": 50, "column": 1}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 57, "column": 33}, "end": {"line": 57, "column": 34}}, "loc": {"start": {"line": 57, "column": 53}, "end": {"line": 77, "column": 1}}, "line": 57}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 84, "column": 35}, "end": {"line": 84, "column": 36}}, "loc": {"start": {"line": 84, "column": 55}, "end": {"line": 115, "column": 1}}, "line": 84}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 122, "column": 37}, "end": {"line": 122, "column": 38}}, "loc": {"start": {"line": 122, "column": 57}, "end": {"line": 153, "column": 1}}, "line": 122}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 20}, "end": {"line": 16, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 16, "column": 27}, "end": {"line": 16, "column": 28}}], "line": 16}, "1": {"loc": {"start": {"line": 16, "column": 30}, "end": {"line": 16, "column": 40}}, "type": "default-arg", "locations": [{"start": {"line": 16, "column": 38}, "end": {"line": 16, "column": 40}}], "line": 16}, "2": {"loc": {"start": {"line": 21, "column": 4}, "end": {"line": 23, "column": 5}}, "type": "if", "locations": [{"start": {"line": 21, "column": 4}, "end": {"line": 23, "column": 5}}, {"start": {}, "end": {}}], "line": 21}, "3": {"loc": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}, "type": "if", "locations": [{"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}, {"start": {}, "end": {}}], "line": 24}, "4": {"loc": {"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": 5}}, "type": "if", "locations": [{"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": 5}}, {"start": {}, "end": {}}], "line": 64}, "5": {"loc": {"start": {"line": 91, "column": 4}, "end": {"line": 93, "column": 5}}, "type": "if", "locations": [{"start": {"line": 91, "column": 4}, "end": {"line": 93, "column": 5}}, {"start": {}, "end": {}}], "line": 91}, "6": {"loc": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 5}}, "type": "if", "locations": [{"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 5}}, {"start": {}, "end": {}}], "line": 96}, "7": {"loc": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, "type": "if", "locations": [{"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, {"start": {}, "end": {}}], "line": 129}, "8": {"loc": {"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}, "type": "if", "locations": [{"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}, {"start": {}, "end": {}}], "line": 134}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/errorMonitor.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/errorMonitor.controller.js", "statementMap": {"0": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 36}}, "1": {"start": {"line": 11, "column": 20}, "end": {"line": 11, "column": 51}}, "2": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 42}}, "3": {"start": {"line": 13, "column": 50}, "end": {"line": 13, "column": 82}}, "4": {"start": {"line": 20, "column": 27}, "end": {"line": 27, "column": 1}}, "5": {"start": {"line": 21, "column": 2}, "end": {"line": 26, "column": 3}}, "6": {"start": {"line": 22, "column": 18}, "end": {"line": 22, "column": 33}}, "7": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 43}}, "8": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 60}}, "9": {"start": {"line": 34, "column": 29}, "end": {"line": 41, "column": 1}}, "10": {"start": {"line": 35, "column": 2}, "end": {"line": 40, "column": 3}}, "11": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 22}}, "12": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 60}}, "13": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 62}}, "14": {"start": {"line": 48, "column": 26}, "end": {"line": 55, "column": 1}}, "15": {"start": {"line": 49, "column": 2}, "end": {"line": 54, "column": 3}}, "16": {"start": {"line": 50, "column": 18}, "end": {"line": 50, "column": 45}}, "17": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 47}}, "18": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 59}}, "19": {"start": {"line": 62, "column": 21}, "end": {"line": 76, "column": 1}}, "20": {"start": {"line": 63, "column": 2}, "end": {"line": 75, "column": 3}}, "21": {"start": {"line": 64, "column": 21}, "end": {"line": 64, "column": 31}}, "22": {"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": 5}}, "23": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 64}}, "24": {"start": {"line": 71, "column": 17}, "end": {"line": 71, "column": 41}}, "25": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 46}}, "26": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 54}}, "27": {"start": {"line": 78, "column": 0}, "end": {"line": 83, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 20, "column": 27}, "end": {"line": 20, "column": 28}}, "loc": {"start": {"line": 20, "column": 47}, "end": {"line": 27, "column": 1}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 34, "column": 29}, "end": {"line": 34, "column": 30}}, "loc": {"start": {"line": 34, "column": 49}, "end": {"line": 41, "column": 1}}, "line": 34}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 48, "column": 26}, "end": {"line": 48, "column": 27}}, "loc": {"start": {"line": 48, "column": 46}, "end": {"line": 55, "column": 1}}, "line": 48}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 62, "column": 21}, "end": {"line": 62, "column": 22}}, "loc": {"start": {"line": 62, "column": 41}, "end": {"line": 76, "column": 1}}, "line": 62}}, "branchMap": {"0": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": 5}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": 5}}, {"start": {}, "end": {}}], "line": 67}, "1": {"loc": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 13}}, {"start": {"line": 67, "column": 17}, "end": {"line": 67, "column": 50}}], "line": 67}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/exerciseV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/exerciseV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 24}, "end": {"line": 7, "column": 63}}, "3": {"start": {"line": 14, "column": 28}, "end": {"line": 53, "column": 1}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 52, "column": 3}}, "5": {"start": {"line": 16, "column": 19}, "end": {"line": 16, "column": 34}}, "6": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 32}}, "7": {"start": {"line": 18, "column": 52}, "end": {"line": 18, "column": 61}}, "8": {"start": {"line": 21, "column": 20}, "end": {"line": 21, "column": 22}}, "9": {"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, "10": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 38}}, "11": {"start": {"line": 27, "column": 19}, "end": {"line": 27, "column": 100}}, "12": {"start": {"line": 30, "column": 22}, "end": {"line": 38, "column": 7}}, "13": {"start": {"line": 30, "column": 51}, "end": {"line": 38, "column": 5}}, "14": {"start": {"line": 40, "column": 4}, "end": {"line": 48, "column": 7}}, "15": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 47}}, "16": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 67}}, "17": {"start": {"line": 60, "column": 24}, "end": {"line": 86, "column": 1}}, "18": {"start": {"line": 61, "column": 2}, "end": {"line": 85, "column": 3}}, "19": {"start": {"line": 62, "column": 19}, "end": {"line": 62, "column": 34}}, "20": {"start": {"line": 63, "column": 19}, "end": {"line": 63, "column": 29}}, "21": {"start": {"line": 66, "column": 21}, "end": {"line": 66, "column": 73}}, "22": {"start": {"line": 68, "column": 4}, "end": {"line": 81, "column": 7}}, "23": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 47}}, "24": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 67}}, "25": {"start": {"line": 93, "column": 23}, "end": {"line": 124, "column": 1}}, "26": {"start": {"line": 94, "column": 2}, "end": {"line": 123, "column": 3}}, "27": {"start": {"line": 95, "column": 19}, "end": {"line": 95, "column": 34}}, "28": {"start": {"line": 96, "column": 84}, "end": {"line": 96, "column": 92}}, "29": {"start": {"line": 99, "column": 25}, "end": {"line": 106, "column": 5}}, "30": {"start": {"line": 108, "column": 21}, "end": {"line": 108, "column": 79}}, "31": {"start": {"line": 110, "column": 4}, "end": {"line": 119, "column": 7}}, "32": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 45}}, "33": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 65}}, "34": {"start": {"line": 131, "column": 23}, "end": {"line": 162, "column": 1}}, "35": {"start": {"line": 132, "column": 2}, "end": {"line": 161, "column": 3}}, "36": {"start": {"line": 133, "column": 19}, "end": {"line": 133, "column": 34}}, "37": {"start": {"line": 134, "column": 19}, "end": {"line": 134, "column": 29}}, "38": {"start": {"line": 135, "column": 77}, "end": {"line": 135, "column": 85}}, "39": {"start": {"line": 138, "column": 23}, "end": {"line": 144, "column": 5}}, "40": {"start": {"line": 146, "column": 21}, "end": {"line": 146, "column": 81}}, "41": {"start": {"line": 148, "column": 4}, "end": {"line": 157, "column": 16}}, "42": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 45}}, "43": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 65}}, "44": {"start": {"line": 169, "column": 23}, "end": {"line": 186, "column": 1}}, "45": {"start": {"line": 170, "column": 2}, "end": {"line": 185, "column": 3}}, "46": {"start": {"line": 171, "column": 19}, "end": {"line": 171, "column": 34}}, "47": {"start": {"line": 172, "column": 19}, "end": {"line": 172, "column": 29}}, "48": {"start": {"line": 175, "column": 20}, "end": {"line": 175, "column": 68}}, "49": {"start": {"line": 177, "column": 4}, "end": {"line": 179, "column": 5}}, "50": {"start": {"line": 178, "column": 6}, "end": {"line": 178, "column": 53}}, "51": {"start": {"line": 181, "column": 4}, "end": {"line": 181, "column": 51}}, "52": {"start": {"line": 183, "column": 4}, "end": {"line": 183, "column": 45}}, "53": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 65}}, "54": {"start": {"line": 228, "column": 27}, "end": {"line": 248, "column": 1}}, "55": {"start": {"line": 229, "column": 2}, "end": {"line": 247, "column": 3}}, "56": {"start": {"line": 230, "column": 19}, "end": {"line": 230, "column": 34}}, "57": {"start": {"line": 231, "column": 19}, "end": {"line": 231, "column": 29}}, "58": {"start": {"line": 234, "column": 4}, "end": {"line": 234, "column": 57}}, "59": {"start": {"line": 236, "column": 4}, "end": {"line": 238, "column": 7}}, "60": {"start": {"line": 240, "column": 4}, "end": {"line": 240, "column": 46}}, "61": {"start": {"line": 242, "column": 4}, "end": {"line": 244, "column": 5}}, "62": {"start": {"line": 243, "column": 6}, "end": {"line": 243, "column": 56}}, "63": {"start": {"line": 246, "column": 4}, "end": {"line": 246, "column": 66}}, "64": {"start": {"line": 290, "column": 24}, "end": {"line": 314, "column": 1}}, "65": {"start": {"line": 291, "column": 2}, "end": {"line": 313, "column": 3}}, "66": {"start": {"line": 292, "column": 19}, "end": {"line": 292, "column": 34}}, "67": {"start": {"line": 293, "column": 19}, "end": {"line": 293, "column": 29}}, "68": {"start": {"line": 296, "column": 4}, "end": {"line": 296, "column": 54}}, "69": {"start": {"line": 298, "column": 4}, "end": {"line": 300, "column": 7}}, "70": {"start": {"line": 302, "column": 4}, "end": {"line": 302, "column": 45}}, "71": {"start": {"line": 304, "column": 4}, "end": {"line": 306, "column": 5}}, "72": {"start": {"line": 305, "column": 6}, "end": {"line": 305, "column": 56}}, "73": {"start": {"line": 308, "column": 4}, "end": {"line": 310, "column": 5}}, "74": {"start": {"line": 309, "column": 6}, "end": {"line": 309, "column": 56}}, "75": {"start": {"line": 312, "column": 4}, "end": {"line": 312, "column": 65}}, "76": {"start": {"line": 391, "column": 28}, "end": {"line": 430, "column": 1}}, "77": {"start": {"line": 392, "column": 2}, "end": {"line": 429, "column": 3}}, "78": {"start": {"line": 393, "column": 19}, "end": {"line": 393, "column": 34}}, "79": {"start": {"line": 394, "column": 22}, "end": {"line": 394, "column": 32}}, "80": {"start": {"line": 395, "column": 40}, "end": {"line": 395, "column": 49}}, "81": {"start": {"line": 398, "column": 19}, "end": {"line": 398, "column": 91}}, "82": {"start": {"line": 401, "column": 22}, "end": {"line": 410, "column": 7}}, "83": {"start": {"line": 401, "column": 51}, "end": {"line": 410, "column": 5}}, "84": {"start": {"line": 412, "column": 4}, "end": {"line": 420, "column": 7}}, "85": {"start": {"line": 422, "column": 4}, "end": {"line": 422, "column": 51}}, "86": {"start": {"line": 424, "column": 4}, "end": {"line": 426, "column": 5}}, "87": {"start": {"line": 425, "column": 6}, "end": {"line": 425, "column": 56}}, "88": {"start": {"line": 428, "column": 4}, "end": {"line": 428, "column": 71}}, "89": {"start": {"line": 432, "column": 0}, "end": {"line": 441, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 29}}, "loc": {"start": {"line": 14, "column": 48}, "end": {"line": 53, "column": 1}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 30, "column": 38}, "end": {"line": 30, "column": 39}}, "loc": {"start": {"line": 30, "column": 51}, "end": {"line": 38, "column": 5}}, "line": 30}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 60, "column": 24}, "end": {"line": 60, "column": 25}}, "loc": {"start": {"line": 60, "column": 44}, "end": {"line": 86, "column": 1}}, "line": 60}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 93, "column": 23}, "end": {"line": 93, "column": 24}}, "loc": {"start": {"line": 93, "column": 43}, "end": {"line": 124, "column": 1}}, "line": 93}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 131, "column": 23}, "end": {"line": 131, "column": 24}}, "loc": {"start": {"line": 131, "column": 43}, "end": {"line": 162, "column": 1}}, "line": 131}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 169, "column": 23}, "end": {"line": 169, "column": 24}}, "loc": {"start": {"line": 169, "column": 43}, "end": {"line": 186, "column": 1}}, "line": 169}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 228, "column": 27}, "end": {"line": 228, "column": 28}}, "loc": {"start": {"line": 228, "column": 47}, "end": {"line": 248, "column": 1}}, "line": 228}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 290, "column": 24}, "end": {"line": 290, "column": 25}}, "loc": {"start": {"line": 290, "column": 44}, "end": {"line": 314, "column": 1}}, "line": 290}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 391, "column": 28}, "end": {"line": 391, "column": 29}}, "loc": {"start": {"line": 391, "column": 48}, "end": {"line": 430, "column": 1}}, "line": 391}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 401, "column": 38}, "end": {"line": 401, "column": 39}}, "loc": {"start": {"line": 401, "column": 51}, "end": {"line": 410, "column": 5}}, "line": 401}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 24}, "end": {"line": 18, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 31}, "end": {"line": 18, "column": 32}}], "line": 18}, "1": {"loc": {"start": {"line": 18, "column": 34}, "end": {"line": 18, "column": 47}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 45}, "end": {"line": 18, "column": 47}}], "line": 18}, "2": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, {"start": {}, "end": {}}], "line": 22}, "3": {"loc": {"start": {"line": 76, "column": 15}, "end": {"line": 76, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 76, "column": 30}, "end": {"line": 76, "column": 47}}, {"start": {"line": 76, "column": 50}, "end": {"line": 76, "column": 54}}], "line": 76}, "4": {"loc": {"start": {"line": 77, "column": 14}, "end": {"line": 77, "column": 93}}, "type": "cond-expr", "locations": [{"start": {"line": 77, "column": 58}, "end": {"line": 77, "column": 86}}, {"start": {"line": 77, "column": 89}, "end": {"line": 77, "column": 93}}], "line": 77}, "5": {"loc": {"start": {"line": 77, "column": 14}, "end": {"line": 77, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 14}, "end": {"line": 77, "column": 26}}, {"start": {"line": 77, "column": 30}, "end": {"line": 77, "column": 55}}], "line": 77}, "6": {"loc": {"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 99}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 61}, "end": {"line": 78, "column": 92}}, {"start": {"line": 78, "column": 95}, "end": {"line": 78, "column": 99}}], "line": 78}, "7": {"loc": {"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 29}}, {"start": {"line": 78, "column": 33}, "end": {"line": 78, "column": 58}}], "line": 78}, "8": {"loc": {"start": {"line": 177, "column": 4}, "end": {"line": 179, "column": 5}}, "type": "if", "locations": [{"start": {"line": 177, "column": 4}, "end": {"line": 179, "column": 5}}, {"start": {}, "end": {}}], "line": 177}, "9": {"loc": {"start": {"line": 242, "column": 4}, "end": {"line": 244, "column": 5}}, "type": "if", "locations": [{"start": {"line": 242, "column": 4}, "end": {"line": 244, "column": 5}}, {"start": {}, "end": {}}], "line": 242}, "10": {"loc": {"start": {"line": 304, "column": 4}, "end": {"line": 306, "column": 5}}, "type": "if", "locations": [{"start": {"line": 304, "column": 4}, "end": {"line": 306, "column": 5}}, {"start": {}, "end": {}}], "line": 304}, "11": {"loc": {"start": {"line": 308, "column": 4}, "end": {"line": 310, "column": 5}}, "type": "if", "locations": [{"start": {"line": 308, "column": 4}, "end": {"line": 310, "column": 5}}, {"start": {}, "end": {}}], "line": 308}, "12": {"loc": {"start": {"line": 395, "column": 12}, "end": {"line": 395, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 395, "column": 19}, "end": {"line": 395, "column": 20}}], "line": 395}, "13": {"loc": {"start": {"line": 395, "column": 22}, "end": {"line": 395, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 395, "column": 33}, "end": {"line": 395, "column": 35}}], "line": 395}, "14": {"loc": {"start": {"line": 424, "column": 4}, "end": {"line": 426, "column": 5}}, "type": "if", "locations": [{"start": {"line": 424, "column": 4}, "end": {"line": 426, "column": 5}}, {"start": {}, "end": {}}], "line": 424}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0], "14": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/health.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/health.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 50}}, "2": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 51}}, "3": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 42}}, "4": {"start": {"line": 9, "column": 11}, "end": {"line": 9, "column": 24}}, "5": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 59}}, "6": {"start": {"line": 18, "column": 25}, "end": {"line": 28, "column": 1}}, "7": {"start": {"line": 19, "column": 2}, "end": {"line": 27, "column": 3}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 24, "column": 7}}, "9": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 58}}, "10": {"start": {"line": 36, "column": 28}, "end": {"line": 70, "column": 1}}, "11": {"start": {"line": 37, "column": 2}, "end": {"line": 69, "column": 3}}, "12": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 35}}, "13": {"start": {"line": 42, "column": 19}, "end": {"line": 52, "column": 5}}, "14": {"start": {"line": 54, "column": 4}, "end": {"line": 59, "column": 7}}, "15": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 48}}, "16": {"start": {"line": 63, "column": 4}, "end": {"line": 68, "column": 12}}, "17": {"start": {"line": 78, "column": 25}, "end": {"line": 130, "column": 1}}, "18": {"start": {"line": 79, "column": 2}, "end": {"line": 129, "column": 3}}, "19": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "20": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 37}}, "21": {"start": {"line": 86, "column": 23}, "end": {"line": 86, "column": 47}}, "22": {"start": {"line": 89, "column": 17}, "end": {"line": 89, "column": 41}}, "23": {"start": {"line": 90, "column": 22}, "end": {"line": 90, "column": 40}}, "24": {"start": {"line": 91, "column": 22}, "end": {"line": 91, "column": 24}}, "25": {"start": {"line": 94, "column": 4}, "end": {"line": 101, "column": 7}}, "26": {"start": {"line": 95, "column": 6}, "end": {"line": 100, "column": 7}}, "27": {"start": {"line": 96, "column": 22}, "end": {"line": 96, "column": 37}}, "28": {"start": {"line": 97, "column": 8}, "end": {"line": 99, "column": 9}}, "29": {"start": {"line": 98, "column": 10}, "end": {"line": 98, "column": 41}}, "30": {"start": {"line": 103, "column": 4}, "end": {"line": 119, "column": 7}}, "31": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 50}}, "32": {"start": {"line": 123, "column": 4}, "end": {"line": 128, "column": 12}}, "33": {"start": {"line": 138, "column": 26}, "end": {"line": 203, "column": 1}}, "34": {"start": {"line": 139, "column": 2}, "end": {"line": 202, "column": 3}}, "35": {"start": {"line": 141, "column": 24}, "end": {"line": 141, "column": 37}}, "36": {"start": {"line": 142, "column": 23}, "end": {"line": 142, "column": 35}}, "37": {"start": {"line": 143, "column": 23}, "end": {"line": 143, "column": 47}}, "38": {"start": {"line": 144, "column": 24}, "end": {"line": 144, "column": 56}}, "39": {"start": {"line": 147, "column": 17}, "end": {"line": 147, "column": 26}}, "40": {"start": {"line": 148, "column": 21}, "end": {"line": 148, "column": 32}}, "41": {"start": {"line": 149, "column": 21}, "end": {"line": 149, "column": 34}}, "42": {"start": {"line": 150, "column": 21}, "end": {"line": 150, "column": 34}}, "43": {"start": {"line": 153, "column": 20}, "end": {"line": 153, "column": 32}}, "44": {"start": {"line": 156, "column": 26}, "end": {"line": 156, "column": 47}}, "45": {"start": {"line": 157, "column": 26}, "end": {"line": 157, "column": 42}}, "46": {"start": {"line": 159, "column": 4}, "end": {"line": 192, "column": 7}}, "47": {"start": {"line": 194, "column": 4}, "end": {"line": 194, "column": 47}}, "48": {"start": {"line": 196, "column": 4}, "end": {"line": 201, "column": 12}}, "49": {"start": {"line": 211, "column": 24}, "end": {"line": 296, "column": 1}}, "50": {"start": {"line": 212, "column": 2}, "end": {"line": 295, "column": 3}}, "51": {"start": {"line": 213, "column": 20}, "end": {"line": 217, "column": 5}}, "52": {"start": {"line": 220, "column": 4}, "end": {"line": 222, "column": 6}}, "53": {"start": {"line": 225, "column": 4}, "end": {"line": 237, "column": 5}}, "54": {"start": {"line": 226, "column": 6}, "end": {"line": 226, "column": 37}}, "55": {"start": {"line": 227, "column": 6}, "end": {"line": 230, "column": 8}}, "56": {"start": {"line": 232, "column": 6}, "end": {"line": 235, "column": 8}}, "57": {"start": {"line": 236, "column": 6}, "end": {"line": 236, "column": 34}}, "58": {"start": {"line": 240, "column": 4}, "end": {"line": 256, "column": 5}}, "59": {"start": {"line": 241, "column": 6}, "end": {"line": 243, "column": 7}}, "60": {"start": {"line": 242, "column": 8}, "end": {"line": 242, "column": 39}}, "61": {"start": {"line": 245, "column": 25}, "end": {"line": 245, "column": 49}}, "62": {"start": {"line": 246, "column": 6}, "end": {"line": 249, "column": 8}}, "63": {"start": {"line": 251, "column": 6}, "end": {"line": 254, "column": 8}}, "64": {"start": {"line": 255, "column": 6}, "end": {"line": 255, "column": 34}}, "65": {"start": {"line": 259, "column": 24}, "end": {"line": 259, "column": 37}}, "66": {"start": {"line": 260, "column": 23}, "end": {"line": 260, "column": 35}}, "67": {"start": {"line": 261, "column": 24}, "end": {"line": 261, "column": 72}}, "68": {"start": {"line": 262, "column": 20}, "end": {"line": 262, "column": 35}}, "69": {"start": {"line": 263, "column": 21}, "end": {"line": 263, "column": 37}}, "70": {"start": {"line": 265, "column": 4}, "end": {"line": 274, "column": 6}}, "71": {"start": {"line": 277, "column": 4}, "end": {"line": 280, "column": 5}}, "72": {"start": {"line": 278, "column": 6}, "end": {"line": 278, "column": 49}}, "73": {"start": {"line": 279, "column": 6}, "end": {"line": 279, "column": 34}}, "74": {"start": {"line": 283, "column": 23}, "end": {"line": 284, "column": 64}}, "75": {"start": {"line": 286, "column": 4}, "end": {"line": 286, "column": 56}}, "76": {"start": {"line": 288, "column": 4}, "end": {"line": 288, "column": 47}}, "77": {"start": {"line": 290, "column": 4}, "end": {"line": 294, "column": 12}}, "78": {"start": {"line": 304, "column": 2}, "end": {"line": 304, "column": 36}}, "79": {"start": {"line": 304, "column": 19}, "end": {"line": 304, "column": 36}}, "80": {"start": {"line": 306, "column": 12}, "end": {"line": 306, "column": 16}}, "81": {"start": {"line": 307, "column": 16}, "end": {"line": 307, "column": 49}}, "82": {"start": {"line": 308, "column": 12}, "end": {"line": 308, "column": 53}}, "83": {"start": {"line": 310, "column": 2}, "end": {"line": 310, "column": 74}}, "84": {"start": {"line": 313, "column": 0}, "end": {"line": 319, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 25}, "end": {"line": 18, "column": 26}}, "loc": {"start": {"line": 18, "column": 45}, "end": {"line": 28, "column": 1}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 28}, "end": {"line": 36, "column": 29}}, "loc": {"start": {"line": 36, "column": 48}, "end": {"line": 70, "column": 1}}, "line": 36}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 78, "column": 25}, "end": {"line": 78, "column": 26}}, "loc": {"start": {"line": 78, "column": 45}, "end": {"line": 130, "column": 1}}, "line": 78}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 94, "column": 22}, "end": {"line": 94, "column": 23}}, "loc": {"start": {"line": 94, "column": 30}, "end": {"line": 101, "column": 5}}, "line": 94}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 138, "column": 26}, "end": {"line": 138, "column": 27}}, "loc": {"start": {"line": 138, "column": 46}, "end": {"line": 203, "column": 1}}, "line": 138}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 211, "column": 24}, "end": {"line": 211, "column": 25}}, "loc": {"start": {"line": 211, "column": 44}, "end": {"line": 296, "column": 1}}, "line": 211}, "6": {"name": "formatBytes", "decl": {"start": {"line": 303, "column": 9}, "end": {"line": 303, "column": 20}}, "loc": {"start": {"line": 303, "column": 28}, "end": {"line": 311, "column": 1}}, "line": 303}}, "branchMap": {"0": {"loc": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "type": "if", "locations": [{"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, {"start": {}, "end": {}}], "line": 81}, "1": {"loc": {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 20}}, {"start": {"line": 81, "column": 24}, "end": {"line": 81, "column": 43}}], "line": 81}, "2": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 100, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 100, "column": 7}}, {"start": {}, "end": {}}], "line": 95}, "3": {"loc": {"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": 14}}, {"start": {"line": 95, "column": 18}, "end": {"line": 95, "column": 39}}], "line": 95}, "4": {"loc": {"start": {"line": 97, "column": 8}, "end": {"line": 99, "column": 9}}, "type": "if", "locations": [{"start": {"line": 97, "column": 8}, "end": {"line": 99, "column": 9}}, {"start": {}, "end": {}}], "line": 97}, "5": {"loc": {"start": {"line": 241, "column": 6}, "end": {"line": 243, "column": 7}}, "type": "if", "locations": [{"start": {"line": 241, "column": 6}, "end": {"line": 243, "column": 7}}, {"start": {}, "end": {}}], "line": 241}, "6": {"loc": {"start": {"line": 241, "column": 10}, "end": {"line": 241, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 241, "column": 10}, "end": {"line": 241, "column": 22}}, {"start": {"line": 241, "column": 26}, "end": {"line": 241, "column": 45}}], "line": 241}, "7": {"loc": {"start": {"line": 277, "column": 4}, "end": {"line": 280, "column": 5}}, "type": "if", "locations": [{"start": {"line": 277, "column": 4}, "end": {"line": 280, "column": 5}}, {"start": {}, "end": {}}], "line": 277}, "8": {"loc": {"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 24}}, {"start": {"line": 277, "column": 28}, "end": {"line": 277, "column": 52}}], "line": 277}, "9": {"loc": {"start": {"line": 283, "column": 23}, "end": {"line": 284, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 283, "column": 49}, "end": {"line": 283, "column": 52}}, {"start": {"line": 284, "column": 23}, "end": {"line": 284, "column": 64}}], "line": 283}, "10": {"loc": {"start": {"line": 284, "column": 23}, "end": {"line": 284, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 284, "column": 55}, "end": {"line": 284, "column": 58}}, {"start": {"line": 284, "column": 61}, "end": {"line": 284, "column": 64}}], "line": 284}, "11": {"loc": {"start": {"line": 304, "column": 2}, "end": {"line": 304, "column": 36}}, "type": "if", "locations": [{"start": {"line": 304, "column": 2}, "end": {"line": 304, "column": 36}}, {"start": {}, "end": {}}], "line": 304}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/insightV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/insightV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 61}}, "3": {"start": {"line": 14, "column": 27}, "end": {"line": 45, "column": 1}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 44, "column": 3}}, "5": {"start": {"line": 16, "column": 19}, "end": {"line": 16, "column": 34}}, "6": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 32}}, "7": {"start": {"line": 18, "column": 40}, "end": {"line": 18, "column": 49}}, "8": {"start": {"line": 21, "column": 19}, "end": {"line": 21, "column": 89}}, "9": {"start": {"line": 24, "column": 21}, "end": {"line": 30, "column": 7}}, "10": {"start": {"line": 24, "column": 49}, "end": {"line": 30, "column": 5}}, "11": {"start": {"line": 32, "column": 4}, "end": {"line": 40, "column": 7}}, "12": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 47}}, "13": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 67}}, "14": {"start": {"line": 52, "column": 23}, "end": {"line": 75, "column": 1}}, "15": {"start": {"line": 53, "column": 2}, "end": {"line": 74, "column": 3}}, "16": {"start": {"line": 54, "column": 19}, "end": {"line": 54, "column": 34}}, "17": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 29}}, "18": {"start": {"line": 58, "column": 20}, "end": {"line": 58, "column": 70}}, "19": {"start": {"line": 60, "column": 4}, "end": {"line": 70, "column": 7}}, "20": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 47}}, "21": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 67}}, "22": {"start": {"line": 82, "column": 22}, "end": {"line": 109, "column": 1}}, "23": {"start": {"line": 83, "column": 2}, "end": {"line": 108, "column": 3}}, "24": {"start": {"line": 84, "column": 19}, "end": {"line": 84, "column": 34}}, "25": {"start": {"line": 85, "column": 51}, "end": {"line": 85, "column": 59}}, "26": {"start": {"line": 88, "column": 24}, "end": {"line": 93, "column": 5}}, "27": {"start": {"line": 95, "column": 20}, "end": {"line": 95, "column": 75}}, "28": {"start": {"line": 97, "column": 4}, "end": {"line": 104, "column": 7}}, "29": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 45}}, "30": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 65}}, "31": {"start": {"line": 116, "column": 22}, "end": {"line": 142, "column": 1}}, "32": {"start": {"line": 117, "column": 2}, "end": {"line": 141, "column": 3}}, "33": {"start": {"line": 118, "column": 19}, "end": {"line": 118, "column": 34}}, "34": {"start": {"line": 119, "column": 19}, "end": {"line": 119, "column": 29}}, "35": {"start": {"line": 120, "column": 44}, "end": {"line": 120, "column": 52}}, "36": {"start": {"line": 123, "column": 23}, "end": {"line": 127, "column": 5}}, "37": {"start": {"line": 129, "column": 20}, "end": {"line": 129, "column": 78}}, "38": {"start": {"line": 131, "column": 4}, "end": {"line": 137, "column": 16}}, "39": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": 45}}, "40": {"start": {"line": 140, "column": 4}, "end": {"line": 140, "column": 65}}, "41": {"start": {"line": 149, "column": 22}, "end": {"line": 166, "column": 1}}, "42": {"start": {"line": 150, "column": 2}, "end": {"line": 165, "column": 3}}, "43": {"start": {"line": 151, "column": 19}, "end": {"line": 151, "column": 34}}, "44": {"start": {"line": 152, "column": 19}, "end": {"line": 152, "column": 29}}, "45": {"start": {"line": 155, "column": 20}, "end": {"line": 155, "column": 66}}, "46": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, "47": {"start": {"line": 158, "column": 6}, "end": {"line": 158, "column": 53}}, "48": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 51}}, "49": {"start": {"line": 163, "column": 4}, "end": {"line": 163, "column": 45}}, "50": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": 65}}, "51": {"start": {"line": 208, "column": 26}, "end": {"line": 228, "column": 1}}, "52": {"start": {"line": 209, "column": 2}, "end": {"line": 227, "column": 3}}, "53": {"start": {"line": 210, "column": 19}, "end": {"line": 210, "column": 34}}, "54": {"start": {"line": 211, "column": 19}, "end": {"line": 211, "column": 29}}, "55": {"start": {"line": 214, "column": 4}, "end": {"line": 214, "column": 55}}, "56": {"start": {"line": 216, "column": 4}, "end": {"line": 218, "column": 7}}, "57": {"start": {"line": 220, "column": 4}, "end": {"line": 220, "column": 46}}, "58": {"start": {"line": 222, "column": 4}, "end": {"line": 224, "column": 5}}, "59": {"start": {"line": 223, "column": 6}, "end": {"line": 223, "column": 56}}, "60": {"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": 66}}, "61": {"start": {"line": 270, "column": 23}, "end": {"line": 294, "column": 1}}, "62": {"start": {"line": 271, "column": 2}, "end": {"line": 293, "column": 3}}, "63": {"start": {"line": 272, "column": 19}, "end": {"line": 272, "column": 34}}, "64": {"start": {"line": 273, "column": 19}, "end": {"line": 273, "column": 29}}, "65": {"start": {"line": 276, "column": 4}, "end": {"line": 276, "column": 52}}, "66": {"start": {"line": 278, "column": 4}, "end": {"line": 280, "column": 7}}, "67": {"start": {"line": 282, "column": 4}, "end": {"line": 282, "column": 45}}, "68": {"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}, "69": {"start": {"line": 285, "column": 6}, "end": {"line": 285, "column": 56}}, "70": {"start": {"line": 288, "column": 4}, "end": {"line": 290, "column": 5}}, "71": {"start": {"line": 289, "column": 6}, "end": {"line": 289, "column": 56}}, "72": {"start": {"line": 292, "column": 4}, "end": {"line": 292, "column": 65}}, "73": {"start": {"line": 371, "column": 27}, "end": {"line": 408, "column": 1}}, "74": {"start": {"line": 372, "column": 2}, "end": {"line": 407, "column": 3}}, "75": {"start": {"line": 373, "column": 19}, "end": {"line": 373, "column": 34}}, "76": {"start": {"line": 374, "column": 22}, "end": {"line": 374, "column": 32}}, "77": {"start": {"line": 375, "column": 40}, "end": {"line": 375, "column": 49}}, "78": {"start": {"line": 378, "column": 19}, "end": {"line": 378, "column": 89}}, "79": {"start": {"line": 381, "column": 21}, "end": {"line": 388, "column": 7}}, "80": {"start": {"line": 381, "column": 49}, "end": {"line": 388, "column": 5}}, "81": {"start": {"line": 390, "column": 4}, "end": {"line": 398, "column": 7}}, "82": {"start": {"line": 400, "column": 4}, "end": {"line": 400, "column": 51}}, "83": {"start": {"line": 402, "column": 4}, "end": {"line": 404, "column": 5}}, "84": {"start": {"line": 403, "column": 6}, "end": {"line": 403, "column": 56}}, "85": {"start": {"line": 406, "column": 4}, "end": {"line": 406, "column": 71}}, "86": {"start": {"line": 410, "column": 0}, "end": {"line": 419, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": 28}}, "loc": {"start": {"line": 14, "column": 47}, "end": {"line": 45, "column": 1}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 24, "column": 37}, "end": {"line": 24, "column": 38}}, "loc": {"start": {"line": 24, "column": 49}, "end": {"line": 30, "column": 5}}, "line": 24}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 52, "column": 23}, "end": {"line": 52, "column": 24}}, "loc": {"start": {"line": 52, "column": 43}, "end": {"line": 75, "column": 1}}, "line": 52}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 82, "column": 22}, "end": {"line": 82, "column": 23}}, "loc": {"start": {"line": 82, "column": 42}, "end": {"line": 109, "column": 1}}, "line": 82}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 116, "column": 22}, "end": {"line": 116, "column": 23}}, "loc": {"start": {"line": 116, "column": 42}, "end": {"line": 142, "column": 1}}, "line": 116}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 149, "column": 22}, "end": {"line": 149, "column": 23}}, "loc": {"start": {"line": 149, "column": 42}, "end": {"line": 166, "column": 1}}, "line": 149}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 208, "column": 26}, "end": {"line": 208, "column": 27}}, "loc": {"start": {"line": 208, "column": 46}, "end": {"line": 228, "column": 1}}, "line": 208}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 270, "column": 23}, "end": {"line": 270, "column": 24}}, "loc": {"start": {"line": 270, "column": 43}, "end": {"line": 294, "column": 1}}, "line": 270}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 371, "column": 27}, "end": {"line": 371, "column": 28}}, "loc": {"start": {"line": 371, "column": 47}, "end": {"line": 408, "column": 1}}, "line": 371}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 381, "column": 37}, "end": {"line": 381, "column": 38}}, "loc": {"start": {"line": 381, "column": 49}, "end": {"line": 388, "column": 5}}, "line": 381}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 20}}], "line": 18}, "1": {"loc": {"start": {"line": 18, "column": 22}, "end": {"line": 18, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 33}, "end": {"line": 18, "column": 35}}], "line": 18}, "2": {"loc": {"start": {"line": 66, "column": 15}, "end": {"line": 66, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 66, "column": 29}, "end": {"line": 66, "column": 45}}, {"start": {"line": 66, "column": 48}, "end": {"line": 66, "column": 52}}], "line": 66}, "3": {"loc": {"start": {"line": 67, "column": 14}, "end": {"line": 67, "column": 90}}, "type": "cond-expr", "locations": [{"start": {"line": 67, "column": 56}, "end": {"line": 67, "column": 83}}, {"start": {"line": 67, "column": 86}, "end": {"line": 67, "column": 90}}], "line": 67}, "4": {"loc": {"start": {"line": 67, "column": 14}, "end": {"line": 67, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 14}, "end": {"line": 67, "column": 25}}, {"start": {"line": 67, "column": 29}, "end": {"line": 67, "column": 53}}], "line": 67}, "5": {"loc": {"start": {"line": 68, "column": 17}, "end": {"line": 68, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 68, "column": 59}, "end": {"line": 68, "column": 89}}, {"start": {"line": 68, "column": 92}, "end": {"line": 68, "column": 96}}], "line": 68}, "6": {"loc": {"start": {"line": 68, "column": 17}, "end": {"line": 68, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 68, "column": 17}, "end": {"line": 68, "column": 28}}, {"start": {"line": 68, "column": 32}, "end": {"line": 68, "column": 56}}], "line": 68}, "7": {"loc": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, "type": "if", "locations": [{"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, {"start": {}, "end": {}}], "line": 157}, "8": {"loc": {"start": {"line": 222, "column": 4}, "end": {"line": 224, "column": 5}}, "type": "if", "locations": [{"start": {"line": 222, "column": 4}, "end": {"line": 224, "column": 5}}, {"start": {}, "end": {}}], "line": 222}, "9": {"loc": {"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}, "type": "if", "locations": [{"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}, {"start": {}, "end": {}}], "line": 284}, "10": {"loc": {"start": {"line": 288, "column": 4}, "end": {"line": 290, "column": 5}}, "type": "if", "locations": [{"start": {"line": 288, "column": 4}, "end": {"line": 290, "column": 5}}, {"start": {}, "end": {}}], "line": 288}, "11": {"loc": {"start": {"line": 375, "column": 12}, "end": {"line": 375, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 375, "column": 19}, "end": {"line": 375, "column": 20}}], "line": 375}, "12": {"loc": {"start": {"line": 375, "column": 22}, "end": {"line": 375, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 375, "column": 33}, "end": {"line": 375, "column": 35}}], "line": 375}, "13": {"loc": {"start": {"line": 402, "column": 4}, "end": {"line": 404, "column": 5}}, "type": "if", "locations": [{"start": {"line": 402, "column": 4}, "end": {"line": 404, "column": 5}}, {"start": {}, "end": {}}], "line": 402}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0], "12": [0], "13": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/learningPlanV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/learningPlanV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 62}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 36}}, "4": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 78}}, "5": {"start": {"line": 21, "column": 21}, "end": {"line": 40, "column": 1}}, "6": {"start": {"line": 22, "column": 2}, "end": {"line": 39, "column": 3}}, "7": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 30}}, "8": {"start": {"line": 24, "column": 90}, "end": {"line": 24, "column": 99}}, "9": {"start": {"line": 27, "column": 19}, "end": {"line": 34, "column": 5}}, "10": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 44}}, "11": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 54}}, "12": {"start": {"line": 47, "column": 20}, "end": {"line": 62, "column": 1}}, "13": {"start": {"line": 48, "column": 2}, "end": {"line": 61, "column": 3}}, "14": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 29}}, "15": {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 30}}, "16": {"start": {"line": 53, "column": 17}, "end": {"line": 53, "column": 66}}, "17": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 42}}, "18": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}, "19": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 57}}, "20": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 53}}, "21": {"start": {"line": 69, "column": 19}, "end": {"line": 81, "column": 1}}, "22": {"start": {"line": 70, "column": 2}, "end": {"line": 80, "column": 3}}, "23": {"start": {"line": 71, "column": 19}, "end": {"line": 71, "column": 30}}, "24": {"start": {"line": 72, "column": 21}, "end": {"line": 72, "column": 29}}, "25": {"start": {"line": 75, "column": 20}, "end": {"line": 75, "column": 74}}, "26": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 45}}, "27": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 52}}, "28": {"start": {"line": 88, "column": 19}, "end": {"line": 104, "column": 1}}, "29": {"start": {"line": 89, "column": 2}, "end": {"line": 103, "column": 3}}, "30": {"start": {"line": 90, "column": 19}, "end": {"line": 90, "column": 29}}, "31": {"start": {"line": 91, "column": 19}, "end": {"line": 91, "column": 30}}, "32": {"start": {"line": 92, "column": 23}, "end": {"line": 92, "column": 31}}, "33": {"start": {"line": 95, "column": 24}, "end": {"line": 95, "column": 84}}, "34": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 49}}, "35": {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, "36": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 57}}, "37": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 52}}, "38": {"start": {"line": 111, "column": 23}, "end": {"line": 131, "column": 1}}, "39": {"start": {"line": 112, "column": 2}, "end": {"line": 130, "column": 3}}, "40": {"start": {"line": 113, "column": 19}, "end": {"line": 113, "column": 29}}, "41": {"start": {"line": 114, "column": 19}, "end": {"line": 114, "column": 30}}, "42": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 57}}, "43": {"start": {"line": 119, "column": 4}, "end": {"line": 121, "column": 7}}, "44": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 48}}, "45": {"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}, "46": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 57}}, "47": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 56}}, "48": {"start": {"line": 138, "column": 20}, "end": {"line": 162, "column": 1}}, "49": {"start": {"line": 139, "column": 2}, "end": {"line": 161, "column": 3}}, "50": {"start": {"line": 140, "column": 19}, "end": {"line": 140, "column": 29}}, "51": {"start": {"line": 141, "column": 19}, "end": {"line": 141, "column": 30}}, "52": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 54}}, "53": {"start": {"line": 146, "column": 4}, "end": {"line": 148, "column": 7}}, "54": {"start": {"line": 150, "column": 4}, "end": {"line": 150, "column": 47}}, "55": {"start": {"line": 152, "column": 4}, "end": {"line": 154, "column": 5}}, "56": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 57}}, "57": {"start": {"line": 156, "column": 4}, "end": {"line": 158, "column": 5}}, "58": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": 58}}, "59": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 53}}, "60": {"start": {"line": 169, "column": 24}, "end": {"line": 181, "column": 1}}, "61": {"start": {"line": 170, "column": 2}, "end": {"line": 180, "column": 3}}, "62": {"start": {"line": 171, "column": 19}, "end": {"line": 171, "column": 30}}, "63": {"start": {"line": 172, "column": 40}, "end": {"line": 172, "column": 49}}, "64": {"start": {"line": 175, "column": 19}, "end": {"line": 175, "column": 104}}, "65": {"start": {"line": 177, "column": 4}, "end": {"line": 177, "column": 44}}, "66": {"start": {"line": 179, "column": 4}, "end": {"line": 179, "column": 57}}, "67": {"start": {"line": 183, "column": 0}, "end": {"line": 191, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 21}, "end": {"line": 21, "column": 22}}, "loc": {"start": {"line": 21, "column": 41}, "end": {"line": 40, "column": 1}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": 21}}, "loc": {"start": {"line": 47, "column": 40}, "end": {"line": 62, "column": 1}}, "line": 47}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 69, "column": 19}, "end": {"line": 69, "column": 20}}, "loc": {"start": {"line": 69, "column": 39}, "end": {"line": 81, "column": 1}}, "line": 69}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 88, "column": 19}, "end": {"line": 88, "column": 20}}, "loc": {"start": {"line": 88, "column": 39}, "end": {"line": 104, "column": 1}}, "line": 88}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 111, "column": 23}, "end": {"line": 111, "column": 24}}, "loc": {"start": {"line": 111, "column": 43}, "end": {"line": 131, "column": 1}}, "line": 111}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 138, "column": 20}, "end": {"line": 138, "column": 21}}, "loc": {"start": {"line": 138, "column": 40}, "end": {"line": 162, "column": 1}}, "line": 138}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 169, "column": 24}, "end": {"line": 169, "column": 25}}, "loc": {"start": {"line": 169, "column": 44}, "end": {"line": 181, "column": 1}}, "line": 169}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 12}, "end": {"line": 24, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 19}, "end": {"line": 24, "column": 20}}], "line": 24}, "1": {"loc": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 33}, "end": {"line": 24, "column": 35}}], "line": 24}, "2": {"loc": {"start": {"line": 24, "column": 45}, "end": {"line": 24, "column": 65}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 54}, "end": {"line": 24, "column": 65}}], "line": 24}, "3": {"loc": {"start": {"line": 24, "column": 67}, "end": {"line": 24, "column": 85}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 79}, "end": {"line": 24, "column": 85}}], "line": 24}, "4": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}, {"start": {}, "end": {}}], "line": 57}, "5": {"loc": {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, "type": "if", "locations": [{"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, {"start": {}, "end": {}}], "line": 99}, "6": {"loc": {"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}, {"start": {}, "end": {}}], "line": 125}, "7": {"loc": {"start": {"line": 152, "column": 4}, "end": {"line": 154, "column": 5}}, "type": "if", "locations": [{"start": {"line": 152, "column": 4}, "end": {"line": 154, "column": 5}}, {"start": {}, "end": {}}], "line": 152}, "8": {"loc": {"start": {"line": 156, "column": 4}, "end": {"line": 158, "column": 5}}, "type": "if", "locations": [{"start": {"line": 156, "column": 4}, "end": {"line": 158, "column": 5}}, {"start": {}, "end": {}}], "line": 156}, "9": {"loc": {"start": {"line": 172, "column": 12}, "end": {"line": 172, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 172, "column": 19}, "end": {"line": 172, "column": 20}}], "line": 172}, "10": {"loc": {"start": {"line": 172, "column": 22}, "end": {"line": 172, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 172, "column": 33}, "end": {"line": 172, "column": 35}}], "line": 172}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0], "10": [0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/mockData.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/mockData.controller.js", "statementMap": {"0": {"start": {"line": 1, "column": 31}, "end": {"line": 1, "column": 66}}, "1": {"start": {"line": 2, "column": 20}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 42}}, "3": {"start": {"line": 7, "column": 4}, "end": {"line": 7, "column": 36}}, "4": {"start": {"line": 14, "column": 20}, "end": {"line": 36, "column": 1}}, "5": {"start": {"line": 15, "column": 2}, "end": {"line": 35, "column": 3}}, "6": {"start": {"line": 16, "column": 23}, "end": {"line": 16, "column": 33}}, "7": {"start": {"line": 19, "column": 4}, "end": {"line": 21, "column": 5}}, "8": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 57}}, "9": {"start": {"line": 24, "column": 19}, "end": {"line": 24, "column": 51}}, "10": {"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}, "11": {"start": {"line": 27, "column": 6}, "end": {"line": 29, "column": 9}}, "12": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 76}}, "13": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 53}}, "14": {"start": {"line": 38, "column": 0}, "end": {"line": 40, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 40}, "end": {"line": 36, "column": 1}}, "line": 14}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 4}, "end": {"line": 21, "column": 5}}, "type": "if", "locations": [{"start": {"line": 19, "column": 4}, "end": {"line": 21, "column": 5}}, {"start": {}, "end": {}}], "line": 19}, "1": {"loc": {"start": {"line": 19, "column": 8}, "end": {"line": 19, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 8}, "end": {"line": 19, "column": 34}}, {"start": {"line": 19, "column": 38}, "end": {"line": 19, "column": 63}}], "line": 19}, "2": {"loc": {"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}, "type": "if", "locations": [{"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}, {"start": {"line": 30, "column": 11}, "end": {"line": 32, "column": 5}}], "line": 26}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/noteV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/noteV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 55}}, "3": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 36}}, "4": {"start": {"line": 19, "column": 24}, "end": {"line": 58, "column": 1}}, "5": {"start": {"line": 20, "column": 2}, "end": {"line": 57, "column": 3}}, "6": {"start": {"line": 21, "column": 19}, "end": {"line": 21, "column": 34}}, "7": {"start": {"line": 22, "column": 22}, "end": {"line": 22, "column": 32}}, "8": {"start": {"line": 23, "column": 40}, "end": {"line": 23, "column": 49}}, "9": {"start": {"line": 26, "column": 19}, "end": {"line": 26, "column": 75}}, "10": {"start": {"line": 29, "column": 18}, "end": {"line": 44, "column": 7}}, "11": {"start": {"line": 29, "column": 43}, "end": {"line": 44, "column": 5}}, "12": {"start": {"line": 46, "column": 4}, "end": {"line": 54, "column": 7}}, "13": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 57}}, "14": {"start": {"line": 65, "column": 21}, "end": {"line": 101, "column": 1}}, "15": {"start": {"line": 66, "column": 2}, "end": {"line": 100, "column": 3}}, "16": {"start": {"line": 67, "column": 19}, "end": {"line": 67, "column": 34}}, "17": {"start": {"line": 68, "column": 40}, "end": {"line": 68, "column": 49}}, "18": {"start": {"line": 71, "column": 19}, "end": {"line": 71, "column": 73}}, "19": {"start": {"line": 74, "column": 18}, "end": {"line": 87, "column": 7}}, "20": {"start": {"line": 74, "column": 43}, "end": {"line": 87, "column": 5}}, "21": {"start": {"line": 89, "column": 4}, "end": {"line": 97, "column": 7}}, "22": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 54}}, "23": {"start": {"line": 108, "column": 20}, "end": {"line": 155, "column": 1}}, "24": {"start": {"line": 109, "column": 2}, "end": {"line": 154, "column": 3}}, "25": {"start": {"line": 110, "column": 19}, "end": {"line": 110, "column": 34}}, "26": {"start": {"line": 111, "column": 19}, "end": {"line": 111, "column": 29}}, "27": {"start": {"line": 114, "column": 17}, "end": {"line": 114, "column": 53}}, "28": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, "29": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 47}}, "30": {"start": {"line": 121, "column": 20}, "end": {"line": 121, "column": 43}}, "31": {"start": {"line": 122, "column": 24}, "end": {"line": 122, "column": 101}}, "32": {"start": {"line": 124, "column": 4}, "end": {"line": 126, "column": 5}}, "33": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 50}}, "34": {"start": {"line": 128, "column": 4}, "end": {"line": 151, "column": 7}}, "35": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 53}}, "36": {"start": {"line": 162, "column": 19}, "end": {"line": 189, "column": 1}}, "37": {"start": {"line": 163, "column": 2}, "end": {"line": 188, "column": 3}}, "38": {"start": {"line": 164, "column": 19}, "end": {"line": 164, "column": 34}}, "39": {"start": {"line": 165, "column": 66}, "end": {"line": 165, "column": 74}}, "40": {"start": {"line": 168, "column": 21}, "end": {"line": 174, "column": 5}}, "41": {"start": {"line": 176, "column": 17}, "end": {"line": 176, "column": 63}}, "42": {"start": {"line": 178, "column": 4}, "end": {"line": 185, "column": 7}}, "43": {"start": {"line": 187, "column": 4}, "end": {"line": 187, "column": 52}}, "44": {"start": {"line": 196, "column": 19}, "end": {"line": 224, "column": 1}}, "45": {"start": {"line": 197, "column": 2}, "end": {"line": 223, "column": 3}}, "46": {"start": {"line": 198, "column": 19}, "end": {"line": 198, "column": 34}}, "47": {"start": {"line": 199, "column": 19}, "end": {"line": 199, "column": 29}}, "48": {"start": {"line": 200, "column": 58}, "end": {"line": 200, "column": 66}}, "49": {"start": {"line": 203, "column": 23}, "end": {"line": 203, "column": 25}}, "50": {"start": {"line": 205, "column": 4}, "end": {"line": 205, "column": 54}}, "51": {"start": {"line": 205, "column": 29}, "end": {"line": 205, "column": 54}}, "52": {"start": {"line": 206, "column": 4}, "end": {"line": 206, "column": 60}}, "53": {"start": {"line": 206, "column": 31}, "end": {"line": 206, "column": 60}}, "54": {"start": {"line": 207, "column": 4}, "end": {"line": 207, "column": 64}}, "55": {"start": {"line": 207, "column": 32}, "end": {"line": 207, "column": 64}}, "56": {"start": {"line": 208, "column": 4}, "end": {"line": 208, "column": 54}}, "57": {"start": {"line": 208, "column": 29}, "end": {"line": 208, "column": 54}}, "58": {"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": 64}}, "59": {"start": {"line": 209, "column": 32}, "end": {"line": 209, "column": 64}}, "60": {"start": {"line": 211, "column": 17}, "end": {"line": 211, "column": 69}}, "61": {"start": {"line": 213, "column": 4}, "end": {"line": 220, "column": 16}}, "62": {"start": {"line": 222, "column": 4}, "end": {"line": 222, "column": 52}}, "63": {"start": {"line": 231, "column": 19}, "end": {"line": 247, "column": 1}}, "64": {"start": {"line": 232, "column": 2}, "end": {"line": 246, "column": 3}}, "65": {"start": {"line": 233, "column": 19}, "end": {"line": 233, "column": 34}}, "66": {"start": {"line": 234, "column": 19}, "end": {"line": 234, "column": 29}}, "67": {"start": {"line": 237, "column": 20}, "end": {"line": 237, "column": 60}}, "68": {"start": {"line": 239, "column": 4}, "end": {"line": 241, "column": 5}}, "69": {"start": {"line": 240, "column": 6}, "end": {"line": 240, "column": 52}}, "70": {"start": {"line": 243, "column": 4}, "end": {"line": 243, "column": 51}}, "71": {"start": {"line": 245, "column": 4}, "end": {"line": 245, "column": 52}}, "72": {"start": {"line": 289, "column": 23}, "end": {"line": 309, "column": 1}}, "73": {"start": {"line": 290, "column": 2}, "end": {"line": 308, "column": 3}}, "74": {"start": {"line": 291, "column": 19}, "end": {"line": 291, "column": 34}}, "75": {"start": {"line": 292, "column": 19}, "end": {"line": 292, "column": 29}}, "76": {"start": {"line": 295, "column": 4}, "end": {"line": 295, "column": 49}}, "77": {"start": {"line": 297, "column": 4}, "end": {"line": 299, "column": 7}}, "78": {"start": {"line": 301, "column": 4}, "end": {"line": 301, "column": 46}}, "79": {"start": {"line": 303, "column": 4}, "end": {"line": 305, "column": 5}}, "80": {"start": {"line": 304, "column": 6}, "end": {"line": 304, "column": 55}}, "81": {"start": {"line": 307, "column": 4}, "end": {"line": 307, "column": 56}}, "82": {"start": {"line": 351, "column": 20}, "end": {"line": 375, "column": 1}}, "83": {"start": {"line": 352, "column": 2}, "end": {"line": 374, "column": 3}}, "84": {"start": {"line": 353, "column": 19}, "end": {"line": 353, "column": 34}}, "85": {"start": {"line": 354, "column": 19}, "end": {"line": 354, "column": 29}}, "86": {"start": {"line": 357, "column": 4}, "end": {"line": 357, "column": 46}}, "87": {"start": {"line": 359, "column": 4}, "end": {"line": 361, "column": 7}}, "88": {"start": {"line": 363, "column": 4}, "end": {"line": 363, "column": 45}}, "89": {"start": {"line": 365, "column": 4}, "end": {"line": 367, "column": 5}}, "90": {"start": {"line": 366, "column": 6}, "end": {"line": 366, "column": 55}}, "91": {"start": {"line": 369, "column": 4}, "end": {"line": 371, "column": 5}}, "92": {"start": {"line": 370, "column": 6}, "end": {"line": 370, "column": 56}}, "93": {"start": {"line": 373, "column": 4}, "end": {"line": 373, "column": 53}}, "94": {"start": {"line": 446, "column": 24}, "end": {"line": 481, "column": 1}}, "95": {"start": {"line": 447, "column": 2}, "end": {"line": 480, "column": 3}}, "96": {"start": {"line": 448, "column": 19}, "end": {"line": 448, "column": 34}}, "97": {"start": {"line": 449, "column": 40}, "end": {"line": 449, "column": 49}}, "98": {"start": {"line": 452, "column": 19}, "end": {"line": 452, "column": 76}}, "99": {"start": {"line": 455, "column": 18}, "end": {"line": 467, "column": 7}}, "100": {"start": {"line": 455, "column": 43}, "end": {"line": 467, "column": 5}}, "101": {"start": {"line": 469, "column": 4}, "end": {"line": 477, "column": 7}}, "102": {"start": {"line": 479, "column": 4}, "end": {"line": 479, "column": 57}}, "103": {"start": {"line": 488, "column": 17}, "end": {"line": 503, "column": 1}}, "104": {"start": {"line": 489, "column": 2}, "end": {"line": 502, "column": 3}}, "105": {"start": {"line": 490, "column": 19}, "end": {"line": 490, "column": 34}}, "106": {"start": {"line": 491, "column": 23}, "end": {"line": 491, "column": 33}}, "107": {"start": {"line": 494, "column": 20}, "end": {"line": 494, "column": 62}}, "108": {"start": {"line": 496, "column": 4}, "end": {"line": 499, "column": 38}}, "109": {"start": {"line": 501, "column": 4}, "end": {"line": 501, "column": 50}}, "110": {"start": {"line": 510, "column": 20}, "end": {"line": 523, "column": 1}}, "111": {"start": {"line": 511, "column": 2}, "end": {"line": 522, "column": 3}}, "112": {"start": {"line": 512, "column": 19}, "end": {"line": 512, "column": 34}}, "113": {"start": {"line": 513, "column": 23}, "end": {"line": 513, "column": 33}}, "114": {"start": {"line": 514, "column": 24}, "end": {"line": 514, "column": 32}}, "115": {"start": {"line": 517, "column": 20}, "end": {"line": 517, "column": 74}}, "116": {"start": {"line": 519, "column": 4}, "end": {"line": 519, "column": 45}}, "117": {"start": {"line": 521, "column": 4}, "end": {"line": 521, "column": 53}}, "118": {"start": {"line": 525, "column": 0}, "end": {"line": 537, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 19, "column": 24}, "end": {"line": 19, "column": 25}}, "loc": {"start": {"line": 19, "column": 44}, "end": {"line": 58, "column": 1}}, "line": 19}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 34}, "end": {"line": 29, "column": 35}}, "loc": {"start": {"line": 29, "column": 43}, "end": {"line": 44, "column": 5}}, "line": 29}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 65, "column": 21}, "end": {"line": 65, "column": 22}}, "loc": {"start": {"line": 65, "column": 41}, "end": {"line": 101, "column": 1}}, "line": 65}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 74, "column": 34}, "end": {"line": 74, "column": 35}}, "loc": {"start": {"line": 74, "column": 43}, "end": {"line": 87, "column": 5}}, "line": 74}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 108, "column": 20}, "end": {"line": 108, "column": 21}}, "loc": {"start": {"line": 108, "column": 40}, "end": {"line": 155, "column": 1}}, "line": 108}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 162, "column": 19}, "end": {"line": 162, "column": 20}}, "loc": {"start": {"line": 162, "column": 39}, "end": {"line": 189, "column": 1}}, "line": 162}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 196, "column": 19}, "end": {"line": 196, "column": 20}}, "loc": {"start": {"line": 196, "column": 39}, "end": {"line": 224, "column": 1}}, "line": 196}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 231, "column": 19}, "end": {"line": 231, "column": 20}}, "loc": {"start": {"line": 231, "column": 39}, "end": {"line": 247, "column": 1}}, "line": 231}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 289, "column": 23}, "end": {"line": 289, "column": 24}}, "loc": {"start": {"line": 289, "column": 43}, "end": {"line": 309, "column": 1}}, "line": 289}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 351, "column": 20}, "end": {"line": 351, "column": 21}}, "loc": {"start": {"line": 351, "column": 40}, "end": {"line": 375, "column": 1}}, "line": 351}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 446, "column": 24}, "end": {"line": 446, "column": 25}}, "loc": {"start": {"line": 446, "column": 44}, "end": {"line": 481, "column": 1}}, "line": 446}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 455, "column": 34}, "end": {"line": 455, "column": 35}}, "loc": {"start": {"line": 455, "column": 43}, "end": {"line": 467, "column": 5}}, "line": 455}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 488, "column": 17}, "end": {"line": 488, "column": 18}}, "loc": {"start": {"line": 488, "column": 37}, "end": {"line": 503, "column": 1}}, "line": 488}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 510, "column": 20}, "end": {"line": 510, "column": 21}}, "loc": {"start": {"line": 510, "column": 40}, "end": {"line": 523, "column": 1}}, "line": 510}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 12}, "end": {"line": 23, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 20}}], "line": 23}, "1": {"loc": {"start": {"line": 23, "column": 22}, "end": {"line": 23, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 33}, "end": {"line": 23, "column": 35}}], "line": 23}, "2": {"loc": {"start": {"line": 39, "column": 12}, "end": {"line": 43, "column": 14}}, "type": "cond-expr", "locations": [{"start": {"line": 39, "column": 24}, "end": {"line": 43, "column": 7}}, {"start": {"line": 43, "column": 10}, "end": {"line": 43, "column": 14}}], "line": 39}, "3": {"loc": {"start": {"line": 68, "column": 12}, "end": {"line": 68, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 68, "column": 19}, "end": {"line": 68, "column": 20}}], "line": 68}, "4": {"loc": {"start": {"line": 68, "column": 22}, "end": {"line": 68, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 68, "column": 33}, "end": {"line": 68, "column": 35}}], "line": 68}, "5": {"loc": {"start": {"line": 83, "column": 11}, "end": {"line": 86, "column": 14}}, "type": "cond-expr", "locations": [{"start": {"line": 83, "column": 22}, "end": {"line": 86, "column": 7}}, {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 14}}], "line": 83}, "6": {"loc": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, "type": "if", "locations": [{"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, {"start": {}, "end": {}}], "line": 116}, "7": {"loc": {"start": {"line": 122, "column": 24}, "end": {"line": 122, "column": 101}}, "type": "binary-expr", "locations": [{"start": {"line": 122, "column": 24}, "end": {"line": 122, "column": 32}}, {"start": {"line": 122, "column": 36}, "end": {"line": 122, "column": 57}}, {"start": {"line": 122, "column": 61}, "end": {"line": 122, "column": 101}}], "line": 122}, "8": {"loc": {"start": {"line": 124, "column": 4}, "end": {"line": 126, "column": 5}}, "type": "if", "locations": [{"start": {"line": 124, "column": 4}, "end": {"line": 126, "column": 5}}, {"start": {}, "end": {}}], "line": 124}, "9": {"loc": {"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 16}}, {"start": {"line": 124, "column": 20}, "end": {"line": 124, "column": 32}}, {"start": {"line": 124, "column": 36}, "end": {"line": 124, "column": 63}}], "line": 124}, "10": {"loc": {"start": {"line": 141, "column": 12}, "end": {"line": 145, "column": 14}}, "type": "cond-expr", "locations": [{"start": {"line": 141, "column": 24}, "end": {"line": 145, "column": 7}}, {"start": {"line": 145, "column": 10}, "end": {"line": 145, "column": 14}}], "line": 141}, "11": {"loc": {"start": {"line": 146, "column": 11}, "end": {"line": 149, "column": 14}}, "type": "cond-expr", "locations": [{"start": {"line": 146, "column": 22}, "end": {"line": 149, "column": 7}}, {"start": {"line": 149, "column": 10}, "end": {"line": 149, "column": 14}}], "line": 146}, "12": {"loc": {"start": {"line": 165, "column": 45}, "end": {"line": 165, "column": 61}}, "type": "default-arg", "locations": [{"start": {"line": 165, "column": 56}, "end": {"line": 165, "column": 61}}], "line": 165}, "13": {"loc": {"start": {"line": 205, "column": 4}, "end": {"line": 205, "column": 54}}, "type": "if", "locations": [{"start": {"line": 205, "column": 4}, "end": {"line": 205, "column": 54}}, {"start": {}, "end": {}}], "line": 205}, "14": {"loc": {"start": {"line": 206, "column": 4}, "end": {"line": 206, "column": 60}}, "type": "if", "locations": [{"start": {"line": 206, "column": 4}, "end": {"line": 206, "column": 60}}, {"start": {}, "end": {}}], "line": 206}, "15": {"loc": {"start": {"line": 207, "column": 4}, "end": {"line": 207, "column": 64}}, "type": "if", "locations": [{"start": {"line": 207, "column": 4}, "end": {"line": 207, "column": 64}}, {"start": {}, "end": {}}], "line": 207}, "16": {"loc": {"start": {"line": 208, "column": 4}, "end": {"line": 208, "column": 54}}, "type": "if", "locations": [{"start": {"line": 208, "column": 4}, "end": {"line": 208, "column": 54}}, {"start": {}, "end": {}}], "line": 208}, "17": {"loc": {"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": 64}}, "type": "if", "locations": [{"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": 64}}, {"start": {}, "end": {}}], "line": 209}, "18": {"loc": {"start": {"line": 239, "column": 4}, "end": {"line": 241, "column": 5}}, "type": "if", "locations": [{"start": {"line": 239, "column": 4}, "end": {"line": 241, "column": 5}}, {"start": {}, "end": {}}], "line": 239}, "19": {"loc": {"start": {"line": 303, "column": 4}, "end": {"line": 305, "column": 5}}, "type": "if", "locations": [{"start": {"line": 303, "column": 4}, "end": {"line": 305, "column": 5}}, {"start": {}, "end": {}}], "line": 303}, "20": {"loc": {"start": {"line": 365, "column": 4}, "end": {"line": 367, "column": 5}}, "type": "if", "locations": [{"start": {"line": 365, "column": 4}, "end": {"line": 367, "column": 5}}, {"start": {}, "end": {}}], "line": 365}, "21": {"loc": {"start": {"line": 369, "column": 4}, "end": {"line": 371, "column": 5}}, "type": "if", "locations": [{"start": {"line": 369, "column": 4}, "end": {"line": 371, "column": 5}}, {"start": {}, "end": {}}], "line": 369}, "22": {"loc": {"start": {"line": 449, "column": 12}, "end": {"line": 449, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 449, "column": 19}, "end": {"line": 449, "column": 20}}], "line": 449}, "23": {"loc": {"start": {"line": 449, "column": 22}, "end": {"line": 449, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 449, "column": 33}, "end": {"line": 449, "column": 35}}], "line": 449}, "24": {"loc": {"start": {"line": 463, "column": 11}, "end": {"line": 466, "column": 14}}, "type": "cond-expr", "locations": [{"start": {"line": 463, "column": 22}, "end": {"line": 466, "column": 7}}, {"start": {"line": 466, "column": 10}, "end": {"line": 466, "column": 14}}], "line": 463}, "25": {"loc": {"start": {"line": 499, "column": 7}, "end": {"line": 499, "column": 36}}, "type": "cond-expr", "locations": [{"start": {"line": 499, "column": 17}, "end": {"line": 499, "column": 24}}, {"start": {"line": 499, "column": 27}, "end": {"line": 499, "column": 36}}], "line": 499}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0, 0], "8": [0, 0], "9": [0, 0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0], "23": [0], "24": [0, 0], "25": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/squareV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/squareV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 59}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 36}}, "4": {"start": {"line": 72, "column": 23}, "end": {"line": 91, "column": 1}}, "5": {"start": {"line": 73, "column": 2}, "end": {"line": 90, "column": 3}}, "6": {"start": {"line": 75, "column": 19}, "end": {"line": 75, "column": 52}}, "7": {"start": {"line": 76, "column": 66}, "end": {"line": 76, "column": 75}}, "8": {"start": {"line": 79, "column": 19}, "end": {"line": 85, "column": 5}}, "9": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 44}}, "10": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 56}}, "11": {"start": {"line": 130, "column": 22}, "end": {"line": 145, "column": 1}}, "12": {"start": {"line": 131, "column": 2}, "end": {"line": 144, "column": 3}}, "13": {"start": {"line": 133, "column": 19}, "end": {"line": 133, "column": 52}}, "14": {"start": {"line": 136, "column": 19}, "end": {"line": 136, "column": 60}}, "15": {"start": {"line": 138, "column": 4}, "end": {"line": 138, "column": 44}}, "16": {"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}, "17": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 53}}, "18": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 55}}, "19": {"start": {"line": 187, "column": 28}, "end": {"line": 203, "column": 1}}, "20": {"start": {"line": 188, "column": 2}, "end": {"line": 202, "column": 3}}, "21": {"start": {"line": 190, "column": 19}, "end": {"line": 190, "column": 52}}, "22": {"start": {"line": 191, "column": 26}, "end": {"line": 191, "column": 35}}, "23": {"start": {"line": 194, "column": 19}, "end": {"line": 194, "column": 73}}, "24": {"start": {"line": 196, "column": 4}, "end": {"line": 196, "column": 44}}, "25": {"start": {"line": 198, "column": 4}, "end": {"line": 200, "column": 5}}, "26": {"start": {"line": 199, "column": 6}, "end": {"line": 199, "column": 53}}, "27": {"start": {"line": 201, "column": 4}, "end": {"line": 201, "column": 61}}, "28": {"start": {"line": 292, "column": 0}, "end": {"line": 296, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 72, "column": 23}, "end": {"line": 72, "column": 24}}, "loc": {"start": {"line": 72, "column": 43}, "end": {"line": 91, "column": 1}}, "line": 72}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 130, "column": 22}, "end": {"line": 130, "column": 23}}, "loc": {"start": {"line": 130, "column": 42}, "end": {"line": 145, "column": 1}}, "line": 130}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 187, "column": 28}, "end": {"line": 187, "column": 29}}, "loc": {"start": {"line": 187, "column": 48}, "end": {"line": 203, "column": 1}}, "line": 187}}, "branchMap": {"0": {"loc": {"start": {"line": 75, "column": 19}, "end": {"line": 75, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 30}, "end": {"line": 75, "column": 45}}, {"start": {"line": 75, "column": 48}, "end": {"line": 75, "column": 52}}], "line": 75}, "1": {"loc": {"start": {"line": 76, "column": 19}, "end": {"line": 76, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 76, "column": 26}, "end": {"line": 76, "column": 27}}], "line": 76}, "2": {"loc": {"start": {"line": 76, "column": 29}, "end": {"line": 76, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 76, "column": 40}, "end": {"line": 76, "column": 42}}], "line": 76}, "3": {"loc": {"start": {"line": 76, "column": 44}, "end": {"line": 76, "column": 61}}, "type": "default-arg", "locations": [{"start": {"line": 76, "column": 53}, "end": {"line": 76, "column": 61}}], "line": 76}, "4": {"loc": {"start": {"line": 133, "column": 19}, "end": {"line": 133, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 133, "column": 30}, "end": {"line": 133, "column": 45}}, {"start": {"line": 133, "column": 48}, "end": {"line": 133, "column": 52}}], "line": 133}, "5": {"loc": {"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}, "type": "if", "locations": [{"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}, {"start": {}, "end": {}}], "line": 140}, "6": {"loc": {"start": {"line": 190, "column": 19}, "end": {"line": 190, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 190, "column": 30}, "end": {"line": 190, "column": 45}}, {"start": {"line": 190, "column": 48}, "end": {"line": 190, "column": 52}}], "line": 190}, "7": {"loc": {"start": {"line": 191, "column": 12}, "end": {"line": 191, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 191, "column": 20}, "end": {"line": 191, "column": 21}}], "line": 191}, "8": {"loc": {"start": {"line": 198, "column": 4}, "end": {"line": 200, "column": 5}}, "type": "if", "locations": [{"start": {"line": 198, "column": 4}, "end": {"line": 200, "column": 5}}, {"start": {}, "end": {}}], "line": 198}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/statisticsMonitor.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/statisticsMonitor.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 26}, "end": {"line": 5, "column": 74}}, "1": {"start": {"line": 6, "column": 20}, "end": {"line": 6, "column": 51}}, "2": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 42}}, "3": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 59}}, "4": {"start": {"line": 15, "column": 30}, "end": {"line": 22, "column": 1}}, "5": {"start": {"line": 16, "column": 2}, "end": {"line": 21, "column": 3}}, "6": {"start": {"line": 17, "column": 20}, "end": {"line": 17, "column": 61}}, "7": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 45}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 63}}, "9": {"start": {"line": 29, "column": 27}, "end": {"line": 47, "column": 1}}, "10": {"start": {"line": 30, "column": 2}, "end": {"line": 46, "column": 3}}, "11": {"start": {"line": 31, "column": 25}, "end": {"line": 31, "column": 35}}, "12": {"start": {"line": 33, "column": 4}, "end": {"line": 35, "column": 5}}, "13": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 51}}, "14": {"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 66}}, "15": {"start": {"line": 39, "column": 4}, "end": {"line": 41, "column": 5}}, "16": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 64}}, "17": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 45}}, "18": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 60}}, "19": {"start": {"line": 54, "column": 30}, "end": {"line": 61, "column": 1}}, "20": {"start": {"line": 55, "column": 2}, "end": {"line": 60, "column": 3}}, "21": {"start": {"line": 56, "column": 20}, "end": {"line": 56, "column": 61}}, "22": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 45}}, "23": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 63}}, "24": {"start": {"line": 68, "column": 24}, "end": {"line": 78, "column": 1}}, "25": {"start": {"line": 69, "column": 2}, "end": {"line": 77, "column": 3}}, "26": {"start": {"line": 70, "column": 22}, "end": {"line": 70, "column": 31}}, "27": {"start": {"line": 71, "column": 21}, "end": {"line": 71, "column": 49}}, "28": {"start": {"line": 73, "column": 25}, "end": {"line": 73, "column": 74}}, "29": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 54}}, "30": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 57}}, "31": {"start": {"line": 85, "column": 21}, "end": {"line": 92, "column": 1}}, "32": {"start": {"line": 86, "column": 2}, "end": {"line": 91, "column": 3}}, "33": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 43}}, "34": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 60}}, "35": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 54}}, "36": {"start": {"line": 94, "column": 0}, "end": {"line": 100, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": 31}}, "loc": {"start": {"line": 15, "column": 50}, "end": {"line": 22, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 27}, "end": {"line": 29, "column": 28}}, "loc": {"start": {"line": 29, "column": 47}, "end": {"line": 47, "column": 1}}, "line": 29}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 54, "column": 30}, "end": {"line": 54, "column": 31}}, "loc": {"start": {"line": 54, "column": 50}, "end": {"line": 61, "column": 1}}, "line": 54}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 68, "column": 24}, "end": {"line": 68, "column": 25}}, "loc": {"start": {"line": 68, "column": 44}, "end": {"line": 78, "column": 1}}, "line": 68}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": 22}}, "loc": {"start": {"line": 85, "column": 41}, "end": {"line": 92, "column": 1}}, "line": 85}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 35, "column": 5}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 35, "column": 5}}, {"start": {}, "end": {}}], "line": 33}, "1": {"loc": {"start": {"line": 39, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 39, "column": 4}, "end": {"line": 41, "column": 5}}, {"start": {}, "end": {}}], "line": 39}, "2": {"loc": {"start": {"line": 71, "column": 21}, "end": {"line": 71, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 71, "column": 29}, "end": {"line": 71, "column": 44}}, {"start": {"line": 71, "column": 47}, "end": {"line": 71, "column": 49}}], "line": 71}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/statisticsV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/statisticsV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 59}}, "3": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 47}}, "4": {"start": {"line": 24, "column": 4}, "end": {"line": 32, "column": 5}}, "5": {"start": {"line": 25, "column": 21}, "end": {"line": 25, "column": 36}}, "6": {"start": {"line": 27, "column": 25}, "end": {"line": 27, "column": 83}}, "7": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 50}}, "8": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 65}}, "9": {"start": {"line": 41, "column": 4}, "end": {"line": 57, "column": 5}}, "10": {"start": {"line": 42, "column": 21}, "end": {"line": 42, "column": 36}}, "11": {"start": {"line": 43, "column": 52}, "end": {"line": 43, "column": 61}}, "12": {"start": {"line": 45, "column": 21}, "end": {"line": 50, "column": 7}}, "13": {"start": {"line": 52, "column": 21}, "end": {"line": 52, "column": 81}}, "14": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 46}}, "15": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 59}}, "16": {"start": {"line": 66, "column": 4}, "end": {"line": 87, "column": 5}}, "17": {"start": {"line": 67, "column": 21}, "end": {"line": 67, "column": 36}}, "18": {"start": {"line": 68, "column": 82}, "end": {"line": 68, "column": 90}}, "19": {"start": {"line": 70, "column": 27}, "end": {"line": 76, "column": 7}}, "20": {"start": {"line": 78, "column": 21}, "end": {"line": 82, "column": 7}}, "21": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 57}}, "22": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 66}}, "23": {"start": {"line": 96, "column": 4}, "end": {"line": 118, "column": 5}}, "24": {"start": {"line": 97, "column": 21}, "end": {"line": 97, "column": 36}}, "25": {"start": {"line": 98, "column": 88}, "end": {"line": 98, "column": 97}}, "26": {"start": {"line": 100, "column": 22}, "end": {"line": 106, "column": 7}}, "27": {"start": {"line": 108, "column": 21}, "end": {"line": 113, "column": 7}}, "28": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 46}}, "29": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 65}}, "30": {"start": {"line": 127, "column": 4}, "end": {"line": 135, "column": 5}}, "31": {"start": {"line": 128, "column": 21}, "end": {"line": 128, "column": 36}}, "32": {"start": {"line": 130, "column": 23}, "end": {"line": 130, "column": 79}}, "33": {"start": {"line": 132, "column": 6}, "end": {"line": 132, "column": 48}}, "34": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 63}}, "35": {"start": {"line": 144, "column": 4}, "end": {"line": 156, "column": 5}}, "36": {"start": {"line": 145, "column": 21}, "end": {"line": 145, "column": 36}}, "37": {"start": {"line": 146, "column": 23}, "end": {"line": 146, "column": 32}}, "38": {"start": {"line": 148, "column": 20}, "end": {"line": 151, "column": 7}}, "39": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 49}}, "40": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 60}}, "41": {"start": {"line": 161, "column": 0}, "end": {"line": 172, "column": 2}}, "42": {"start": {"line": 162, "column": 21}, "end": {"line": 162, "column": 66}}, "43": {"start": {"line": 164, "column": 2}, "end": {"line": 171, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 3}}, "loc": {"start": {"line": 14, "column": 33}, "end": {"line": 16, "column": 3}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 3}}, "loc": {"start": {"line": 23, "column": 40}, "end": {"line": 33, "column": 3}}, "line": 23}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 3}}, "loc": {"start": {"line": 40, "column": 34}, "end": {"line": 58, "column": 3}}, "line": 40}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 3}}, "loc": {"start": {"line": 65, "column": 41}, "end": {"line": 88, "column": 3}}, "line": 65}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 3}}, "loc": {"start": {"line": 95, "column": 40}, "end": {"line": 119, "column": 3}}, "line": 95}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 126, "column": 2}, "end": {"line": 126, "column": 3}}, "loc": {"start": {"line": 126, "column": 38}, "end": {"line": 136, "column": 3}}, "line": 126}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 143, "column": 2}, "end": {"line": 143, "column": 3}}, "loc": {"start": {"line": 143, "column": 35}, "end": {"line": 157, "column": 3}}, "line": 143}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 161, "column": 17}, "end": {"line": 161, "column": 18}}, "loc": {"start": {"line": 161, "column": 40}, "end": {"line": 172, "column": 1}}, "line": 161}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 19}, "end": {"line": 46, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 46, "column": 31}, "end": {"line": 46, "column": 50}}, {"start": {"line": 46, "column": 53}, "end": {"line": 46, "column": 62}}], "line": 46}, "1": {"loc": {"start": {"line": 47, "column": 17}, "end": {"line": 47, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 47, "column": 27}, "end": {"line": 47, "column": 44}}, {"start": {"line": 47, "column": 47}, "end": {"line": 47, "column": 56}}], "line": 47}, "2": {"loc": {"start": {"line": 48, "column": 15}, "end": {"line": 48, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 48, "column": 23}, "end": {"line": 48, "column": 38}}, {"start": {"line": 48, "column": 41}, "end": {"line": 48, "column": 50}}], "line": 48}, "3": {"loc": {"start": {"line": 49, "column": 16}, "end": {"line": 49, "column": 53}}, "type": "cond-expr", "locations": [{"start": {"line": 49, "column": 25}, "end": {"line": 49, "column": 41}}, {"start": {"line": 49, "column": 44}, "end": {"line": 49, "column": 53}}], "line": 49}, "4": {"loc": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 33}}, "type": "cond-expr", "locations": [{"start": {"line": 111, "column": 15}, "end": {"line": 111, "column": 29}}, {"start": {"line": 111, "column": 32}, "end": {"line": 111, "column": 33}}], "line": 111}, "5": {"loc": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 42}}, "type": "cond-expr", "locations": [{"start": {"line": 112, "column": 19}, "end": {"line": 112, "column": 37}}, {"start": {"line": 112, "column": 40}, "end": {"line": 112, "column": 42}}], "line": 112}, "6": {"loc": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 34}}, "type": "cond-expr", "locations": [{"start": {"line": 150, "column": 15}, "end": {"line": 150, "column": 29}}, {"start": {"line": 150, "column": 32}, "end": {"line": 150, "column": 34}}], "line": 150}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/tagV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/tagV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 62}}, "2": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 42}}, "3": {"start": {"line": 8, "column": 71}, "end": {"line": 8, "column": 103}}, "4": {"start": {"line": 11, "column": 19}, "end": {"line": 11, "column": 60}}, "5": {"start": {"line": 53, "column": 22}, "end": {"line": 71, "column": 1}}, "6": {"start": {"line": 54, "column": 2}, "end": {"line": 70, "column": 3}}, "7": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 34}}, "8": {"start": {"line": 56, "column": 19}, "end": {"line": 56, "column": 29}}, "9": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 47}}, "10": {"start": {"line": 61, "column": 4}, "end": {"line": 63, "column": 7}}, "11": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, "12": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 55}}, "13": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 55}}, "14": {"start": {"line": 115, "column": 19}, "end": {"line": 137, "column": 1}}, "15": {"start": {"line": 116, "column": 2}, "end": {"line": 136, "column": 3}}, "16": {"start": {"line": 117, "column": 19}, "end": {"line": 117, "column": 34}}, "17": {"start": {"line": 118, "column": 19}, "end": {"line": 118, "column": 29}}, "18": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 44}}, "19": {"start": {"line": 123, "column": 4}, "end": {"line": 125, "column": 7}}, "20": {"start": {"line": 127, "column": 4}, "end": {"line": 129, "column": 5}}, "21": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 55}}, "22": {"start": {"line": 131, "column": 4}, "end": {"line": 133, "column": 5}}, "23": {"start": {"line": 132, "column": 6}, "end": {"line": 132, "column": 55}}, "24": {"start": {"line": 135, "column": 4}, "end": {"line": 135, "column": 52}}, "25": {"start": {"line": 195, "column": 23}, "end": {"line": 233, "column": 1}}, "26": {"start": {"line": 196, "column": 2}, "end": {"line": 232, "column": 3}}, "27": {"start": {"line": 197, "column": 19}, "end": {"line": 197, "column": 34}}, "28": {"start": {"line": 198, "column": 40}, "end": {"line": 198, "column": 49}}, "29": {"start": {"line": 201, "column": 19}, "end": {"line": 205, "column": 5}}, "30": {"start": {"line": 208, "column": 26}, "end": {"line": 221, "column": 7}}, "31": {"start": {"line": 208, "column": 50}, "end": {"line": 221, "column": 5}}, "32": {"start": {"line": 223, "column": 4}, "end": {"line": 229, "column": 7}}, "33": {"start": {"line": 231, "column": 4}, "end": {"line": 231, "column": 56}}, "34": {"start": {"line": 278, "column": 0}, "end": {"line": 282, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 53, "column": 22}, "end": {"line": 53, "column": 23}}, "loc": {"start": {"line": 53, "column": 42}, "end": {"line": 71, "column": 1}}, "line": 53}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 115, "column": 19}, "end": {"line": 115, "column": 20}}, "loc": {"start": {"line": 115, "column": 39}, "end": {"line": 137, "column": 1}}, "line": 115}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 195, "column": 23}, "end": {"line": 195, "column": 24}}, "loc": {"start": {"line": 195, "column": 43}, "end": {"line": 233, "column": 1}}, "line": 195}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 208, "column": 42}, "end": {"line": 208, "column": 43}}, "loc": {"start": {"line": 208, "column": 50}, "end": {"line": 221, "column": 5}}, "line": 208}}, "branchMap": {"0": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, "type": "if", "locations": [{"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, {"start": {}, "end": {}}], "line": 65}, "1": {"loc": {"start": {"line": 127, "column": 4}, "end": {"line": 129, "column": 5}}, "type": "if", "locations": [{"start": {"line": 127, "column": 4}, "end": {"line": 129, "column": 5}}, {"start": {}, "end": {}}], "line": 127}, "2": {"loc": {"start": {"line": 131, "column": 4}, "end": {"line": 133, "column": 5}}, "type": "if", "locations": [{"start": {"line": 131, "column": 4}, "end": {"line": 133, "column": 5}}, {"start": {}, "end": {}}], "line": 131}, "3": {"loc": {"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 198, "column": 19}, "end": {"line": 198, "column": 20}}], "line": 198}, "4": {"loc": {"start": {"line": 198, "column": 22}, "end": {"line": 198, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 198, "column": 33}, "end": {"line": 198, "column": 35}}], "line": 198}, "5": {"loc": {"start": {"line": 217, "column": 16}, "end": {"line": 220, "column": 14}}, "type": "cond-expr", "locations": [{"start": {"line": 217, "column": 31}, "end": {"line": 220, "column": 7}}, {"start": {"line": 220, "column": 10}, "end": {"line": 220, "column": 14}}], "line": 217}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 3, "7": 3, "8": 3, "9": 3, "10": 1, "11": 2, "12": 1, "13": 1, "14": 1, "15": 4, "16": 4, "17": 4, "18": 4, "19": 1, "20": 3, "21": 1, "22": 2, "23": 1, "24": 1, "25": 1, "26": 2, "27": 2, "28": 2, "29": 2, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1}, "f": {"0": 3, "1": 4, "2": 2, "3": 1}, "b": {"0": [1, 1], "1": [1, 2], "2": [1, 1], "3": [0], "4": [0], "5": [1, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "5c378a91a88361dfdd1de64c7e47ae5e6e626d4a"}, "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/themeV2.controller.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/controllers/themeV2.controller.js", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 62}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 36}}, "4": {"start": {"line": 14, "column": 21}, "end": {"line": 14, "column": 64}}, "5": {"start": {"line": 21, "column": 18}, "end": {"line": 33, "column": 1}}, "6": {"start": {"line": 22, "column": 2}, "end": {"line": 32, "column": 3}}, "7": {"start": {"line": 23, "column": 32}, "end": {"line": 23, "column": 41}}, "8": {"start": {"line": 24, "column": 32}, "end": {"line": 24, "column": 58}}, "9": {"start": {"line": 27, "column": 19}, "end": {"line": 27, "column": 71}}, "10": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 48}}, "11": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 51}}, "12": {"start": {"line": 40, "column": 21}, "end": {"line": 54, "column": 1}}, "13": {"start": {"line": 41, "column": 2}, "end": {"line": 53, "column": 3}}, "14": {"start": {"line": 42, "column": 19}, "end": {"line": 42, "column": 29}}, "15": {"start": {"line": 45, "column": 18}, "end": {"line": 45, "column": 53}}, "16": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 43}}, "17": {"start": {"line": 49, "column": 4}, "end": {"line": 51, "column": 5}}, "18": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 51}}, "19": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 54}}, "20": {"start": {"line": 61, "column": 24}, "end": {"line": 80, "column": 1}}, "21": {"start": {"line": 62, "column": 2}, "end": {"line": 79, "column": 3}}, "22": {"start": {"line": 63, "column": 19}, "end": {"line": 63, "column": 29}}, "23": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 43}}, "24": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 7}}, "25": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 46}}, "26": {"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, "27": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 47}}, "28": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 57}}, "29": {"start": {"line": 87, "column": 21}, "end": {"line": 110, "column": 1}}, "30": {"start": {"line": 88, "column": 2}, "end": {"line": 109, "column": 3}}, "31": {"start": {"line": 89, "column": 19}, "end": {"line": 89, "column": 29}}, "32": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 40}}, "33": {"start": {"line": 94, "column": 4}, "end": {"line": 96, "column": 7}}, "34": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 45}}, "35": {"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}, "36": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 47}}, "37": {"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": 5}}, "38": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 56}}, "39": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 54}}, "40": {"start": {"line": 117, "column": 25}, "end": {"line": 128, "column": 1}}, "41": {"start": {"line": 118, "column": 2}, "end": {"line": 127, "column": 3}}, "42": {"start": {"line": 119, "column": 40}, "end": {"line": 119, "column": 49}}, "43": {"start": {"line": 122, "column": 19}, "end": {"line": 122, "column": 70}}, "44": {"start": {"line": 124, "column": 4}, "end": {"line": 124, "column": 44}}, "45": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 58}}, "46": {"start": {"line": 130, "column": 0}, "end": {"line": 136, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 19}}, "loc": {"start": {"line": 21, "column": 38}, "end": {"line": 33, "column": 1}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 22}}, "loc": {"start": {"line": 40, "column": 41}, "end": {"line": 54, "column": 1}}, "line": 40}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 61, "column": 24}, "end": {"line": 61, "column": 25}}, "loc": {"start": {"line": 61, "column": 44}, "end": {"line": 80, "column": 1}}, "line": 61}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 87, "column": 21}, "end": {"line": 87, "column": 22}}, "loc": {"start": {"line": 87, "column": 41}, "end": {"line": 110, "column": 1}}, "line": 87}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 117, "column": 25}, "end": {"line": 117, "column": 26}}, "loc": {"start": {"line": 117, "column": 45}, "end": {"line": 128, "column": 1}}, "line": 117}}, "branchMap": {"0": {"loc": {"start": {"line": 49, "column": 4}, "end": {"line": 51, "column": 5}}, "type": "if", "locations": [{"start": {"line": 49, "column": 4}, "end": {"line": 51, "column": 5}}, {"start": {}, "end": {}}], "line": 49}, "1": {"loc": {"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, "type": "if", "locations": [{"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, {"start": {}, "end": {}}], "line": 74}, "2": {"loc": {"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}, "type": "if", "locations": [{"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}, {"start": {}, "end": {}}], "line": 100}, "3": {"loc": {"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": 5}}, "type": "if", "locations": [{"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": 5}}, {"start": {}, "end": {}}], "line": 104}, "4": {"loc": {"start": {"line": 119, "column": 12}, "end": {"line": 119, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 119, "column": 19}, "end": {"line": 119, "column": 20}}], "line": 119}, "5": {"loc": {"start": {"line": 119, "column": 22}, "end": {"line": 119, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 119, "column": 33}, "end": {"line": 119, "column": 35}}], "line": 119}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/middlewares/errorHandler.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/middlewares/errorHandler.js", "statementMap": {"0": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 41}}, "1": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 93}}, "2": {"start": {"line": 21, "column": 0}, "end": {"line": 25, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/middlewares/index.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/middlewares/index.js", "statementMap": {"0": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 41}}, "1": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 41}}, "2": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 50}}, "3": {"start": {"line": 31, "column": 32}, "end": {"line": 31, "column": 69}}, "4": {"start": {"line": 34, "column": 22}, "end": {"line": 34, "column": 60}}, "5": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 37}}, "6": {"start": {"line": 44, "column": 36}, "end": {"line": 44, "column": 77}}, "7": {"start": {"line": 47, "column": 21}, "end": {"line": 47, "column": 55}}, "8": {"start": {"line": 50, "column": 28}, "end": {"line": 50, "column": 57}}, "9": {"start": {"line": 53, "column": 28}, "end": {"line": 53, "column": 61}}, "10": {"start": {"line": 56, "column": 0}, "end": {"line": 97, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/DeadLetterQueue.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/DeadLetterQueue.js", "statementMap": {"0": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 42}}, "1": {"start": {"line": 6, "column": 22}, "end": {"line": 6, "column": 51}}, "2": {"start": {"line": 8, "column": 24}, "end": {"line": 121, "column": 2}}, "3": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 33}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/achievement.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/achievement.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 20}, "end": {"line": 84, "column": 2}}, "3": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 29}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/bContent.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/bContent.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 17}, "end": {"line": 23, "column": 2}}, "3": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 26}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/badge.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/badge.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 14}, "end": {"line": 61, "column": 2}}, "3": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 23}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/bubbleContent.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/bubbleContent.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 22}, "end": {"line": 108, "column": 2}}, "3": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 31}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/bubbleInteraction.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/bubbleInteraction.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 26}, "end": {"line": 157, "column": 2}}, "3": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 35}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/commentLike.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/commentLike.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 20}, "end": {"line": 41, "column": 2}}, "3": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 29}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyContent.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyContent.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 21}, "end": {"line": 105, "column": 2}}, "3": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 30}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyContentRelation.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyContentRelation.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 29}, "end": {"line": 50, "column": 2}}, "3": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 38}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyRecord.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyRecord.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 20}, "end": {"line": 112, "column": 2}}, "3": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 29}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/exercise.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/exercise.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 17}, "end": {"line": 98, "column": 2}}, "3": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 26}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/featureFlag.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/featureFlag.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 20}, "end": {"line": 85, "column": 2}}, "3": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 29}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/index.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/index.js", "statementMap": {"0": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 89}}, "1": {"start": {"line": 14, "column": 15}, "end": {"line": 14, "column": 41}}, "2": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/insight.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/insight.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 16}, "end": {"line": 89, "column": 2}}, "3": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 25}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/learningActivity.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/learningActivity.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 10, "column": 25}, "end": {"line": 123, "column": 2}}, "3": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 34}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/learningPlan.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/learningPlan.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 21}, "end": {"line": 142, "column": 2}}, "3": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 30}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/learningTemplate.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/learningTemplate.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 25}, "end": {"line": 118, "column": 2}}, "3": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 34}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/level.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/level.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 14}, "end": {"line": 53, "column": 2}}, "3": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 23}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/note.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/note.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 13}, "end": {"line": 108, "column": 2}}, "3": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 22}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/noteComment.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/noteComment.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 20}, "end": {"line": 93, "column": 2}}, "3": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 29}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/noteLike.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/noteLike.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 17}, "end": {"line": 59, "column": 2}}, "3": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 26}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/notification.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/notification.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 21}, "end": {"line": 78, "column": 2}}, "3": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 30}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/passwordResetToken.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/passwordResetToken.model.js", "statementMap": {"0": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 42}}, "1": {"start": {"line": 7, "column": 0}, "end": {"line": 89, "column": 2}}, "2": {"start": {"line": 8, "column": 29}, "end": {"line": 78, "column": 4}}, "3": {"start": {"line": 80, "column": 2}, "end": {"line": 86, "column": 4}}, "4": {"start": {"line": 82, "column": 4}, "end": {"line": 85, "column": 7}}, "5": {"start": {"line": 88, "column": 2}, "end": {"line": 88, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 17}, "end": {"line": 7, "column": 18}}, "loc": {"start": {"line": 7, "column": 32}, "end": {"line": 89, "column": 1}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 80, "column": 33}, "end": {"line": 80, "column": 34}}, "loc": {"start": {"line": 80, "column": 45}, "end": {"line": 86, "column": 3}}, "line": 80}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/planTag.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/planTag.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 16}, "end": {"line": 65, "column": 2}}, "3": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 25}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/systemConfig.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/systemConfig.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 21}, "end": {"line": 71, "column": 2}}, "3": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 30}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/tag.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/tag.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 12}, "end": {"line": 112, "column": 2}}, "3": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 21}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/tagCategory.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/tagCategory.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 20}, "end": {"line": 105, "column": 2}}, "3": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 29}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/tagFeedback.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/tagFeedback.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 20}, "end": {"line": 107, "column": 2}}, "3": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 29}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/tagLike.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/tagLike.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 16}, "end": {"line": 59, "column": 2}}, "3": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 25}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/tagSynonym.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/tagSynonym.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 19}, "end": {"line": 52, "column": 2}}, "3": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 28}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/theme.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/theme.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 14}, "end": {"line": 89, "column": 2}}, "3": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 23}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/unified-index.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/unified-index.js", "statementMap": {"0": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 51}}, "1": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 50}}, "2": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 38}}, "3": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 50}}, "4": {"start": {"line": 11, "column": 21}, "end": {"line": 11, "column": 52}}, "5": {"start": {"line": 12, "column": 29}, "end": {"line": 12, "column": 68}}, "6": {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 50}}, "7": {"start": {"line": 14, "column": 17}, "end": {"line": 14, "column": 44}}, "8": {"start": {"line": 15, "column": 16}, "end": {"line": 15, "column": 42}}, "9": {"start": {"line": 16, "column": 25}, "end": {"line": 16, "column": 60}}, "10": {"start": {"line": 17, "column": 21}, "end": {"line": 17, "column": 52}}, "11": {"start": {"line": 18, "column": 25}, "end": {"line": 18, "column": 60}}, "12": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 38}}, "13": {"start": {"line": 20, "column": 13}, "end": {"line": 20, "column": 36}}, "14": {"start": {"line": 21, "column": 20}, "end": {"line": 21, "column": 50}}, "15": {"start": {"line": 22, "column": 17}, "end": {"line": 22, "column": 44}}, "16": {"start": {"line": 23, "column": 16}, "end": {"line": 23, "column": 42}}, "17": {"start": {"line": 24, "column": 12}, "end": {"line": 24, "column": 34}}, "18": {"start": {"line": 25, "column": 20}, "end": {"line": 25, "column": 50}}, "19": {"start": {"line": 26, "column": 20}, "end": {"line": 26, "column": 50}}, "20": {"start": {"line": 27, "column": 16}, "end": {"line": 27, "column": 42}}, "21": {"start": {"line": 28, "column": 19}, "end": {"line": 28, "column": 48}}, "22": {"start": {"line": 29, "column": 20}, "end": {"line": 29, "column": 50}}, "23": {"start": {"line": 30, "column": 14}, "end": {"line": 30, "column": 38}}, "24": {"start": {"line": 31, "column": 13}, "end": {"line": 31, "column": 36}}, "25": {"start": {"line": 32, "column": 24}, "end": {"line": 32, "column": 58}}, "26": {"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 46}}, "27": {"start": {"line": 34, "column": 28}, "end": {"line": 34, "column": 66}}, "28": {"start": {"line": 35, "column": 19}, "end": {"line": 35, "column": 48}}, "29": {"start": {"line": 36, "column": 26}, "end": {"line": 36, "column": 62}}, "30": {"start": {"line": 37, "column": 32}, "end": {"line": 37, "column": 74}}, "31": {"start": {"line": 38, "column": 20}, "end": {"line": 38, "column": 50}}, "32": {"start": {"line": 43, "column": 0}, "end": {"line": 46, "column": 3}}, "33": {"start": {"line": 47, "column": 0}, "end": {"line": 50, "column": 3}}, "34": {"start": {"line": 53, "column": 0}, "end": {"line": 56, "column": 3}}, "35": {"start": {"line": 57, "column": 0}, "end": {"line": 60, "column": 3}}, "36": {"start": {"line": 63, "column": 0}, "end": {"line": 66, "column": 3}}, "37": {"start": {"line": 67, "column": 0}, "end": {"line": 70, "column": 3}}, "38": {"start": {"line": 73, "column": 0}, "end": {"line": 76, "column": 3}}, "39": {"start": {"line": 77, "column": 0}, "end": {"line": 80, "column": 3}}, "40": {"start": {"line": 83, "column": 0}, "end": {"line": 86, "column": 3}}, "41": {"start": {"line": 87, "column": 0}, "end": {"line": 90, "column": 3}}, "42": {"start": {"line": 93, "column": 0}, "end": {"line": 96, "column": 3}}, "43": {"start": {"line": 97, "column": 0}, "end": {"line": 100, "column": 3}}, "44": {"start": {"line": 103, "column": 0}, "end": {"line": 106, "column": 3}}, "45": {"start": {"line": 107, "column": 0}, "end": {"line": 110, "column": 3}}, "46": {"start": {"line": 113, "column": 0}, "end": {"line": 116, "column": 3}}, "47": {"start": {"line": 117, "column": 0}, "end": {"line": 120, "column": 3}}, "48": {"start": {"line": 123, "column": 0}, "end": {"line": 128, "column": 3}}, "49": {"start": {"line": 129, "column": 0}, "end": {"line": 134, "column": 3}}, "50": {"start": {"line": 137, "column": 0}, "end": {"line": 140, "column": 3}}, "51": {"start": {"line": 141, "column": 0}, "end": {"line": 144, "column": 3}}, "52": {"start": {"line": 146, "column": 0}, "end": {"line": 149, "column": 3}}, "53": {"start": {"line": 150, "column": 0}, "end": {"line": 153, "column": 3}}, "54": {"start": {"line": 156, "column": 0}, "end": {"line": 159, "column": 3}}, "55": {"start": {"line": 160, "column": 0}, "end": {"line": 163, "column": 3}}, "56": {"start": {"line": 166, "column": 0}, "end": {"line": 169, "column": 3}}, "57": {"start": {"line": 170, "column": 0}, "end": {"line": 173, "column": 3}}, "58": {"start": {"line": 176, "column": 0}, "end": {"line": 179, "column": 3}}, "59": {"start": {"line": 180, "column": 0}, "end": {"line": 183, "column": 3}}, "60": {"start": {"line": 186, "column": 0}, "end": {"line": 189, "column": 3}}, "61": {"start": {"line": 190, "column": 0}, "end": {"line": 193, "column": 3}}, "62": {"start": {"line": 196, "column": 0}, "end": {"line": 199, "column": 3}}, "63": {"start": {"line": 200, "column": 0}, "end": {"line": 203, "column": 3}}, "64": {"start": {"line": 206, "column": 0}, "end": {"line": 209, "column": 3}}, "65": {"start": {"line": 210, "column": 0}, "end": {"line": 213, "column": 3}}, "66": {"start": {"line": 216, "column": 0}, "end": {"line": 219, "column": 3}}, "67": {"start": {"line": 220, "column": 0}, "end": {"line": 223, "column": 3}}, "68": {"start": {"line": 226, "column": 0}, "end": {"line": 229, "column": 3}}, "69": {"start": {"line": 230, "column": 0}, "end": {"line": 233, "column": 3}}, "70": {"start": {"line": 236, "column": 0}, "end": {"line": 239, "column": 3}}, "71": {"start": {"line": 240, "column": 0}, "end": {"line": 243, "column": 3}}, "72": {"start": {"line": 246, "column": 0}, "end": {"line": 251, "column": 3}}, "73": {"start": {"line": 252, "column": 0}, "end": {"line": 257, "column": 3}}, "74": {"start": {"line": 260, "column": 0}, "end": {"line": 265, "column": 3}}, "75": {"start": {"line": 266, "column": 0}, "end": {"line": 271, "column": 3}}, "76": {"start": {"line": 274, "column": 0}, "end": {"line": 279, "column": 3}}, "77": {"start": {"line": 280, "column": 0}, "end": {"line": 285, "column": 3}}, "78": {"start": {"line": 288, "column": 0}, "end": {"line": 321, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/user.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/user.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 13}, "end": {"line": 103, "column": 2}}, "3": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 22}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/userAchievement.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/userAchievement.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 24}, "end": {"line": 61, "column": 2}}, "3": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 33}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/userBadge.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/userBadge.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 18}, "end": {"line": 56, "column": 2}}, "3": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 27}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/userContentProgress.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/userContentProgress.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 28}, "end": {"line": 84, "column": 2}}, "3": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 37}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/userFeatureAccess.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/userFeatureAccess.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 26}, "end": {"line": 78, "column": 2}}, "3": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 35}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/userFollow.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/userFollow.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 8, "column": 19}, "end": {"line": 68, "column": 2}}, "3": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 28}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/userLearningStats.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/userLearningStats.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 26}, "end": {"line": 95, "column": 2}}, "3": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 35}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/userNotificationSetting.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/userNotificationSetting.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 32}, "end": {"line": 41, "column": 2}}, "3": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 41}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/models/userSetting.model.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/models/userSetting.model.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 4, "column": 20}, "end": {"line": 42, "column": 2}}, "3": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 29}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/ai.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/ai.routes.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 17}, "end": {"line": 2, "column": 45}}, "2": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 60}}, "3": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 68}}, "4": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 68}}, "5": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 31}}, "6": {"start": {"line": 81, "column": 0}, "end": {"line": 85, "column": 2}}, "7": {"start": {"line": 114, "column": 0}, "end": {"line": 118, "column": 2}}, "8": {"start": {"line": 212, "column": 0}, "end": {"line": 221, "column": 2}}, "9": {"start": {"line": 310, "column": 0}, "end": {"line": 318, "column": 2}}, "10": {"start": {"line": 320, "column": 0}, "end": {"line": 320, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/batchOperation.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/batchOperation.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 31}}, "2": {"start": {"line": 7, "column": 33}, "end": {"line": 7, "column": 84}}, "3": {"start": {"line": 8, "column": 41}, "end": {"line": 8, "column": 70}}, "4": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 109}}, "5": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 102}}, "6": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 111}}, "7": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 104}}, "8": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 95}}, "9": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 88}}, "10": {"start": {"line": 269, "column": 0}, "end": {"line": 269, "column": 101}}, "11": {"start": {"line": 304, "column": 0}, "end": {"line": 304, "column": 94}}, "12": {"start": {"line": 306, "column": 0}, "end": {"line": 306, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/cleanup.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/cleanup.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 31}}, "2": {"start": {"line": 7, "column": 26}, "end": {"line": 7, "column": 70}}, "3": {"start": {"line": 8, "column": 41}, "end": {"line": 8, "column": 70}}, "4": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 88}}, "5": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 91}}, "6": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 80}}, "7": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 88}}, "8": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 86}}, "9": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/dailyContentV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/dailyContentV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 31}}, "2": {"start": {"line": 7, "column": 31}, "end": {"line": 7, "column": 82}}, "3": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 54}}, "4": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 82}}, "5": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 72}}, "6": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 71}}, "7": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 90}}, "8": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 81}}, "9": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 93}}, "10": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/deadLetterQueue.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/deadLetterQueue.routes.js", "statementMap": {"0": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 34}}, "1": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 31}}, "2": {"start": {"line": 6, "column": 34}, "end": {"line": 6, "column": 86}}, "3": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 69}}, "4": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 28}}, "5": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 62}}, "6": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 69}}, "7": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 78}}, "8": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 82}}, "9": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/errorMonitor.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/errorMonitor.routes.js", "statementMap": {"0": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 34}}, "1": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 31}}, "2": {"start": {"line": 6, "column": 31}, "end": {"line": 6, "column": 80}}, "3": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 68}}, "4": {"start": {"line": 9, "column": 0}, "end": {"line": 13, "column": 2}}, "5": {"start": {"line": 15, "column": 0}, "end": {"line": 19, "column": 2}}, "6": {"start": {"line": 21, "column": 0}, "end": {"line": 25, "column": 2}}, "7": {"start": {"line": 27, "column": 0}, "end": {"line": 31, "column": 2}}, "8": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/exerciseV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/exerciseV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 52}}, "2": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 74}}, "3": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 68}}, "4": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 68}}, "5": {"start": {"line": 11, "column": 15}, "end": {"line": 11, "column": 31}}, "6": {"start": {"line": 13, "column": 0}, "end": {"line": 24, "column": 2}}, "7": {"start": {"line": 26, "column": 0}, "end": {"line": 30, "column": 2}}, "8": {"start": {"line": 32, "column": 0}, "end": {"line": 47, "column": 2}}, "9": {"start": {"line": 49, "column": 0}, "end": {"line": 63, "column": 2}}, "10": {"start": {"line": 65, "column": 0}, "end": {"line": 69, "column": 2}}, "11": {"start": {"line": 76, "column": 0}, "end": {"line": 80, "column": 2}}, "12": {"start": {"line": 87, "column": 0}, "end": {"line": 91, "column": 2}}, "13": {"start": {"line": 98, "column": 0}, "end": {"line": 107, "column": 2}}, "14": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/health.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/health.routes.js", "statementMap": {"0": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 34}}, "1": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 9, "column": 11}, "end": {"line": 9, "column": 24}}, "3": {"start": {"line": 10, "column": 26}, "end": {"line": 10, "column": 55}}, "4": {"start": {"line": 11, "column": 24}, "end": {"line": 11, "column": 50}}, "5": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 42}}, "6": {"start": {"line": 13, "column": 15}, "end": {"line": 13, "column": 42}}, "7": {"start": {"line": 14, "column": 25}, "end": {"line": 14, "column": 68}}, "8": {"start": {"line": 78, "column": 0}, "end": {"line": 151, "column": 3}}, "9": {"start": {"line": 79, "column": 2}, "end": {"line": 150, "column": 3}}, "10": {"start": {"line": 81, "column": 19}, "end": {"line": 81, "column": 33}}, "11": {"start": {"line": 82, "column": 4}, "end": {"line": 90, "column": 5}}, "12": {"start": {"line": 83, "column": 25}, "end": {"line": 83, "column": 40}}, "13": {"start": {"line": 84, "column": 6}, "end": {"line": 87, "column": 7}}, "14": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 43}}, "15": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 31}}, "16": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 55}}, "17": {"start": {"line": 93, "column": 22}, "end": {"line": 93, "column": 36}}, "18": {"start": {"line": 94, "column": 4}, "end": {"line": 101, "column": 5}}, "19": {"start": {"line": 95, "column": 6}, "end": {"line": 98, "column": 7}}, "20": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 33}}, "21": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 34}}, "22": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 57}}, "23": {"start": {"line": 104, "column": 23}, "end": {"line": 118, "column": 5}}, "24": {"start": {"line": 121, "column": 22}, "end": {"line": 121, "column": 85}}, "25": {"start": {"line": 122, "column": 23}, "end": {"line": 122, "column": 44}}, "26": {"start": {"line": 125, "column": 21}, "end": {"line": 135, "column": 5}}, "27": {"start": {"line": 138, "column": 4}, "end": {"line": 140, "column": 5}}, "28": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 35}}, "29": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 42}}, "30": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 45}}, "31": {"start": {"line": 145, "column": 4}, "end": {"line": 149, "column": 7}}, "32": {"start": {"line": 172, "column": 0}, "end": {"line": 174, "column": 3}}, "33": {"start": {"line": 173, "column": 2}, "end": {"line": 173, "column": 41}}, "34": {"start": {"line": 208, "column": 0}, "end": {"line": 237, "column": 3}}, "35": {"start": {"line": 209, "column": 2}, "end": {"line": 236, "column": 3}}, "36": {"start": {"line": 211, "column": 22}, "end": {"line": 211, "column": 27}}, "37": {"start": {"line": 212, "column": 4}, "end": {"line": 220, "column": 5}}, "38": {"start": {"line": 213, "column": 25}, "end": {"line": 213, "column": 40}}, "39": {"start": {"line": 214, "column": 6}, "end": {"line": 217, "column": 7}}, "40": {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 43}}, "41": {"start": {"line": 216, "column": 8}, "end": {"line": 216, "column": 27}}, "42": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": 55}}, "43": {"start": {"line": 223, "column": 20}, "end": {"line": 223, "column": 70}}, "44": {"start": {"line": 224, "column": 23}, "end": {"line": 224, "column": 42}}, "45": {"start": {"line": 226, "column": 4}, "end": {"line": 229, "column": 7}}, "46": {"start": {"line": 231, "column": 4}, "end": {"line": 231, "column": 45}}, "47": {"start": {"line": 232, "column": 4}, "end": {"line": 235, "column": 7}}, "48": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 62}}, "49": {"start": {"line": 267, "column": 0}, "end": {"line": 267, "column": 56}}, "50": {"start": {"line": 282, "column": 0}, "end": {"line": 282, "column": 58}}, "51": {"start": {"line": 297, "column": 0}, "end": {"line": 297, "column": 54}}, "52": {"start": {"line": 299, "column": 0}, "end": {"line": 299, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 78, "column": 16}, "end": {"line": 78, "column": 17}}, "loc": {"start": {"line": 78, "column": 36}, "end": {"line": 151, "column": 1}}, "line": 78}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 172, "column": 24}, "end": {"line": 172, "column": 25}}, "loc": {"start": {"line": 172, "column": 38}, "end": {"line": 174, "column": 1}}, "line": 172}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 208, "column": 25}, "end": {"line": 208, "column": 26}}, "loc": {"start": {"line": 208, "column": 45}, "end": {"line": 237, "column": 1}}, "line": 208}}, "branchMap": {"0": {"loc": {"start": {"line": 84, "column": 6}, "end": {"line": 87, "column": 7}}, "type": "if", "locations": [{"start": {"line": 84, "column": 6}, "end": {"line": 87, "column": 7}}, {"start": {}, "end": {}}], "line": 84}, "1": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 98, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 98, "column": 7}}, {"start": {}, "end": {}}], "line": 95}, "2": {"loc": {"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": 21}}, {"start": {"line": 95, "column": 25}, "end": {"line": 95, "column": 43}}], "line": 95}, "3": {"loc": {"start": {"line": 121, "column": 22}, "end": {"line": 121, "column": 85}}, "type": "binary-expr", "locations": [{"start": {"line": 121, "column": 22}, "end": {"line": 121, "column": 57}}, {"start": {"line": 121, "column": 61}, "end": {"line": 121, "column": 85}}], "line": 121}, "4": {"loc": {"start": {"line": 122, "column": 23}, "end": {"line": 122, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 122, "column": 35}, "end": {"line": 122, "column": 38}}, {"start": {"line": 122, "column": 41}, "end": {"line": 122, "column": 44}}], "line": 122}, "5": {"loc": {"start": {"line": 126, "column": 14}, "end": {"line": 126, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 126, "column": 26}, "end": {"line": 126, "column": 30}}, {"start": {"line": 126, "column": 33}, "end": {"line": 126, "column": 40}}], "line": 126}, "6": {"loc": {"start": {"line": 127, "column": 15}, "end": {"line": 127, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 127, "column": 15}, "end": {"line": 127, "column": 33}}, {"start": {"line": 127, "column": 37}, "end": {"line": 127, "column": 44}}], "line": 127}, "7": {"loc": {"start": {"line": 138, "column": 4}, "end": {"line": 140, "column": 5}}, "type": "if", "locations": [{"start": {"line": 138, "column": 4}, "end": {"line": 140, "column": 5}}, {"start": {}, "end": {}}], "line": 138}, "8": {"loc": {"start": {"line": 214, "column": 6}, "end": {"line": 217, "column": 7}}, "type": "if", "locations": [{"start": {"line": 214, "column": 6}, "end": {"line": 217, "column": 7}}, {"start": {}, "end": {}}], "line": 214}, "9": {"loc": {"start": {"line": 223, "column": 20}, "end": {"line": 223, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 223, "column": 20}, "end": {"line": 223, "column": 55}}, {"start": {"line": 223, "column": 59}, "end": {"line": 223, "column": 70}}], "line": 223}, "10": {"loc": {"start": {"line": 224, "column": 23}, "end": {"line": 224, "column": 42}}, "type": "cond-expr", "locations": [{"start": {"line": 224, "column": 33}, "end": {"line": 224, "column": 36}}, {"start": {"line": 224, "column": 39}, "end": {"line": 224, "column": 42}}], "line": 224}, "11": {"loc": {"start": {"line": 227, "column": 14}, "end": {"line": 227, "column": 38}}, "type": "cond-expr", "locations": [{"start": {"line": 227, "column": 24}, "end": {"line": 227, "column": 28}}, {"start": {"line": 227, "column": 31}, "end": {"line": 227, "column": 38}}], "line": 227}, "12": {"loc": {"start": {"line": 228, "column": 15}, "end": {"line": 228, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 228, "column": 25}, "end": {"line": 228, "column": 40}}, {"start": {"line": 228, "column": 43}, "end": {"line": 228, "column": 62}}], "line": 228}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/insightV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/insightV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 52}}, "2": {"start": {"line": 7, "column": 26}, "end": {"line": 7, "column": 72}}, "3": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 68}}, "4": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 68}}, "5": {"start": {"line": 11, "column": 15}, "end": {"line": 11, "column": 31}}, "6": {"start": {"line": 13, "column": 0}, "end": {"line": 22, "column": 2}}, "7": {"start": {"line": 24, "column": 0}, "end": {"line": 28, "column": 2}}, "8": {"start": {"line": 30, "column": 0}, "end": {"line": 41, "column": 2}}, "9": {"start": {"line": 43, "column": 0}, "end": {"line": 53, "column": 2}}, "10": {"start": {"line": 55, "column": 0}, "end": {"line": 59, "column": 2}}, "11": {"start": {"line": 66, "column": 0}, "end": {"line": 70, "column": 2}}, "12": {"start": {"line": 77, "column": 0}, "end": {"line": 81, "column": 2}}, "13": {"start": {"line": 88, "column": 0}, "end": {"line": 97, "column": 2}}, "14": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/learningPlanV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/learningPlanV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 31}}, "2": {"start": {"line": 7, "column": 31}, "end": {"line": 7, "column": 82}}, "3": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 54}}, "4": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 73}}, "5": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 67}}, "6": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 69}}, "7": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 98}}, "8": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 100}}, "9": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 87}}, "10": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 78}}, "11": {"start": {"line": 267, "column": 0}, "end": {"line": 267, "column": 77}}, "12": {"start": {"line": 269, "column": 0}, "end": {"line": 269, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/mockData.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/mockData.routes.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 31}}, "2": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 68}}, "3": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 72}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 10, "column": 2}}, "5": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/noteV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/noteV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 52}}, "2": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 66}}, "3": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 68}}, "4": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 68}}, "5": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 73}}, "6": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 31}}, "7": {"start": {"line": 14, "column": 0}, "end": {"line": 23, "column": 2}}, "8": {"start": {"line": 25, "column": 0}, "end": {"line": 34, "column": 2}}, "9": {"start": {"line": 36, "column": 0}, "end": {"line": 40, "column": 2}}, "10": {"start": {"line": 42, "column": 0}, "end": {"line": 56, "column": 2}}, "11": {"start": {"line": 58, "column": 0}, "end": {"line": 72, "column": 2}}, "12": {"start": {"line": 74, "column": 0}, "end": {"line": 78, "column": 2}}, "13": {"start": {"line": 85, "column": 0}, "end": {"line": 89, "column": 2}}, "14": {"start": {"line": 96, "column": 0}, "end": {"line": 100, "column": 2}}, "15": {"start": {"line": 107, "column": 0}, "end": {"line": 116, "column": 2}}, "16": {"start": {"line": 118, "column": 0}, "end": {"line": 122, "column": 2}}, "17": {"start": {"line": 124, "column": 0}, "end": {"line": 133, "column": 2}}, "18": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/squareV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/squareV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 18}, "end": {"line": 6, "column": 46}}, "2": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 70}}, "3": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 68}}, "4": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 68}}, "5": {"start": {"line": 11, "column": 15}, "end": {"line": 11, "column": 31}}, "6": {"start": {"line": 13, "column": 0}, "end": {"line": 23, "column": 2}}, "7": {"start": {"line": 25, "column": 0}, "end": {"line": 28, "column": 2}}, "8": {"start": {"line": 30, "column": 0}, "end": {"line": 37, "column": 2}}, "9": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/statisticsMonitor.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/statisticsMonitor.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 18}, "end": {"line": 6, "column": 46}}, "2": {"start": {"line": 7, "column": 44}, "end": {"line": 7, "column": 85}}, "3": {"start": {"line": 8, "column": 21}, "end": {"line": 8, "column": 68}}, "4": {"start": {"line": 9, "column": 36}, "end": {"line": 9, "column": 90}}, "5": {"start": {"line": 11, "column": 15}, "end": {"line": 11, "column": 31}}, "6": {"start": {"line": 64, "column": 0}, "end": {"line": 68, "column": 2}}, "7": {"start": {"line": 120, "column": 0}, "end": {"line": 124, "column": 2}}, "8": {"start": {"line": 183, "column": 0}, "end": {"line": 187, "column": 2}}, "9": {"start": {"line": 242, "column": 0}, "end": {"line": 251, "column": 2}}, "10": {"start": {"line": 286, "column": 0}, "end": {"line": 290, "column": 2}}, "11": {"start": {"line": 292, "column": 0}, "end": {"line": 292, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/statisticsV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/statisticsV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 52}}, "2": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 68}}, "3": {"start": {"line": 8, "column": 21}, "end": {"line": 8, "column": 68}}, "4": {"start": {"line": 9, "column": 36}, "end": {"line": 9, "column": 90}}, "5": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 62}}, "6": {"start": {"line": 14, "column": 26}, "end": {"line": 14, "column": 74}}, "7": {"start": {"line": 17, "column": 31}, "end": {"line": 17, "column": 99}}, "8": {"start": {"line": 19, "column": 15}, "end": {"line": 19, "column": 31}}, "9": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 40}}, "10": {"start": {"line": 87, "column": 0}, "end": {"line": 91, "column": 2}}, "11": {"start": {"line": 188, "column": 0}, "end": {"line": 199, "column": 2}}, "12": {"start": {"line": 273, "column": 0}, "end": {"line": 303, "column": 2}}, "13": {"start": {"line": 417, "column": 0}, "end": {"line": 448, "column": 2}}, "14": {"start": {"line": 484, "column": 0}, "end": {"line": 488, "column": 2}}, "15": {"start": {"line": 531, "column": 0}, "end": {"line": 539, "column": 2}}, "16": {"start": {"line": 541, "column": 0}, "end": {"line": 541, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/tagV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/tagV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 18}, "end": {"line": 6, "column": 46}}, "2": {"start": {"line": 7, "column": 24}, "end": {"line": 7, "column": 66}}, "3": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 68}}, "4": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 68}}, "5": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 73}}, "6": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 31}}, "7": {"start": {"line": 19, "column": 0}, "end": {"line": 24, "column": 2}}, "8": {"start": {"line": 31, "column": 0}, "end": {"line": 36, "column": 2}}, "9": {"start": {"line": 43, "column": 0}, "end": {"line": 52, "column": 2}}, "10": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/testData.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/testData.routes.js", "statementMap": {"0": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 34}}, "1": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 31}}, "2": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 47}}, "3": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 38}}, "4": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/routes/themeV2.routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/routes/themeV2.routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 31}}, "2": {"start": {"line": 7, "column": 24}, "end": {"line": 7, "column": 68}}, "3": {"start": {"line": 8, "column": 41}, "end": {"line": 8, "column": 70}}, "4": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 43}}, "5": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 49}}, "6": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 97}}, "7": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 88}}, "8": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 87}}, "9": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/services/learningPlanV2.service.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/services/learningPlanV2.service.js", "statementMap": {"0": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "1": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 62}}, "2": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 35}}, "3": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 51}}, "4": {"start": {"line": 10, "column": 66}, "end": {"line": 10, "column": 86}}, "5": {"start": {"line": 11, "column": 24}, "end": {"line": 11, "column": 56}}, "6": {"start": {"line": 12, "column": 36}, "end": {"line": 12, "column": 64}}, "7": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 91}}, "8": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 73}}, "9": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 62}}, "10": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 37}}, "11": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 23}}, "12": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 19}}, "13": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 27}}, "14": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 31}}, "15": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 37}}, "16": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 25}}, "17": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 27}}, "18": {"start": {"line": 34, "column": 15}, "end": {"line": 34, "column": 27}}, "19": {"start": {"line": 36, "column": 4}, "end": {"line": 43, "column": 5}}, "20": {"start": {"line": 38, "column": 21}, "end": {"line": 38, "column": 46}}, "21": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 37}}, "22": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 50}}, "23": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 18}}, "24": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 25}}, "25": {"start": {"line": 52, "column": 14}, "end": {"line": 52, "column": 25}}, "26": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 44}}, "27": {"start": {"line": 53, "column": 33}, "end": {"line": 53, "column": 44}}, "28": {"start": {"line": 56, "column": 17}, "end": {"line": 56, "column": 53}}, "29": {"start": {"line": 59, "column": 4}, "end": {"line": 65, "column": 7}}, "30": {"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, "31": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 55}}, "32": {"start": {"line": 62, "column": 13}, "end": {"line": 64, "column": 7}}, "33": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 61}}, "34": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 16}}, "35": {"start": {"line": 81, "column": 4}, "end": {"line": 145, "column": 5}}, "36": {"start": {"line": 82, "column": 21}, "end": {"line": 82, "column": 42}}, "37": {"start": {"line": 83, "column": 20}, "end": {"line": 83, "column": 28}}, "38": {"start": {"line": 86, "column": 20}, "end": {"line": 86, "column": 39}}, "39": {"start": {"line": 89, "column": 6}, "end": {"line": 91, "column": 7}}, "40": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 30}}, "41": {"start": {"line": 94, "column": 23}, "end": {"line": 94, "column": 35}}, "42": {"start": {"line": 95, "column": 6}, "end": {"line": 99, "column": 7}}, "43": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 34}}, "44": {"start": {"line": 97, "column": 13}, "end": {"line": 99, "column": 7}}, "45": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 29}}, "46": {"start": {"line": 102, "column": 24}, "end": {"line": 102, "column": 74}}, "47": {"start": {"line": 105, "column": 30}, "end": {"line": 117, "column": 8}}, "48": {"start": {"line": 120, "column": 20}, "end": {"line": 131, "column": 9}}, "49": {"start": {"line": 120, "column": 38}, "end": {"line": 131, "column": 7}}, "50": {"start": {"line": 133, "column": 6}, "end": {"line": 141, "column": 8}}, "51": {"start": {"line": 143, "column": 6}, "end": {"line": 143, "column": 58}}, "52": {"start": {"line": 144, "column": 6}, "end": {"line": 144, "column": 86}}, "53": {"start": {"line": 154, "column": 4}, "end": {"line": 176, "column": 5}}, "54": {"start": {"line": 156, "column": 19}, "end": {"line": 166, "column": 8}}, "55": {"start": {"line": 168, "column": 6}, "end": {"line": 170, "column": 7}}, "56": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 20}}, "57": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 18}}, "58": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": 58}}, "59": {"start": {"line": 175, "column": 6}, "end": {"line": 175, "column": 86}}, "60": {"start": {"line": 186, "column": 4}, "end": {"line": 236, "column": 5}}, "61": {"start": {"line": 188, "column": 19}, "end": {"line": 201, "column": 8}}, "62": {"start": {"line": 203, "column": 6}, "end": {"line": 205, "column": 7}}, "63": {"start": {"line": 204, "column": 8}, "end": {"line": 204, "column": 35}}, "64": {"start": {"line": 208, "column": 6}, "end": {"line": 210, "column": 7}}, "65": {"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": 37}}, "66": {"start": {"line": 212, "column": 6}, "end": {"line": 212, "column": 50}}, "67": {"start": {"line": 215, "column": 23}, "end": {"line": 225, "column": 7}}, "68": {"start": {"line": 227, "column": 6}, "end": {"line": 227, "column": 53}}, "69": {"start": {"line": 229, "column": 6}, "end": {"line": 232, "column": 8}}, "70": {"start": {"line": 234, "column": 6}, "end": {"line": 234, "column": 56}}, "71": {"start": {"line": 235, "column": 6}, "end": {"line": 235, "column": 18}}, "72": {"start": {"line": 246, "column": 4}, "end": {"line": 311, "column": 5}}, "73": {"start": {"line": 248, "column": 19}, "end": {"line": 257, "column": 8}}, "74": {"start": {"line": 259, "column": 6}, "end": {"line": 261, "column": 7}}, "75": {"start": {"line": 260, "column": 8}, "end": {"line": 260, "column": 68}}, "76": {"start": {"line": 264, "column": 28}, "end": {"line": 267, "column": 8}}, "77": {"start": {"line": 270, "column": 28}, "end": {"line": 289, "column": 7}}, "78": {"start": {"line": 292, "column": 32}, "end": {"line": 299, "column": 9}}, "79": {"start": {"line": 292, "column": 62}, "end": {"line": 299, "column": 7}}, "80": {"start": {"line": 301, "column": 6}, "end": {"line": 304, "column": 8}}, "81": {"start": {"line": 306, "column": 6}, "end": {"line": 306, "column": 56}}, "82": {"start": {"line": 307, "column": 6}, "end": {"line": 309, "column": 7}}, "83": {"start": {"line": 308, "column": 8}, "end": {"line": 308, "column": 20}}, "84": {"start": {"line": 310, "column": 6}, "end": {"line": 310, "column": 84}}, "85": {"start": {"line": 322, "column": 24}, "end": {"line": 322, "column": 58}}, "86": {"start": {"line": 324, "column": 4}, "end": {"line": 391, "column": 5}}, "87": {"start": {"line": 333, "column": 10}, "end": {"line": 333, "column": 18}}, "88": {"start": {"line": 336, "column": 20}, "end": {"line": 339, "column": 8}}, "89": {"start": {"line": 341, "column": 6}, "end": {"line": 344, "column": 7}}, "90": {"start": {"line": 342, "column": 8}, "end": {"line": 342, "column": 37}}, "91": {"start": {"line": 343, "column": 8}, "end": {"line": 343, "column": 63}}, "92": {"start": {"line": 347, "column": 19}, "end": {"line": 356, "column": 25}}, "93": {"start": {"line": 359, "column": 6}, "end": {"line": 378, "column": 7}}, "94": {"start": {"line": 361, "column": 28}, "end": {"line": 365, "column": 10}}, "95": {"start": {"line": 362, "column": 10}, "end": {"line": 364, "column": 29}}, "96": {"start": {"line": 368, "column": 8}, "end": {"line": 377, "column": 11}}, "97": {"start": {"line": 369, "column": 10}, "end": {"line": 376, "column": 29}}, "98": {"start": {"line": 380, "column": 6}, "end": {"line": 380, "column": 33}}, "99": {"start": {"line": 381, "column": 6}, "end": {"line": 381, "column": 47}}, "100": {"start": {"line": 383, "column": 6}, "end": {"line": 383, "column": 18}}, "101": {"start": {"line": 385, "column": 6}, "end": {"line": 385, "column": 35}}, "102": {"start": {"line": 386, "column": 6}, "end": {"line": 386, "column": 54}}, "103": {"start": {"line": 387, "column": 6}, "end": {"line": 389, "column": 7}}, "104": {"start": {"line": 388, "column": 8}, "end": {"line": 388, "column": 20}}, "105": {"start": {"line": 390, "column": 6}, "end": {"line": 390, "column": 82}}, "106": {"start": {"line": 401, "column": 4}, "end": {"line": 418, "column": 5}}, "107": {"start": {"line": 403, "column": 19}, "end": {"line": 405, "column": 8}}, "108": {"start": {"line": 407, "column": 6}, "end": {"line": 409, "column": 7}}, "109": {"start": {"line": 408, "column": 8}, "end": {"line": 408, "column": 68}}, "110": {"start": {"line": 411, "column": 6}, "end": {"line": 411, "column": 18}}, "111": {"start": {"line": 413, "column": 6}, "end": {"line": 413, "column": 56}}, "112": {"start": {"line": 414, "column": 6}, "end": {"line": 416, "column": 7}}, "113": {"start": {"line": 415, "column": 8}, "end": {"line": 415, "column": 20}}, "114": {"start": {"line": 417, "column": 6}, "end": {"line": 417, "column": 84}}, "115": {"start": {"line": 429, "column": 4}, "end": {"line": 456, "column": 5}}, "116": {"start": {"line": 431, "column": 19}, "end": {"line": 433, "column": 8}}, "117": {"start": {"line": 435, "column": 6}, "end": {"line": 437, "column": 7}}, "118": {"start": {"line": 436, "column": 8}, "end": {"line": 436, "column": 68}}, "119": {"start": {"line": 440, "column": 27}, "end": {"line": 440, "column": 29}}, "120": {"start": {"line": 441, "column": 6}, "end": {"line": 441, "column": 73}}, "121": {"start": {"line": 441, "column": 30}, "end": {"line": 441, "column": 73}}, "122": {"start": {"line": 442, "column": 6}, "end": {"line": 442, "column": 107}}, "123": {"start": {"line": 442, "column": 41}, "end": {"line": 442, "column": 107}}, "124": {"start": {"line": 443, "column": 6}, "end": {"line": 443, "column": 104}}, "125": {"start": {"line": 443, "column": 40}, "end": {"line": 443, "column": 104}}, "126": {"start": {"line": 444, "column": 6}, "end": {"line": 444, "column": 92}}, "127": {"start": {"line": 444, "column": 36}, "end": {"line": 444, "column": 92}}, "128": {"start": {"line": 447, "column": 6}, "end": {"line": 447, "column": 38}}, "129": {"start": {"line": 449, "column": 6}, "end": {"line": 449, "column": 18}}, "130": {"start": {"line": 451, "column": 6}, "end": {"line": 451, "column": 54}}, "131": {"start": {"line": 452, "column": 6}, "end": {"line": 454, "column": 7}}, "132": {"start": {"line": 453, "column": 8}, "end": {"line": 453, "column": 20}}, "133": {"start": {"line": 455, "column": 6}, "end": {"line": 455, "column": 82}}, "134": {"start": {"line": 467, "column": 4}, "end": {"line": 487, "column": 5}}, "135": {"start": {"line": 469, "column": 19}, "end": {"line": 471, "column": 8}}, "136": {"start": {"line": 473, "column": 6}, "end": {"line": 475, "column": 7}}, "137": {"start": {"line": 474, "column": 8}, "end": {"line": 474, "column": 68}}, "138": {"start": {"line": 478, "column": 6}, "end": {"line": 478, "column": 27}}, "139": {"start": {"line": 480, "column": 6}, "end": {"line": 480, "column": 18}}, "140": {"start": {"line": 482, "column": 6}, "end": {"line": 482, "column": 54}}, "141": {"start": {"line": 483, "column": 6}, "end": {"line": 485, "column": 7}}, "142": {"start": {"line": 484, "column": 8}, "end": {"line": 484, "column": 20}}, "143": {"start": {"line": 486, "column": 6}, "end": {"line": 486, "column": 82}}, "144": {"start": {"line": 497, "column": 4}, "end": {"line": 517, "column": 5}}, "145": {"start": {"line": 499, "column": 19}, "end": {"line": 501, "column": 8}}, "146": {"start": {"line": 503, "column": 6}, "end": {"line": 505, "column": 7}}, "147": {"start": {"line": 504, "column": 8}, "end": {"line": 504, "column": 68}}, "148": {"start": {"line": 508, "column": 6}, "end": {"line": 508, "column": 43}}, "149": {"start": {"line": 510, "column": 6}, "end": {"line": 510, "column": 18}}, "150": {"start": {"line": 512, "column": 6}, "end": {"line": 512, "column": 55}}, "151": {"start": {"line": 513, "column": 6}, "end": {"line": 515, "column": 7}}, "152": {"start": {"line": 514, "column": 8}, "end": {"line": 514, "column": 20}}, "153": {"start": {"line": 516, "column": 6}, "end": {"line": 516, "column": 83}}, "154": {"start": {"line": 527, "column": 4}, "end": {"line": 552, "column": 5}}, "155": {"start": {"line": 529, "column": 19}, "end": {"line": 532, "column": 8}}, "156": {"start": {"line": 534, "column": 6}, "end": {"line": 536, "column": 7}}, "157": {"start": {"line": 535, "column": 8}, "end": {"line": 535, "column": 68}}, "158": {"start": {"line": 538, "column": 6}, "end": {"line": 540, "column": 7}}, "159": {"start": {"line": 539, "column": 8}, "end": {"line": 539, "column": 68}}, "160": {"start": {"line": 543, "column": 6}, "end": {"line": 543, "column": 27}}, "161": {"start": {"line": 545, "column": 6}, "end": {"line": 545, "column": 18}}, "162": {"start": {"line": 547, "column": 6}, "end": {"line": 547, "column": 54}}, "163": {"start": {"line": 548, "column": 6}, "end": {"line": 550, "column": 7}}, "164": {"start": {"line": 549, "column": 8}, "end": {"line": 549, "column": 20}}, "165": {"start": {"line": 551, "column": 6}, "end": {"line": 551, "column": 82}}, "166": {"start": {"line": 563, "column": 4}, "end": {"line": 609, "column": 5}}, "167": {"start": {"line": 564, "column": 21}, "end": {"line": 564, "column": 42}}, "168": {"start": {"line": 565, "column": 20}, "end": {"line": 565, "column": 28}}, "169": {"start": {"line": 568, "column": 30}, "end": {"line": 584, "column": 8}}, "170": {"start": {"line": 587, "column": 20}, "end": {"line": 595, "column": 9}}, "171": {"start": {"line": 587, "column": 38}, "end": {"line": 595, "column": 7}}, "172": {"start": {"line": 597, "column": 6}, "end": {"line": 605, "column": 8}}, "173": {"start": {"line": 607, "column": 6}, "end": {"line": 607, "column": 60}}, "174": {"start": {"line": 608, "column": 6}, "end": {"line": 608, "column": 88}}, "175": {"start": {"line": 617, "column": 4}, "end": {"line": 702, "column": 5}}, "176": {"start": {"line": 619, "column": 19}, "end": {"line": 628, "column": 8}}, "177": {"start": {"line": 630, "column": 6}, "end": {"line": 632, "column": 7}}, "178": {"start": {"line": 631, "column": 8}, "end": {"line": 631, "column": 39}}, "179": {"start": {"line": 635, "column": 23}, "end": {"line": 645, "column": 7}}, "180": {"start": {"line": 648, "column": 28}, "end": {"line": 651, "column": 8}}, "181": {"start": {"line": 654, "column": 29}, "end": {"line": 659, "column": 7}}, "182": {"start": {"line": 662, "column": 21}, "end": {"line": 692, "column": 7}}, "183": {"start": {"line": 683, "column": 53}, "end": {"line": 690, "column": 9}}, "184": {"start": {"line": 695, "column": 6}, "end": {"line": 695, "column": 51}}, "185": {"start": {"line": 697, "column": 6}, "end": {"line": 697, "column": 58}}, "186": {"start": {"line": 698, "column": 6}, "end": {"line": 700, "column": 7}}, "187": {"start": {"line": 699, "column": 8}, "end": {"line": 699, "column": 20}}, "188": {"start": {"line": 701, "column": 6}, "end": {"line": 701, "column": 86}}, "189": {"start": {"line": 711, "column": 24}, "end": {"line": 711, "column": 58}}, "190": {"start": {"line": 713, "column": 4}, "end": {"line": 802, "column": 5}}, "191": {"start": {"line": 715, "column": 26}, "end": {"line": 718, "column": 8}}, "192": {"start": {"line": 720, "column": 6}, "end": {"line": 723, "column": 7}}, "193": {"start": {"line": 721, "column": 8}, "end": {"line": 721, "column": 37}}, "194": {"start": {"line": 722, "column": 8}, "end": {"line": 722, "column": 39}}, "195": {"start": {"line": 726, "column": 23}, "end": {"line": 739, "column": 25}}, "196": {"start": {"line": 742, "column": 30}, "end": {"line": 750, "column": 8}}, "197": {"start": {"line": 753, "column": 26}, "end": {"line": 763, "column": 9}}, "198": {"start": {"line": 754, "column": 20}, "end": {"line": 754, "column": 31}}, "199": {"start": {"line": 755, "column": 8}, "end": {"line": 762, "column": 28}}, "200": {"start": {"line": 766, "column": 6}, "end": {"line": 775, "column": 9}}, "201": {"start": {"line": 767, "column": 8}, "end": {"line": 774, "column": 27}}, "202": {"start": {"line": 778, "column": 28}, "end": {"line": 781, "column": 8}}, "203": {"start": {"line": 784, "column": 6}, "end": {"line": 792, "column": 9}}, "204": {"start": {"line": 785, "column": 8}, "end": {"line": 791, "column": 27}}, "205": {"start": {"line": 794, "column": 6}, "end": {"line": 794, "column": 33}}, "206": {"start": {"line": 795, "column": 6}, "end": {"line": 795, "column": 53}}, "207": {"start": {"line": 797, "column": 6}, "end": {"line": 797, "column": 22}}, "208": {"start": {"line": 799, "column": 6}, "end": {"line": 799, "column": 35}}, "209": {"start": {"line": 800, "column": 6}, "end": {"line": 800, "column": 58}}, "210": {"start": {"line": 801, "column": 6}, "end": {"line": 801, "column": 18}}, "211": {"start": {"line": 811, "column": 4}, "end": {"line": 842, "column": 5}}, "212": {"start": {"line": 813, "column": 19}, "end": {"line": 813, "column": 59}}, "213": {"start": {"line": 815, "column": 6}, "end": {"line": 817, "column": 7}}, "214": {"start": {"line": 816, "column": 8}, "end": {"line": 816, "column": 35}}, "215": {"start": {"line": 820, "column": 19}, "end": {"line": 824, "column": 8}}, "216": {"start": {"line": 827, "column": 21}, "end": {"line": 827, "column": 34}}, "217": {"start": {"line": 828, "column": 6}, "end": {"line": 836, "column": 9}}, "218": {"start": {"line": 829, "column": 24}, "end": {"line": 829, "column": 36}}, "219": {"start": {"line": 831, "column": 8}, "end": {"line": 831, "column": 62}}, "220": {"start": {"line": 832, "column": 8}, "end": {"line": 832, "column": 44}}, "221": {"start": {"line": 833, "column": 8}, "end": {"line": 833, "column": 52}}, "222": {"start": {"line": 834, "column": 8}, "end": {"line": 834, "column": 52}}, "223": {"start": {"line": 835, "column": 8}, "end": {"line": 835, "column": 23}}, "224": {"start": {"line": 838, "column": 6}, "end": {"line": 838, "column": 20}}, "225": {"start": {"line": 840, "column": 6}, "end": {"line": 840, "column": 56}}, "226": {"start": {"line": 841, "column": 6}, "end": {"line": 841, "column": 18}}, "227": {"start": {"line": 852, "column": 4}, "end": {"line": 857, "column": 5}}, "228": {"start": {"line": 853, "column": 6}, "end": {"line": 853, "column": 59}}, "229": {"start": {"line": 855, "column": 6}, "end": {"line": 855, "column": 56}}, "230": {"start": {"line": 856, "column": 6}, "end": {"line": 856, "column": 18}}, "231": {"start": {"line": 861, "column": 0}, "end": {"line": 861, "column": 37}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 3}}, "loc": {"start": {"line": 15, "column": 16}, "end": {"line": 26, "column": 3}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 3}}, "loc": {"start": {"line": 33, "column": 27}, "end": {"line": 44, "column": 3}}, "line": 33}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 3}}, "loc": {"start": {"line": 51, "column": 32}, "end": {"line": 68, "column": 3}}, "line": 51}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 59, "column": 30}, "end": {"line": 59, "column": 31}}, "loc": {"start": {"line": 59, "column": 37}, "end": {"line": 65, "column": 5}}, "line": 59}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": 3}}, "loc": {"start": {"line": 80, "column": 111}, "end": {"line": 146, "column": 3}}, "line": 80}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 120, "column": 29}, "end": {"line": 120, "column": 30}}, "loc": {"start": {"line": 120, "column": 38}, "end": {"line": 131, "column": 7}}, "line": 120}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 153, "column": 2}, "end": {"line": 153, "column": 3}}, "loc": {"start": {"line": 153, "column": 31}, "end": {"line": 177, "column": 3}}, "line": 153}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 185, "column": 2}, "end": {"line": 185, "column": 3}}, "loc": {"start": {"line": 185, "column": 39}, "end": {"line": 237, "column": 3}}, "line": 185}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 3}}, "loc": {"start": {"line": 245, "column": 36}, "end": {"line": 312, "column": 3}}, "line": 245}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 292, "column": 50}, "end": {"line": 292, "column": 51}}, "loc": {"start": {"line": 292, "column": 62}, "end": {"line": 299, "column": 7}}, "line": 292}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 321, "column": 2}, "end": {"line": 321, "column": 3}}, "loc": {"start": {"line": 321, "column": 48}, "end": {"line": 392, "column": 3}}, "line": 321}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 361, "column": 55}, "end": {"line": 361, "column": 56}}, "loc": {"start": {"line": 362, "column": 10}, "end": {"line": 364, "column": 29}}, "line": 362}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 368, "column": 42}, "end": {"line": 368, "column": 43}}, "loc": {"start": {"line": 369, "column": 10}, "end": {"line": 376, "column": 29}}, "line": 369}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 400, "column": 2}, "end": {"line": 400, "column": 3}}, "loc": {"start": {"line": 400, "column": 39}, "end": {"line": 419, "column": 3}}, "line": 400}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 428, "column": 2}, "end": {"line": 428, "column": 3}}, "loc": {"start": {"line": 428, "column": 47}, "end": {"line": 457, "column": 3}}, "line": 428}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 466, "column": 2}, "end": {"line": 466, "column": 3}}, "loc": {"start": {"line": 466, "column": 35}, "end": {"line": 488, "column": 3}}, "line": 466}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 496, "column": 2}, "end": {"line": 496, "column": 3}}, "loc": {"start": {"line": 496, "column": 39}, "end": {"line": 518, "column": 3}}, "line": 496}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 526, "column": 2}, "end": {"line": 526, "column": 3}}, "loc": {"start": {"line": 526, "column": 36}, "end": {"line": 553, "column": 3}}, "line": 526}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 562, "column": 2}, "end": {"line": 562, "column": 3}}, "loc": {"start": {"line": 562, "column": 57}, "end": {"line": 610, "column": 3}}, "line": 562}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 587, "column": 29}, "end": {"line": 587, "column": 30}}, "loc": {"start": {"line": 587, "column": 38}, "end": {"line": 595, "column": 7}}, "line": 587}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 616, "column": 2}, "end": {"line": 616, "column": 3}}, "loc": {"start": {"line": 616, "column": 31}, "end": {"line": 703, "column": 3}}, "line": 616}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 683, "column": 41}, "end": {"line": 683, "column": 42}}, "loc": {"start": {"line": 683, "column": 53}, "end": {"line": 690, "column": 9}}, "line": 683}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 710, "column": 2}, "end": {"line": 710, "column": 3}}, "loc": {"start": {"line": 710, "column": 44}, "end": {"line": 803, "column": 3}}, "line": 710}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 753, "column": 64}, "end": {"line": 753, "column": 65}}, "loc": {"start": {"line": 753, "column": 75}, "end": {"line": 763, "column": 7}}, "line": 753}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 766, "column": 40}, "end": {"line": 766, "column": 41}}, "loc": {"start": {"line": 767, "column": 8}, "end": {"line": 774, "column": 27}}, "line": 767}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 784, "column": 42}, "end": {"line": 784, "column": 43}}, "loc": {"start": {"line": 785, "column": 8}, "end": {"line": 791, "column": 27}}, "line": 785}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 810, "column": 2}, "end": {"line": 810, "column": 3}}, "loc": {"start": {"line": 810, "column": 40}, "end": {"line": 843, "column": 3}}, "line": 810}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 828, "column": 29}, "end": {"line": 828, "column": 30}}, "loc": {"start": {"line": 828, "column": 36}, "end": {"line": 836, "column": 7}}, "line": 828}, "28": {"name": "(anonymous_28)", "decl": {"start": {"line": 851, "column": 2}, "end": {"line": 851, "column": 3}}, "loc": {"start": {"line": 851, "column": 39}, "end": {"line": 858, "column": 3}}, "line": 851}}, "branchMap": {"0": {"loc": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 27}}, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 27}}, {"start": {}, "end": {}}], "line": 34}, "1": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 25}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 25}}, {"start": {}, "end": {}}], "line": 52}, "2": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 44}}, "type": "if", "locations": [{"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 44}}, {"start": {}, "end": {}}], "line": 53}, "3": {"loc": {"start": {"line": 56, "column": 17}, "end": {"line": 56, "column": 53}}, "type": "cond-expr", "locations": [{"start": {"line": 56, "column": 30}, "end": {"line": 56, "column": 42}}, {"start": {"line": 56, "column": 45}, "end": {"line": 56, "column": 53}}], "line": 56}, "4": {"loc": {"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, {"start": {"line": 62, "column": 13}, "end": {"line": 64, "column": 7}}], "line": 60}, "5": {"loc": {"start": {"line": 62, "column": 13}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 62, "column": 13}, "end": {"line": 64, "column": 7}}, {"start": {}, "end": {}}], "line": 62}, "6": {"loc": {"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 46}}, {"start": {"line": 62, "column": 50}, "end": {"line": 62, "column": 68}}], "line": 62}, "7": {"loc": {"start": {"line": 80, "column": 29}, "end": {"line": 80, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 80, "column": 36}, "end": {"line": 80, "column": 37}}], "line": 80}, "8": {"loc": {"start": {"line": 80, "column": 39}, "end": {"line": 80, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 80, "column": 50}, "end": {"line": 80, "column": 52}}], "line": 80}, "9": {"loc": {"start": {"line": 80, "column": 54}, "end": {"line": 80, "column": 67}}, "type": "default-arg", "locations": [{"start": {"line": 80, "column": 63}, "end": {"line": 80, "column": 67}}], "line": 80}, "10": {"loc": {"start": {"line": 80, "column": 69}, "end": {"line": 80, "column": 89}}, "type": "default-arg", "locations": [{"start": {"line": 80, "column": 78}, "end": {"line": 80, "column": 89}}], "line": 80}, "11": {"loc": {"start": {"line": 80, "column": 91}, "end": {"line": 80, "column": 109}}, "type": "default-arg", "locations": [{"start": {"line": 80, "column": 103}, "end": {"line": 80, "column": 109}}], "line": 80}, "12": {"loc": {"start": {"line": 89, "column": 6}, "end": {"line": 91, "column": 7}}, "type": "if", "locations": [{"start": {"line": 89, "column": 6}, "end": {"line": 91, "column": 7}}, {"start": {}, "end": {}}], "line": 89}, "13": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 99, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 99, "column": 7}}, {"start": {"line": 97, "column": 13}, "end": {"line": 99, "column": 7}}], "line": 95}, "14": {"loc": {"start": {"line": 97, "column": 13}, "end": {"line": 99, "column": 7}}, "type": "if", "locations": [{"start": {"line": 97, "column": 13}, "end": {"line": 99, "column": 7}}, {"start": {}, "end": {}}], "line": 97}, "15": {"loc": {"start": {"line": 102, "column": 24}, "end": {"line": 102, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 102, "column": 60}, "end": {"line": 102, "column": 65}}, {"start": {"line": 102, "column": 68}, "end": {"line": 102, "column": 74}}], "line": 102}, "16": {"loc": {"start": {"line": 125, "column": 19}, "end": {"line": 125, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 125, "column": 32}, "end": {"line": 125, "column": 47}}, {"start": {"line": 125, "column": 50}, "end": {"line": 125, "column": 54}}], "line": 125}, "17": {"loc": {"start": {"line": 126, "column": 20}, "end": {"line": 126, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 126, "column": 33}, "end": {"line": 126, "column": 49}}, {"start": {"line": 126, "column": 52}, "end": {"line": 126, "column": 56}}], "line": 126}, "18": {"loc": {"start": {"line": 168, "column": 6}, "end": {"line": 170, "column": 7}}, "type": "if", "locations": [{"start": {"line": 168, "column": 6}, "end": {"line": 170, "column": 7}}, {"start": {}, "end": {}}], "line": 168}, "19": {"loc": {"start": {"line": 203, "column": 6}, "end": {"line": 205, "column": 7}}, "type": "if", "locations": [{"start": {"line": 203, "column": 6}, "end": {"line": 205, "column": 7}}, {"start": {}, "end": {}}], "line": 203}, "20": {"loc": {"start": {"line": 208, "column": 6}, "end": {"line": 210, "column": 7}}, "type": "if", "locations": [{"start": {"line": 208, "column": 6}, "end": {"line": 210, "column": 7}}, {"start": {}, "end": {}}], "line": 208}, "21": {"loc": {"start": {"line": 208, "column": 10}, "end": {"line": 208, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 208, "column": 10}, "end": {"line": 208, "column": 16}}, {"start": {"line": 208, "column": 20}, "end": {"line": 208, "column": 43}}, {"start": {"line": 208, "column": 47}, "end": {"line": 208, "column": 70}}], "line": 208}, "22": {"loc": {"start": {"line": 259, "column": 6}, "end": {"line": 261, "column": 7}}, "type": "if", "locations": [{"start": {"line": 259, "column": 6}, "end": {"line": 261, "column": 7}}, {"start": {}, "end": {}}], "line": 259}, "23": {"loc": {"start": {"line": 275, "column": 19}, "end": {"line": 275, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 275, "column": 32}, "end": {"line": 275, "column": 47}}, {"start": {"line": 275, "column": 50}, "end": {"line": 275, "column": 54}}], "line": 275}, "24": {"loc": {"start": {"line": 276, "column": 26}, "end": {"line": 276, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 276, "column": 39}, "end": {"line": 276, "column": 62}}, {"start": {"line": 276, "column": 65}, "end": {"line": 276, "column": 69}}], "line": 276}, "25": {"loc": {"start": {"line": 277, "column": 20}, "end": {"line": 277, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 277, "column": 33}, "end": {"line": 277, "column": 49}}, {"start": {"line": 277, "column": 52}, "end": {"line": 277, "column": 56}}], "line": 277}, "26": {"loc": {"start": {"line": 307, "column": 6}, "end": {"line": 309, "column": 7}}, "type": "if", "locations": [{"start": {"line": 307, "column": 6}, "end": {"line": 309, "column": 7}}, {"start": {}, "end": {}}], "line": 307}, "27": {"loc": {"start": {"line": 321, "column": 37}, "end": {"line": 321, "column": 46}}, "type": "default-arg", "locations": [{"start": {"line": 321, "column": 44}, "end": {"line": 321, "column": 46}}], "line": 321}, "28": {"loc": {"start": {"line": 341, "column": 6}, "end": {"line": 344, "column": 7}}, "type": "if", "locations": [{"start": {"line": 341, "column": 6}, "end": {"line": 344, "column": 7}}, {"start": {}, "end": {}}], "line": 341}, "29": {"loc": {"start": {"line": 353, "column": 30}, "end": {"line": 353, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 353, "column": 30}, "end": {"line": 353, "column": 48}}, {"start": {"line": 353, "column": 52}, "end": {"line": 353, "column": 53}}], "line": 353}, "30": {"loc": {"start": {"line": 354, "column": 29}, "end": {"line": 354, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 354, "column": 29}, "end": {"line": 354, "column": 46}}, {"start": {"line": 354, "column": 50}, "end": {"line": 354, "column": 51}}], "line": 354}, "31": {"loc": {"start": {"line": 355, "column": 25}, "end": {"line": 355, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 355, "column": 25}, "end": {"line": 355, "column": 38}}, {"start": {"line": 355, "column": 42}, "end": {"line": 355, "column": 44}}], "line": 355}, "32": {"loc": {"start": {"line": 359, "column": 6}, "end": {"line": 378, "column": 7}}, "type": "if", "locations": [{"start": {"line": 359, "column": 6}, "end": {"line": 378, "column": 7}}, {"start": {}, "end": {}}], "line": 359}, "33": {"loc": {"start": {"line": 359, "column": 10}, "end": {"line": 359, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 359, "column": 10}, "end": {"line": 359, "column": 14}}, {"start": {"line": 359, "column": 18}, "end": {"line": 359, "column": 33}}], "line": 359}, "34": {"loc": {"start": {"line": 372, "column": 29}, "end": {"line": 372, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 372, "column": 29}, "end": {"line": 372, "column": 56}}, {"start": {"line": 372, "column": 60}, "end": {"line": 372, "column": 63}}], "line": 372}, "35": {"loc": {"start": {"line": 373, "column": 20}, "end": {"line": 373, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 373, "column": 20}, "end": {"line": 373, "column": 38}}, {"start": {"line": 373, "column": 42}, "end": {"line": 373, "column": 45}}], "line": 373}, "36": {"loc": {"start": {"line": 375, "column": 24}, "end": {"line": 375, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 375, "column": 24}, "end": {"line": 375, "column": 46}}, {"start": {"line": 375, "column": 50}, "end": {"line": 375, "column": 55}}], "line": 375}, "37": {"loc": {"start": {"line": 387, "column": 6}, "end": {"line": 389, "column": 7}}, "type": "if", "locations": [{"start": {"line": 387, "column": 6}, "end": {"line": 389, "column": 7}}, {"start": {}, "end": {}}], "line": 387}, "38": {"loc": {"start": {"line": 407, "column": 6}, "end": {"line": 409, "column": 7}}, "type": "if", "locations": [{"start": {"line": 407, "column": 6}, "end": {"line": 409, "column": 7}}, {"start": {}, "end": {}}], "line": 407}, "39": {"loc": {"start": {"line": 414, "column": 6}, "end": {"line": 416, "column": 7}}, "type": "if", "locations": [{"start": {"line": 414, "column": 6}, "end": {"line": 416, "column": 7}}, {"start": {}, "end": {}}], "line": 414}, "40": {"loc": {"start": {"line": 435, "column": 6}, "end": {"line": 437, "column": 7}}, "type": "if", "locations": [{"start": {"line": 435, "column": 6}, "end": {"line": 437, "column": 7}}, {"start": {}, "end": {}}], "line": 435}, "41": {"loc": {"start": {"line": 441, "column": 6}, "end": {"line": 441, "column": 73}}, "type": "if", "locations": [{"start": {"line": 441, "column": 6}, "end": {"line": 441, "column": 73}}, {"start": {}, "end": {}}], "line": 441}, "42": {"loc": {"start": {"line": 442, "column": 6}, "end": {"line": 442, "column": 107}}, "type": "if", "locations": [{"start": {"line": 442, "column": 6}, "end": {"line": 442, "column": 107}}, {"start": {}, "end": {}}], "line": 442}, "43": {"loc": {"start": {"line": 443, "column": 6}, "end": {"line": 443, "column": 104}}, "type": "if", "locations": [{"start": {"line": 443, "column": 6}, "end": {"line": 443, "column": 104}}, {"start": {}, "end": {}}], "line": 443}, "44": {"loc": {"start": {"line": 444, "column": 6}, "end": {"line": 444, "column": 92}}, "type": "if", "locations": [{"start": {"line": 444, "column": 6}, "end": {"line": 444, "column": 92}}, {"start": {}, "end": {}}], "line": 444}, "45": {"loc": {"start": {"line": 452, "column": 6}, "end": {"line": 454, "column": 7}}, "type": "if", "locations": [{"start": {"line": 452, "column": 6}, "end": {"line": 454, "column": 7}}, {"start": {}, "end": {}}], "line": 452}, "46": {"loc": {"start": {"line": 473, "column": 6}, "end": {"line": 475, "column": 7}}, "type": "if", "locations": [{"start": {"line": 473, "column": 6}, "end": {"line": 475, "column": 7}}, {"start": {}, "end": {}}], "line": 473}, "47": {"loc": {"start": {"line": 483, "column": 6}, "end": {"line": 485, "column": 7}}, "type": "if", "locations": [{"start": {"line": 483, "column": 6}, "end": {"line": 485, "column": 7}}, {"start": {}, "end": {}}], "line": 483}, "48": {"loc": {"start": {"line": 503, "column": 6}, "end": {"line": 505, "column": 7}}, "type": "if", "locations": [{"start": {"line": 503, "column": 6}, "end": {"line": 505, "column": 7}}, {"start": {}, "end": {}}], "line": 503}, "49": {"loc": {"start": {"line": 513, "column": 6}, "end": {"line": 515, "column": 7}}, "type": "if", "locations": [{"start": {"line": 513, "column": 6}, "end": {"line": 515, "column": 7}}, {"start": {}, "end": {}}], "line": 513}, "50": {"loc": {"start": {"line": 534, "column": 6}, "end": {"line": 536, "column": 7}}, "type": "if", "locations": [{"start": {"line": 534, "column": 6}, "end": {"line": 536, "column": 7}}, {"start": {}, "end": {}}], "line": 534}, "51": {"loc": {"start": {"line": 538, "column": 6}, "end": {"line": 540, "column": 7}}, "type": "if", "locations": [{"start": {"line": 538, "column": 6}, "end": {"line": 540, "column": 7}}, {"start": {}, "end": {}}], "line": 538}, "52": {"loc": {"start": {"line": 548, "column": 6}, "end": {"line": 550, "column": 7}}, "type": "if", "locations": [{"start": {"line": 548, "column": 6}, "end": {"line": 550, "column": 7}}, {"start": {}, "end": {}}], "line": 548}, "53": {"loc": {"start": {"line": 562, "column": 32}, "end": {"line": 562, "column": 40}}, "type": "default-arg", "locations": [{"start": {"line": 562, "column": 39}, "end": {"line": 562, "column": 40}}], "line": 562}, "54": {"loc": {"start": {"line": 562, "column": 42}, "end": {"line": 562, "column": 55}}, "type": "default-arg", "locations": [{"start": {"line": 562, "column": 53}, "end": {"line": 562, "column": 55}}], "line": 562}, "55": {"loc": {"start": {"line": 592, "column": 19}, "end": {"line": 592, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 592, "column": 32}, "end": {"line": 592, "column": 47}}, {"start": {"line": 592, "column": 50}, "end": {"line": 592, "column": 54}}], "line": 592}, "56": {"loc": {"start": {"line": 593, "column": 20}, "end": {"line": 593, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 593, "column": 33}, "end": {"line": 593, "column": 49}}, {"start": {"line": 593, "column": 52}, "end": {"line": 593, "column": 56}}], "line": 593}, "57": {"loc": {"start": {"line": 630, "column": 6}, "end": {"line": 632, "column": 7}}, "type": "if", "locations": [{"start": {"line": 630, "column": 6}, "end": {"line": 632, "column": 7}}, {"start": {}, "end": {}}], "line": 630}, "58": {"loc": {"start": {"line": 666, "column": 21}, "end": {"line": 666, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 666, "column": 34}, "end": {"line": 666, "column": 49}}, {"start": {"line": 666, "column": 52}, "end": {"line": 666, "column": 56}}], "line": 666}, "59": {"loc": {"start": {"line": 667, "column": 28}, "end": {"line": 667, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 667, "column": 41}, "end": {"line": 667, "column": 64}}, {"start": {"line": 667, "column": 67}, "end": {"line": 667, "column": 71}}], "line": 667}, "60": {"loc": {"start": {"line": 668, "column": 28}, "end": {"line": 668, "column": 70}}, "type": "cond-expr", "locations": [{"start": {"line": 668, "column": 41}, "end": {"line": 668, "column": 63}}, {"start": {"line": 668, "column": 66}, "end": {"line": 668, "column": 70}}], "line": 668}, "61": {"loc": {"start": {"line": 669, "column": 21}, "end": {"line": 669, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 669, "column": 34}, "end": {"line": 669, "column": 49}}, {"start": {"line": 669, "column": 52}, "end": {"line": 669, "column": 56}}], "line": 669}, "62": {"loc": {"start": {"line": 670, "column": 22}, "end": {"line": 670, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 670, "column": 35}, "end": {"line": 670, "column": 51}}, {"start": {"line": 670, "column": 54}, "end": {"line": 670, "column": 58}}], "line": 670}, "63": {"loc": {"start": {"line": 698, "column": 6}, "end": {"line": 700, "column": 7}}, "type": "if", "locations": [{"start": {"line": 698, "column": 6}, "end": {"line": 700, "column": 7}}, {"start": {}, "end": {}}], "line": 698}, "64": {"loc": {"start": {"line": 720, "column": 6}, "end": {"line": 723, "column": 7}}, "type": "if", "locations": [{"start": {"line": 720, "column": 6}, "end": {"line": 723, "column": 7}}, {"start": {}, "end": {}}], "line": 720}, "65": {"loc": {"start": {"line": 770, "column": 27}, "end": {"line": 770, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 770, "column": 27}, "end": {"line": 770, "column": 65}}, {"start": {"line": 770, "column": 69}, "end": {"line": 770, "column": 72}}], "line": 770}, "66": {"loc": {"start": {"line": 771, "column": 18}, "end": {"line": 771, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 771, "column": 18}, "end": {"line": 771, "column": 47}}, {"start": {"line": 771, "column": 51}, "end": {"line": 771, "column": 54}}], "line": 771}, "67": {"loc": {"start": {"line": 773, "column": 22}, "end": {"line": 773, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 773, "column": 22}, "end": {"line": 773, "column": 55}}, {"start": {"line": 773, "column": 59}, "end": {"line": 773, "column": 64}}], "line": 773}, "68": {"loc": {"start": {"line": 815, "column": 6}, "end": {"line": 817, "column": 7}}, "type": "if", "locations": [{"start": {"line": 815, "column": 6}, "end": {"line": 817, "column": 7}}, {"start": {}, "end": {}}], "line": 815}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "203": 0, "204": 0, "205": 0, "206": 0, "207": 0, "208": 0, "209": 0, "210": 0, "211": 0, "212": 0, "213": 0, "214": 0, "215": 0, "216": 0, "217": 0, "218": 0, "219": 0, "220": 0, "221": 0, "222": 0, "223": 0, "224": 0, "225": 0, "226": 0, "227": 0, "228": 0, "229": 0, "230": 0, "231": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 0], "47": [0, 0], "48": [0, 0], "49": [0, 0], "50": [0, 0], "51": [0, 0], "52": [0, 0], "53": [0], "54": [0], "55": [0, 0], "56": [0, 0], "57": [0, 0], "58": [0, 0], "59": [0, 0], "60": [0, 0], "61": [0, 0], "62": [0, 0], "63": [0, 0], "64": [0, 0], "65": [0, 0], "66": [0, 0], "67": [0, 0], "68": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/services/tag.service.compatibility.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/services/tag.service.compatibility.js", "statementMap": {"0": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "1": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 62}}, "2": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 79}}, "3": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 73}}, "4": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 91}}, "5": {"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}, "6": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 70}}, "7": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 57}}, "8": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 18}}, "9": {"start": {"line": 41, "column": 4}, "end": {"line": 47, "column": 5}}, "10": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 65}}, "11": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 59}}, "12": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 18}}, "13": {"start": {"line": 58, "column": 4}, "end": {"line": 75, "column": 5}}, "14": {"start": {"line": 60, "column": 21}, "end": {"line": 60, "column": 71}}, "15": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "16": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 41}}, "17": {"start": {"line": 67, "column": 6}, "end": {"line": 69, "column": 7}}, "18": {"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": 34}}, "19": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 20}}, "20": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 55}}, "21": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 18}}, "22": {"start": {"line": 85, "column": 4}, "end": {"line": 91, "column": 5}}, "23": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 73}}, "24": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 57}}, "25": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 18}}, "26": {"start": {"line": 99, "column": 4}, "end": {"line": 105, "column": 5}}, "27": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 61}}, "28": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 57}}, "29": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 18}}, "30": {"start": {"line": 114, "column": 4}, "end": {"line": 128, "column": 5}}, "31": {"start": {"line": 116, "column": 22}, "end": {"line": 121, "column": 7}}, "32": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 65}}, "33": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 53}}, "34": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 18}}, "35": {"start": {"line": 139, "column": 4}, "end": {"line": 156, "column": 5}}, "36": {"start": {"line": 141, "column": 18}, "end": {"line": 141, "column": 57}}, "37": {"start": {"line": 144, "column": 22}, "end": {"line": 149, "column": 7}}, "38": {"start": {"line": 152, "column": 6}, "end": {"line": 152, "column": 65}}, "39": {"start": {"line": 154, "column": 6}, "end": {"line": 154, "column": 53}}, "40": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 18}}, "41": {"start": {"line": 167, "column": 4}, "end": {"line": 183, "column": 5}}, "42": {"start": {"line": 169, "column": 18}, "end": {"line": 169, "column": 57}}, "43": {"start": {"line": 172, "column": 6}, "end": {"line": 177, "column": 7}}, "44": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 54}}, "45": {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 62}}, "46": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 18}}, "47": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 53}}, "48": {"start": {"line": 182, "column": 6}, "end": {"line": 182, "column": 18}}, "49": {"start": {"line": 193, "column": 4}, "end": {"line": 204, "column": 5}}, "50": {"start": {"line": 195, "column": 18}, "end": {"line": 195, "column": 57}}, "51": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 60}}, "52": {"start": {"line": 200, "column": 6}, "end": {"line": 200, "column": 18}}, "53": {"start": {"line": 202, "column": 6}, "end": {"line": 202, "column": 54}}, "54": {"start": {"line": 203, "column": 6}, "end": {"line": 203, "column": 18}}, "55": {"start": {"line": 214, "column": 4}, "end": {"line": 233, "column": 5}}, "56": {"start": {"line": 216, "column": 18}, "end": {"line": 216, "column": 63}}, "57": {"start": {"line": 218, "column": 6}, "end": {"line": 220, "column": 7}}, "58": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 41}}, "59": {"start": {"line": 222, "column": 6}, "end": {"line": 224, "column": 7}}, "60": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 39}}, "61": {"start": {"line": 227, "column": 6}, "end": {"line": 227, "column": 61}}, "62": {"start": {"line": 229, "column": 6}, "end": {"line": 229, "column": 18}}, "63": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 53}}, "64": {"start": {"line": 232, "column": 6}, "end": {"line": 232, "column": 18}}, "65": {"start": {"line": 244, "column": 4}, "end": {"line": 267, "column": 5}}, "66": {"start": {"line": 246, "column": 20}, "end": {"line": 250, "column": 7}}, "67": {"start": {"line": 252, "column": 19}, "end": {"line": 252, "column": 69}}, "68": {"start": {"line": 255, "column": 26}, "end": {"line": 255, "column": 59}}, "69": {"start": {"line": 255, "column": 45}, "end": {"line": 255, "column": 58}}, "70": {"start": {"line": 257, "column": 6}, "end": {"line": 263, "column": 8}}, "71": {"start": {"line": 265, "column": 6}, "end": {"line": 265, "column": 59}}, "72": {"start": {"line": 266, "column": 6}, "end": {"line": 266, "column": 18}}, "73": {"start": {"line": 272, "column": 32}, "end": {"line": 272, "column": 61}}, "74": {"start": {"line": 274, "column": 0}, "end": {"line": 274, "column": 41}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 3}}, "loc": {"start": {"line": 10, "column": 16}, "end": {"line": 17, "column": 3}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 3}}, "loc": {"start": {"line": 25, "column": 40}, "end": {"line": 33, "column": 3}}, "line": 25}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 3}}, "loc": {"start": {"line": 40, "column": 35}, "end": {"line": 48, "column": 3}}, "line": 40}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 3}}, "loc": {"start": {"line": 57, "column": 61}, "end": {"line": 76, "column": 3}}, "line": 57}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 84, "column": 2}, "end": {"line": 84, "column": 3}}, "loc": {"start": {"line": 84, "column": 43}, "end": {"line": 92, "column": 3}}, "line": 84}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 3}}, "loc": {"start": {"line": 98, "column": 31}, "end": {"line": 106, "column": 3}}, "line": 98}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 3}}, "loc": {"start": {"line": 113, "column": 27}, "end": {"line": 129, "column": 3}}, "line": 113}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 3}}, "loc": {"start": {"line": 138, "column": 45}, "end": {"line": 157, "column": 3}}, "line": 138}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 166, "column": 2}, "end": {"line": 166, "column": 3}}, "loc": {"start": {"line": 166, "column": 48}, "end": {"line": 184, "column": 3}}, "line": 166}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 192, "column": 2}, "end": {"line": 192, "column": 3}}, "loc": {"start": {"line": 192, "column": 37}, "end": {"line": 205, "column": 3}}, "line": 192}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 213, "column": 2}, "end": {"line": 213, "column": 3}}, "loc": {"start": {"line": 213, "column": 34}, "end": {"line": 234, "column": 3}}, "line": 213}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 243, "column": 2}, "end": {"line": 243, "column": 3}}, "loc": {"start": {"line": 243, "column": 56}, "end": {"line": 268, "column": 3}}, "line": 243}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 255, "column": 38}, "end": {"line": 255, "column": 39}}, "loc": {"start": {"line": 255, "column": 45}, "end": {"line": 255, "column": 58}}, "line": 255}}, "branchMap": {"0": {"loc": {"start": {"line": 57, "column": 37}, "end": {"line": 57, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 57, "column": 54}, "end": {"line": 57, "column": 59}}], "line": 57}, "1": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, {"start": {}, "end": {}}], "line": 62}, "2": {"loc": {"start": {"line": 67, "column": 6}, "end": {"line": 69, "column": 7}}, "type": "if", "locations": [{"start": {"line": 67, "column": 6}, "end": {"line": 69, "column": 7}}, {"start": {}, "end": {}}], "line": 67}, "3": {"loc": {"start": {"line": 67, "column": 10}, "end": {"line": 67, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 10}, "end": {"line": 67, "column": 25}}, {"start": {"line": 67, "column": 29}, "end": {"line": 67, "column": 45}}], "line": 67}, "4": {"loc": {"start": {"line": 119, "column": 21}, "end": {"line": 119, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 21}, "end": {"line": 119, "column": 40}}, {"start": {"line": 119, "column": 44}, "end": {"line": 119, "column": 46}}], "line": 119}, "5": {"loc": {"start": {"line": 120, "column": 19}, "end": {"line": 120, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 120, "column": 19}, "end": {"line": 120, "column": 37}}, {"start": {"line": 120, "column": 41}, "end": {"line": 120, "column": 49}}], "line": 120}, "6": {"loc": {"start": {"line": 166, "column": 33}, "end": {"line": 166, "column": 46}}, "type": "default-arg", "locations": [{"start": {"line": 166, "column": 41}, "end": {"line": 166, "column": 46}}], "line": 166}, "7": {"loc": {"start": {"line": 172, "column": 6}, "end": {"line": 177, "column": 7}}, "type": "if", "locations": [{"start": {"line": 172, "column": 6}, "end": {"line": 177, "column": 7}}, {"start": {"line": 175, "column": 13}, "end": {"line": 177, "column": 7}}], "line": 172}, "8": {"loc": {"start": {"line": 218, "column": 6}, "end": {"line": 220, "column": 7}}, "type": "if", "locations": [{"start": {"line": 218, "column": 6}, "end": {"line": 220, "column": 7}}, {"start": {}, "end": {}}], "line": 218}, "9": {"loc": {"start": {"line": 222, "column": 6}, "end": {"line": 224, "column": 7}}, "type": "if", "locations": [{"start": {"line": 222, "column": 6}, "end": {"line": 224, "column": 7}}, {"start": {}, "end": {}}], "line": 222}, "10": {"loc": {"start": {"line": 243, "column": 31}, "end": {"line": 243, "column": 39}}, "type": "default-arg", "locations": [{"start": {"line": 243, "column": 38}, "end": {"line": 243, "column": 39}}], "line": 243}, "11": {"loc": {"start": {"line": 243, "column": 41}, "end": {"line": 243, "column": 54}}, "type": "default-arg", "locations": [{"start": {"line": 243, "column": 52}, "end": {"line": 243, "column": 54}}], "line": 243}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0], "11": [0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/apiError.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/apiError.js", "statementMap": {"0": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 47}}, "1": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 78}}, "2": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 19}}, "3": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 33}}, "4": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 39}}, "5": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 69}}, "6": {"start": {"line": 33, "column": 4}, "end": {"line": 37, "column": 5}}, "7": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 25}}, "8": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 54}}, "9": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 26}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 27, "column": 69}, "end": {"line": 38, "column": 3}}, "line": 27}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 35}, "end": {"line": 27, "column": 55}}, "type": "default-arg", "locations": [{"start": {"line": 27, "column": 51}, "end": {"line": 27, "column": 55}}], "line": 27}, "1": {"loc": {"start": {"line": 27, "column": 57}, "end": {"line": 27, "column": 67}}, "type": "default-arg", "locations": [{"start": {"line": 27, "column": 65}, "end": {"line": 27, "column": 67}}], "line": 27}, "2": {"loc": {"start": {"line": 31, "column": 18}, "end": {"line": 31, "column": 68}}, "type": "cond-expr", "locations": [{"start": {"line": 31, "column": 52}, "end": {"line": 31, "column": 58}}, {"start": {"line": 31, "column": 61}, "end": {"line": 31, "column": 68}}], "line": 31}, "3": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 37, "column": 5}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 37, "column": 5}}, {"start": {"line": 35, "column": 11}, "end": {"line": 37, "column": 5}}], "line": 33}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/apiResponse.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/apiResponse.js", "statementMap": {"0": {"start": {"line": 6, "column": 16}, "end": {"line": 14, "column": 1}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 67}}, "2": {"start": {"line": 9, "column": 2}, "end": {"line": 13, "column": 5}}, "3": {"start": {"line": 17, "column": 14}, "end": {"line": 28, "column": 1}}, "4": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 67}}, "5": {"start": {"line": 20, "column": 2}, "end": {"line": 27, "column": 5}}, "6": {"start": {"line": 31, "column": 16}, "end": {"line": 39, "column": 1}}, "7": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 67}}, "8": {"start": {"line": 34, "column": 2}, "end": {"line": 38, "column": 5}}, "9": {"start": {"line": 42, "column": 18}, "end": {"line": 44, "column": 1}}, "10": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 31}}, "11": {"start": {"line": 47, "column": 21}, "end": {"line": 58, "column": 1}}, "12": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 67}}, "13": {"start": {"line": 50, "column": 2}, "end": {"line": 57, "column": 5}}, "14": {"start": {"line": 61, "column": 18}, "end": {"line": 72, "column": 1}}, "15": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 67}}, "16": {"start": {"line": 64, "column": 2}, "end": {"line": 71, "column": 5}}, "17": {"start": {"line": 75, "column": 17}, "end": {"line": 86, "column": 1}}, "18": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 67}}, "19": {"start": {"line": 78, "column": 2}, "end": {"line": 85, "column": 5}}, "20": {"start": {"line": 89, "column": 19}, "end": {"line": 100, "column": 1}}, "21": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 67}}, "22": {"start": {"line": 92, "column": 2}, "end": {"line": 99, "column": 5}}, "23": {"start": {"line": 103, "column": 17}, "end": {"line": 114, "column": 1}}, "24": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": 67}}, "25": {"start": {"line": 106, "column": 2}, "end": {"line": 113, "column": 5}}, "26": {"start": {"line": 117, "column": 24}, "end": {"line": 128, "column": 1}}, "27": {"start": {"line": 119, "column": 2}, "end": {"line": 119, "column": 67}}, "28": {"start": {"line": 120, "column": 2}, "end": {"line": 127, "column": 5}}, "29": {"start": {"line": 130, "column": 0}, "end": {"line": 141, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 17}}, "loc": {"start": {"line": 6, "column": 75}, "end": {"line": 14, "column": 1}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 15}}, "loc": {"start": {"line": 17, "column": 97}, "end": {"line": 28, "column": 1}}, "line": 17}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 31, "column": 16}, "end": {"line": 31, "column": 17}}, "loc": {"start": {"line": 31, "column": 79}, "end": {"line": 39, "column": 1}}, "line": 31}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 18}, "end": {"line": 42, "column": 19}}, "loc": {"start": {"line": 42, "column": 27}, "end": {"line": 44, "column": 1}}, "line": 42}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 47, "column": 21}, "end": {"line": 47, "column": 22}}, "loc": {"start": {"line": 47, "column": 77}, "end": {"line": 58, "column": 1}}, "line": 47}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 61, "column": 18}, "end": {"line": 61, "column": 19}}, "loc": {"start": {"line": 61, "column": 71}, "end": {"line": 72, "column": 1}}, "line": 61}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 75, "column": 17}, "end": {"line": 75, "column": 18}}, "loc": {"start": {"line": 75, "column": 72}, "end": {"line": 86, "column": 1}}, "line": 75}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 89, "column": 19}, "end": {"line": 89, "column": 20}}, "loc": {"start": {"line": 89, "column": 74}, "end": {"line": 100, "column": 1}}, "line": 89}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 103, "column": 17}, "end": {"line": 103, "column": 18}}, "loc": {"start": {"line": 103, "column": 77}, "end": {"line": 114, "column": 1}}, "line": 103}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 117, "column": 24}, "end": {"line": 117, "column": 25}}, "loc": {"start": {"line": 117, "column": 78}, "end": {"line": 128, "column": 1}}, "line": 117}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 22}, "end": {"line": 6, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 31}}], "line": 6}, "1": {"loc": {"start": {"line": 6, "column": 33}, "end": {"line": 6, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 6, "column": 43}, "end": {"line": 6, "column": 52}}], "line": 6}, "2": {"loc": {"start": {"line": 6, "column": 54}, "end": {"line": 6, "column": 70}}, "type": "default-arg", "locations": [{"start": {"line": 6, "column": 67}, "end": {"line": 6, "column": 70}}], "line": 6}, "3": {"loc": {"start": {"line": 17, "column": 20}, "end": {"line": 17, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 30}, "end": {"line": 17, "column": 37}}], "line": 17}, "4": {"loc": {"start": {"line": 17, "column": 39}, "end": {"line": 17, "column": 60}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 46}, "end": {"line": 17, "column": 60}}], "line": 17}, "5": {"loc": {"start": {"line": 17, "column": 62}, "end": {"line": 17, "column": 78}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 75}, "end": {"line": 17, "column": 78}}], "line": 17}, "6": {"loc": {"start": {"line": 17, "column": 80}, "end": {"line": 17, "column": 92}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 90}, "end": {"line": 17, "column": 92}}], "line": 17}, "7": {"loc": {"start": {"line": 31, "column": 22}, "end": {"line": 31, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 31, "column": 29}, "end": {"line": 31, "column": 31}}], "line": 31}, "8": {"loc": {"start": {"line": 31, "column": 33}, "end": {"line": 31, "column": 74}}, "type": "default-arg", "locations": [{"start": {"line": 31, "column": 43}, "end": {"line": 31, "column": 74}}], "line": 31}, "9": {"loc": {"start": {"line": 47, "column": 27}, "end": {"line": 47, "column": 58}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 37}, "end": {"line": 47, "column": 58}}], "line": 47}, "10": {"loc": {"start": {"line": 47, "column": 60}, "end": {"line": 47, "column": 72}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 70}, "end": {"line": 47, "column": 72}}], "line": 47}, "11": {"loc": {"start": {"line": 61, "column": 24}, "end": {"line": 61, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 61, "column": 34}, "end": {"line": 61, "column": 52}}], "line": 61}, "12": {"loc": {"start": {"line": 61, "column": 54}, "end": {"line": 61, "column": 66}}, "type": "default-arg", "locations": [{"start": {"line": 61, "column": 64}, "end": {"line": 61, "column": 66}}], "line": 61}, "13": {"loc": {"start": {"line": 75, "column": 23}, "end": {"line": 75, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 75, "column": 33}, "end": {"line": 75, "column": 53}}], "line": 75}, "14": {"loc": {"start": {"line": 75, "column": 55}, "end": {"line": 75, "column": 67}}, "type": "default-arg", "locations": [{"start": {"line": 75, "column": 65}, "end": {"line": 75, "column": 67}}], "line": 75}, "15": {"loc": {"start": {"line": 89, "column": 25}, "end": {"line": 89, "column": 55}}, "type": "default-arg", "locations": [{"start": {"line": 89, "column": 35}, "end": {"line": 89, "column": 55}}], "line": 89}, "16": {"loc": {"start": {"line": 89, "column": 57}, "end": {"line": 89, "column": 69}}, "type": "default-arg", "locations": [{"start": {"line": 89, "column": 67}, "end": {"line": 89, "column": 69}}], "line": 89}, "17": {"loc": {"start": {"line": 103, "column": 23}, "end": {"line": 103, "column": 58}}, "type": "default-arg", "locations": [{"start": {"line": 103, "column": 33}, "end": {"line": 103, "column": 58}}], "line": 103}, "18": {"loc": {"start": {"line": 103, "column": 60}, "end": {"line": 103, "column": 72}}, "type": "default-arg", "locations": [{"start": {"line": 103, "column": 70}, "end": {"line": 103, "column": 72}}], "line": 103}, "19": {"loc": {"start": {"line": 117, "column": 30}, "end": {"line": 117, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 117, "column": 40}, "end": {"line": 117, "column": 59}}], "line": 117}, "20": {"loc": {"start": {"line": 117, "column": 61}, "end": {"line": 117, "column": 73}}, "type": "default-arg", "locations": [{"start": {"line": 117, "column": 71}, "end": {"line": 117, "column": 73}}], "line": 117}}, "s": {"0": 1, "1": 3, "2": 0, "3": 1, "4": 3, "5": 0, "6": 1, "7": 1, "8": 0, "9": 1, "10": 1, "11": 1, "12": 1, "13": 0, "14": 1, "15": 1, "16": 0, "17": 1, "18": 1, "19": 0, "20": 1, "21": 1, "22": 0, "23": 1, "24": 1, "25": 0, "26": 1, "27": 1, "28": 0, "29": 1}, "f": {"0": 3, "1": 3, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1}, "b": {"0": [0], "1": [1], "2": [2], "3": [1], "4": [1], "5": [2], "6": [2], "7": [0], "8": [1], "9": [1], "10": [1], "11": [1], "12": [1], "13": [1], "14": [1], "15": [1], "16": [1], "17": [1], "18": [1], "19": [1], "20": [1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "7d3877768dd903f3577a3ede7c5dd8cb94aacb6b"}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/dataTransformer.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/dataTransformer.js", "statementMap": {"0": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 66}}, "1": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 97}}, "2": {"start": {"line": 17, "column": 0}, "end": {"line": 23, "column": 2}}, "3": {"start": {"line": 20, "column": 29}, "end": {"line": 20, "column": 106}}, "4": {"start": {"line": 21, "column": 29}, "end": {"line": 21, "column": 106}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 20, "column": 20}, "end": {"line": 20, "column": 21}}, "loc": {"start": {"line": 20, "column": 29}, "end": {"line": 20, "column": 106}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 20}, "end": {"line": 21, "column": 21}}, "loc": {"start": {"line": 21, "column": 29}, "end": {"line": 21, "column": 106}}, "line": 21}}, "branchMap": {}, "s": {"0": 3, "1": 3, "2": 3, "3": 12, "4": 8}, "f": {"0": 12, "1": 8}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "d39973085e34900ed33159e6283c672a29d5e8f3"}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/enhanced-data-transformer.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/enhanced-data-transformer.js", "statementMap": {"0": {"start": {"line": 11, "column": 20}, "end": {"line": 17, "column": 1}}, "1": {"start": {"line": 31, "column": 20}, "end": {"line": 39, "column": 1}}, "2": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 50}}, "3": {"start": {"line": 33, "column": 39}, "end": {"line": 33, "column": 50}}, "4": {"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 61}}, "5": {"start": {"line": 37, "column": 46}, "end": {"line": 37, "column": 63}}, "6": {"start": {"line": 38, "column": 36}, "end": {"line": 38, "column": 59}}, "7": {"start": {"line": 53, "column": 20}, "end": {"line": 65, "column": 1}}, "8": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 50}}, "9": {"start": {"line": 55, "column": 39}, "end": {"line": 55, "column": 50}}, "10": {"start": {"line": 57, "column": 2}, "end": {"line": 64, "column": 19}}, "11": {"start": {"line": 79, "column": 20}, "end": {"line": 91, "column": 1}}, "12": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 50}}, "13": {"start": {"line": 81, "column": 39}, "end": {"line": 81, "column": 50}}, "14": {"start": {"line": 83, "column": 2}, "end": {"line": 90, "column": 19}}, "15": {"start": {"line": 105, "column": 21}, "end": {"line": 111, "column": 1}}, "16": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 50}}, "17": {"start": {"line": 107, "column": 39}, "end": {"line": 107, "column": 50}}, "18": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 82}}, "19": {"start": {"line": 110, "column": 57}, "end": {"line": 110, "column": 80}}, "20": {"start": {"line": 125, "column": 23}, "end": {"line": 130, "column": 1}}, "21": {"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 50}}, "22": {"start": {"line": 127, "column": 39}, "end": {"line": 127, "column": 50}}, "23": {"start": {"line": 129, "column": 2}, "end": {"line": 129, "column": 40}}, "24": {"start": {"line": 142, "column": 26}, "end": {"line": 167, "column": 1}}, "25": {"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 51}}, "26": {"start": {"line": 144, "column": 39}, "end": {"line": 144, "column": 51}}, "27": {"start": {"line": 147, "column": 2}, "end": {"line": 149, "column": 3}}, "28": {"start": {"line": 148, "column": 4}, "end": {"line": 148, "column": 29}}, "29": {"start": {"line": 152, "column": 2}, "end": {"line": 158, "column": 3}}, "30": {"start": {"line": 154, "column": 4}, "end": {"line": 156, "column": 5}}, "31": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 34}}, "32": {"start": {"line": 157, "column": 4}, "end": {"line": 157, "column": 29}}, "33": {"start": {"line": 161, "column": 2}, "end": {"line": 163, "column": 3}}, "34": {"start": {"line": 162, "column": 4}, "end": {"line": 162, "column": 30}}, "35": {"start": {"line": 166, "column": 2}, "end": {"line": 166, "column": 27}}, "36": {"start": {"line": 178, "column": 22}, "end": {"line": 196, "column": 1}}, "37": {"start": {"line": 180, "column": 2}, "end": {"line": 180, "column": 50}}, "38": {"start": {"line": 180, "column": 39}, "end": {"line": 180, "column": 50}}, "39": {"start": {"line": 182, "column": 2}, "end": {"line": 195, "column": 3}}, "40": {"start": {"line": 184, "column": 6}, "end": {"line": 184, "column": 30}}, "41": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 30}}, "42": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": 30}}, "43": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 31}}, "44": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 33}}, "45": {"start": {"line": 194, "column": 6}, "end": {"line": 194, "column": 17}}, "46": {"start": {"line": 207, "column": 22}, "end": {"line": 230, "column": 1}}, "47": {"start": {"line": 208, "column": 2}, "end": {"line": 210, "column": 3}}, "48": {"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": 15}}, "49": {"start": {"line": 212, "column": 2}, "end": {"line": 214, "column": 3}}, "50": {"start": {"line": 213, "column": 4}, "end": {"line": 213, "column": 61}}, "51": {"start": {"line": 213, "column": 27}, "end": {"line": 213, "column": 59}}, "52": {"start": {"line": 216, "column": 17}, "end": {"line": 216, "column": 19}}, "53": {"start": {"line": 217, "column": 2}, "end": {"line": 227, "column": 5}}, "54": {"start": {"line": 218, "column": 27}, "end": {"line": 218, "column": 58}}, "55": {"start": {"line": 219, "column": 18}, "end": {"line": 219, "column": 26}}, "56": {"start": {"line": 222, "column": 4}, "end": {"line": 226, "column": 5}}, "57": {"start": {"line": 223, "column": 6}, "end": {"line": 223, "column": 65}}, "58": {"start": {"line": 225, "column": 6}, "end": {"line": 225, "column": 37}}, "59": {"start": {"line": 229, "column": 2}, "end": {"line": 229, "column": 16}}, "60": {"start": {"line": 242, "column": 34}, "end": {"line": 296, "column": 1}}, "61": {"start": {"line": 243, "column": 25}, "end": {"line": 249, "column": 3}}, "62": {"start": {"line": 251, "column": 17}, "end": {"line": 251, "column": 50}}, "63": {"start": {"line": 253, "column": 2}, "end": {"line": 295, "column": 4}}, "64": {"start": {"line": 255, "column": 26}, "end": {"line": 260, "column": 6}}, "65": {"start": {"line": 256, "column": 6}, "end": {"line": 258, "column": 7}}, "66": {"start": {"line": 257, "column": 8}, "end": {"line": 257, "column": 38}}, "67": {"start": {"line": 259, "column": 6}, "end": {"line": 259, "column": 66}}, "68": {"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}, "69": {"start": {"line": 263, "column": 6}, "end": {"line": 263, "column": 20}}, "70": {"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 5}}, "71": {"start": {"line": 268, "column": 6}, "end": {"line": 268, "column": 62}}, "72": {"start": {"line": 272, "column": 25}, "end": {"line": 272, "column": 33}}, "73": {"start": {"line": 275, "column": 4}, "end": {"line": 292, "column": 6}}, "74": {"start": {"line": 277, "column": 6}, "end": {"line": 285, "column": 7}}, "75": {"start": {"line": 278, "column": 8}, "end": {"line": 284, "column": 9}}, "76": {"start": {"line": 280, "column": 10}, "end": {"line": 280, "column": 69}}, "77": {"start": {"line": 283, "column": 10}, "end": {"line": 283, "column": 59}}, "78": {"start": {"line": 288, "column": 6}, "end": {"line": 288, "column": 30}}, "79": {"start": {"line": 291, "column": 6}, "end": {"line": 291, "column": 28}}, "80": {"start": {"line": 294, "column": 4}, "end": {"line": 294, "column": 11}}, "81": {"start": {"line": 298, "column": 0}, "end": {"line": 309, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 31, "column": 20}, "end": {"line": 31, "column": 21}}, "loc": {"start": {"line": 31, "column": 29}, "end": {"line": 39, "column": 1}}, "line": 31}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 37, "column": 34}, "end": {"line": 37, "column": 35}}, "loc": {"start": {"line": 37, "column": 46}, "end": {"line": 37, "column": 63}}, "line": 37}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 38, "column": 23}, "end": {"line": 38, "column": 24}}, "loc": {"start": {"line": 38, "column": 36}, "end": {"line": 38, "column": 59}}, "line": 38}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 53, "column": 20}, "end": {"line": 53, "column": 21}}, "loc": {"start": {"line": 53, "column": 29}, "end": {"line": 65, "column": 1}}, "line": 53}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 79, "column": 20}, "end": {"line": 79, "column": 21}}, "loc": {"start": {"line": 79, "column": 29}, "end": {"line": 91, "column": 1}}, "line": 79}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 105, "column": 21}, "end": {"line": 105, "column": 22}}, "loc": {"start": {"line": 105, "column": 30}, "end": {"line": 111, "column": 1}}, "line": 105}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 110, "column": 44}, "end": {"line": 110, "column": 45}}, "loc": {"start": {"line": 110, "column": 57}, "end": {"line": 110, "column": 80}}, "line": 110}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 125, "column": 23}, "end": {"line": 125, "column": 24}}, "loc": {"start": {"line": 125, "column": 32}, "end": {"line": 130, "column": 1}}, "line": 125}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 142, "column": 26}, "end": {"line": 142, "column": 27}}, "loc": {"start": {"line": 142, "column": 35}, "end": {"line": 167, "column": 1}}, "line": 142}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 178, "column": 22}, "end": {"line": 178, "column": 23}}, "loc": {"start": {"line": 178, "column": 44}, "end": {"line": 196, "column": 1}}, "line": 178}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 207, "column": 22}, "end": {"line": 207, "column": 23}}, "loc": {"start": {"line": 207, "column": 44}, "end": {"line": 230, "column": 1}}, "line": 207}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 213, "column": 19}, "end": {"line": 213, "column": 20}}, "loc": {"start": {"line": 213, "column": 27}, "end": {"line": 213, "column": 59}}, "line": 213}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 217, "column": 27}, "end": {"line": 217, "column": 28}}, "loc": {"start": {"line": 217, "column": 34}, "end": {"line": 227, "column": 3}}, "line": 217}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 242, "column": 34}, "end": {"line": 242, "column": 35}}, "loc": {"start": {"line": 242, "column": 52}, "end": {"line": 296, "column": 1}}, "line": 242}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 253, "column": 9}, "end": {"line": 253, "column": 10}}, "loc": {"start": {"line": 253, "column": 29}, "end": {"line": 295, "column": 3}}, "line": 253}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 255, "column": 51}, "end": {"line": 255, "column": 52}}, "loc": {"start": {"line": 255, "column": 62}, "end": {"line": 260, "column": 5}}, "line": 255}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 275, "column": 15}, "end": {"line": 275, "column": 16}}, "loc": {"start": {"line": 275, "column": 30}, "end": {"line": 292, "column": 5}}, "line": 275}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 50}}, "type": "if", "locations": [{"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 50}}, {"start": {}, "end": {}}], "line": 33}, "1": {"loc": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 10}}, {"start": {"line": 33, "column": 14}, "end": {"line": 33, "column": 37}}], "line": 33}, "2": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 50}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 50}}, {"start": {}, "end": {}}], "line": 55}, "3": {"loc": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 10}}, {"start": {"line": 55, "column": 14}, "end": {"line": 55, "column": 37}}], "line": 55}, "4": {"loc": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 50}}, "type": "if", "locations": [{"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 50}}, {"start": {}, "end": {}}], "line": 81}, "5": {"loc": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 10}}, {"start": {"line": 81, "column": 14}, "end": {"line": 81, "column": 37}}], "line": 81}, "6": {"loc": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 50}}, "type": "if", "locations": [{"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 50}}, {"start": {}, "end": {}}], "line": 107}, "7": {"loc": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 10}}, {"start": {"line": 107, "column": 14}, "end": {"line": 107, "column": 37}}], "line": 107}, "8": {"loc": {"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 50}}, "type": "if", "locations": [{"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 50}}, {"start": {}, "end": {}}], "line": 127}, "9": {"loc": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 10}}, {"start": {"line": 127, "column": 14}, "end": {"line": 127, "column": 37}}], "line": 127}, "10": {"loc": {"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 51}}, "type": "if", "locations": [{"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 51}}, {"start": {}, "end": {}}], "line": 144}, "11": {"loc": {"start": {"line": 144, "column": 6}, "end": {"line": 144, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 6}, "end": {"line": 144, "column": 10}}, {"start": {"line": 144, "column": 14}, "end": {"line": 144, "column": 37}}], "line": 144}, "12": {"loc": {"start": {"line": 147, "column": 2}, "end": {"line": 149, "column": 3}}, "type": "if", "locations": [{"start": {"line": 147, "column": 2}, "end": {"line": 149, "column": 3}}, {"start": {}, "end": {}}], "line": 147}, "13": {"loc": {"start": {"line": 152, "column": 2}, "end": {"line": 158, "column": 3}}, "type": "if", "locations": [{"start": {"line": 152, "column": 2}, "end": {"line": 158, "column": 3}}, {"start": {}, "end": {}}], "line": 152}, "14": {"loc": {"start": {"line": 154, "column": 4}, "end": {"line": 156, "column": 5}}, "type": "if", "locations": [{"start": {"line": 154, "column": 4}, "end": {"line": 156, "column": 5}}, {"start": {}, "end": {}}], "line": 154}, "15": {"loc": {"start": {"line": 161, "column": 2}, "end": {"line": 163, "column": 3}}, "type": "if", "locations": [{"start": {"line": 161, "column": 2}, "end": {"line": 163, "column": 3}}, {"start": {}, "end": {}}], "line": 161}, "16": {"loc": {"start": {"line": 180, "column": 2}, "end": {"line": 180, "column": 50}}, "type": "if", "locations": [{"start": {"line": 180, "column": 2}, "end": {"line": 180, "column": 50}}, {"start": {}, "end": {}}], "line": 180}, "17": {"loc": {"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 10}}, {"start": {"line": 180, "column": 14}, "end": {"line": 180, "column": 37}}], "line": 180}, "18": {"loc": {"start": {"line": 182, "column": 2}, "end": {"line": 195, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 183, "column": 4}, "end": {"line": 184, "column": 30}}, {"start": {"line": 185, "column": 4}, "end": {"line": 186, "column": 30}}, {"start": {"line": 187, "column": 4}, "end": {"line": 188, "column": 30}}, {"start": {"line": 189, "column": 4}, "end": {"line": 190, "column": 31}}, {"start": {"line": 191, "column": 4}, "end": {"line": 192, "column": 33}}, {"start": {"line": 193, "column": 4}, "end": {"line": 194, "column": 17}}], "line": 182}, "19": {"loc": {"start": {"line": 208, "column": 2}, "end": {"line": 210, "column": 3}}, "type": "if", "locations": [{"start": {"line": 208, "column": 2}, "end": {"line": 210, "column": 3}}, {"start": {}, "end": {}}], "line": 208}, "20": {"loc": {"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": 18}}, {"start": {"line": 208, "column": 22}, "end": {"line": 208, "column": 39}}, {"start": {"line": 208, "column": 43}, "end": {"line": 208, "column": 66}}], "line": 208}, "21": {"loc": {"start": {"line": 212, "column": 2}, "end": {"line": 214, "column": 3}}, "type": "if", "locations": [{"start": {"line": 212, "column": 2}, "end": {"line": 214, "column": 3}}, {"start": {}, "end": {}}], "line": 212}, "22": {"loc": {"start": {"line": 222, "column": 4}, "end": {"line": 226, "column": 5}}, "type": "if", "locations": [{"start": {"line": 222, "column": 4}, "end": {"line": 226, "column": 5}}, {"start": {"line": 224, "column": 11}, "end": {"line": 226, "column": 5}}], "line": 222}, "23": {"loc": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 22}}, {"start": {"line": 222, "column": 26}, "end": {"line": 222, "column": 51}}], "line": 222}, "24": {"loc": {"start": {"line": 242, "column": 35}, "end": {"line": 242, "column": 47}}, "type": "default-arg", "locations": [{"start": {"line": 242, "column": 45}, "end": {"line": 242, "column": 47}}], "line": 242}, "25": {"loc": {"start": {"line": 256, "column": 6}, "end": {"line": 258, "column": 7}}, "type": "if", "locations": [{"start": {"line": 256, "column": 6}, "end": {"line": 258, "column": 7}}, {"start": {}, "end": {}}], "line": 256}, "26": {"loc": {"start": {"line": 259, "column": 13}, "end": {"line": 259, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 259, "column": 13}, "end": {"line": 259, "column": 33}}, {"start": {"line": 259, "column": 37}, "end": {"line": 259, "column": 65}}], "line": 259}, "27": {"loc": {"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}, "type": "if", "locations": [{"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}, {"start": {}, "end": {}}], "line": 262}, "28": {"loc": {"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 5}}, "type": "if", "locations": [{"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 5}}, {"start": {}, "end": {}}], "line": 267}, "29": {"loc": {"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 31}}, {"start": {"line": 267, "column": 35}, "end": {"line": 267, "column": 43}}], "line": 267}, "30": {"loc": {"start": {"line": 277, "column": 6}, "end": {"line": 285, "column": 7}}, "type": "if", "locations": [{"start": {"line": 277, "column": 6}, "end": {"line": 285, "column": 7}}, {"start": {}, "end": {}}], "line": 277}, "31": {"loc": {"start": {"line": 277, "column": 10}, "end": {"line": 277, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 277, "column": 10}, "end": {"line": 277, "column": 34}}, {"start": {"line": 277, "column": 38}, "end": {"line": 277, "column": 42}}], "line": 277}, "32": {"loc": {"start": {"line": 278, "column": 8}, "end": {"line": 284, "column": 9}}, "type": "if", "locations": [{"start": {"line": 278, "column": 8}, "end": {"line": 284, "column": 9}}, {"start": {"line": 281, "column": 15}, "end": {"line": 284, "column": 9}}], "line": 278}}, "s": {"0": 3, "1": 3, "2": 50, "3": 1, "4": 49, "5": 34, "6": 1, "7": 3, "8": 35, "9": 1, "10": 34, "11": 3, "12": 0, "13": 0, "14": 0, "15": 3, "16": 0, "17": 0, "18": 0, "19": 0, "20": 3, "21": 0, "22": 0, "23": 0, "24": 3, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 3, "37": 71, "38": 0, "39": 71, "40": 43, "41": 28, "42": 0, "43": 0, "44": 0, "45": 0, "46": 3, "47": 39, "48": 10, "49": 29, "50": 2, "51": 4, "52": 27, "53": 27, "54": 71, "55": 71, "56": 71, "57": 10, "58": 61, "59": 27, "60": 3, "61": 5, "62": 5, "63": 5, "64": 5, "65": 0, "66": 0, "67": 0, "68": 5, "69": 0, "70": 5, "71": 2, "72": 5, "73": 5, "74": 4, "75": 3, "76": 2, "77": 1, "78": 4, "79": 4, "80": 5, "81": 3}, "f": {"0": 50, "1": 34, "2": 1, "3": 35, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 71, "10": 39, "11": 4, "12": 71, "13": 5, "14": 5, "15": 0, "16": 4}, "b": {"0": [1, 49], "1": [50, 49], "2": [1, 34], "3": [35, 34], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 71], "17": [71, 71], "18": [43, 28, 0, 0, 0, 0], "19": [10, 29], "20": [39, 37, 35], "21": [2, 27], "22": [10, 61], "23": [71, 71], "24": [1], "25": [0, 0], "26": [0, 0], "27": [0, 5], "28": [2, 3], "29": [5, 2], "30": [3, 1], "31": [4, 3], "32": [2, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "43e5245e4d46e9bf5f4c4f7edf8fb39640d1832b"}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/enhanced-jwt.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/enhanced-jwt.js", "statementMap": {"0": {"start": {"line": 1, "column": 12}, "end": {"line": 1, "column": 35}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 32}}, "2": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 42}}, "3": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 42}}, "4": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 50}}, "5": {"start": {"line": 18, "column": 4}, "end": {"line": 50, "column": 5}}, "6": {"start": {"line": 20, "column": 18}, "end": {"line": 20, "column": 56}}, "7": {"start": {"line": 23, "column": 26}, "end": {"line": 27, "column": 7}}, "8": {"start": {"line": 30, "column": 27}, "end": {"line": 34, "column": 7}}, "9": {"start": {"line": 37, "column": 6}, "end": {"line": 40, "column": 7}}, "10": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 75}}, "11": {"start": {"line": 42, "column": 6}, "end": {"line": 46, "column": 8}}, "12": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 47}}, "13": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 32}}, "14": {"start": {"line": 59, "column": 4}, "end": {"line": 77, "column": 5}}, "15": {"start": {"line": 61, "column": 22}, "end": {"line": 61, "column": 58}}, "16": {"start": {"line": 64, "column": 6}, "end": {"line": 71, "column": 7}}, "17": {"start": {"line": 65, "column": 8}, "end": {"line": 70, "column": 13}}, "18": {"start": {"line": 66, "column": 29}, "end": {"line": 66, "column": 55}}, "19": {"start": {"line": 68, "column": 12}, "end": {"line": 68, "column": 57}}, "20": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 27}}, "21": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 21}}, "22": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 47}}, "23": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 18}}, "24": {"start": {"line": 87, "column": 4}, "end": {"line": 114, "column": 5}}, "25": {"start": {"line": 89, "column": 22}, "end": {"line": 89, "column": 65}}, "26": {"start": {"line": 92, "column": 6}, "end": {"line": 95, "column": 7}}, "27": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 34}}, "28": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 20}}, "29": {"start": {"line": 98, "column": 6}, "end": {"line": 107, "column": 7}}, "30": {"start": {"line": 99, "column": 26}, "end": {"line": 99, "column": 67}}, "31": {"start": {"line": 100, "column": 8}, "end": {"line": 103, "column": 9}}, "32": {"start": {"line": 101, "column": 10}, "end": {"line": 101, "column": 50}}, "33": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 22}}, "34": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 61}}, "35": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 43}}, "36": {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 47}}, "37": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 18}}, "38": {"start": {"line": 123, "column": 4}, "end": {"line": 156, "column": 5}}, "39": {"start": {"line": 124, "column": 6}, "end": {"line": 127, "column": 7}}, "40": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 42}}, "41": {"start": {"line": 126, "column": 8}, "end": {"line": 126, "column": 21}}, "42": {"start": {"line": 130, "column": 23}, "end": {"line": 130, "column": 46}}, "43": {"start": {"line": 131, "column": 23}, "end": {"line": 131, "column": 59}}, "44": {"start": {"line": 133, "column": 6}, "end": {"line": 135, "column": 7}}, "45": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 20}}, "46": {"start": {"line": 138, "column": 20}, "end": {"line": 138, "column": 39}}, "47": {"start": {"line": 141, "column": 6}, "end": {"line": 143, "column": 7}}, "48": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 78}}, "49": {"start": {"line": 146, "column": 6}, "end": {"line": 146, "column": 26}}, "50": {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 25}}, "51": {"start": {"line": 151, "column": 6}, "end": {"line": 151, "column": 66}}, "52": {"start": {"line": 152, "column": 6}, "end": {"line": 152, "column": 18}}, "53": {"start": {"line": 154, "column": 6}, "end": {"line": 154, "column": 49}}, "54": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 19}}, "55": {"start": {"line": 165, "column": 23}, "end": {"line": 165, "column": 48}}, "56": {"start": {"line": 167, "column": 4}, "end": {"line": 169, "column": 5}}, "57": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 18}}, "58": {"start": {"line": 171, "column": 4}, "end": {"line": 171, "column": 36}}, "59": {"start": {"line": 183, "column": 4}, "end": {"line": 185, "column": 5}}, "60": {"start": {"line": 184, "column": 6}, "end": {"line": 184, "column": 13}}, "61": {"start": {"line": 187, "column": 4}, "end": {"line": 199, "column": 5}}, "62": {"start": {"line": 188, "column": 23}, "end": {"line": 188, "column": 46}}, "63": {"start": {"line": 191, "column": 6}, "end": {"line": 191, "column": 44}}, "64": {"start": {"line": 194, "column": 6}, "end": {"line": 194, "column": 52}}, "65": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 49}}, "66": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 49}}, "67": {"start": {"line": 209, "column": 4}, "end": {"line": 211, "column": 5}}, "68": {"start": {"line": 210, "column": 6}, "end": {"line": 210, "column": 19}}, "69": {"start": {"line": 213, "column": 4}, "end": {"line": 220, "column": 5}}, "70": {"start": {"line": 215, "column": 24}, "end": {"line": 215, "column": 71}}, "71": {"start": {"line": 216, "column": 6}, "end": {"line": 216, "column": 25}}, "72": {"start": {"line": 218, "column": 6}, "end": {"line": 218, "column": 51}}, "73": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": 19}}, "74": {"start": {"line": 231, "column": 4}, "end": {"line": 233, "column": 5}}, "75": {"start": {"line": 232, "column": 6}, "end": {"line": 232, "column": 19}}, "76": {"start": {"line": 235, "column": 4}, "end": {"line": 247, "column": 5}}, "77": {"start": {"line": 237, "column": 6}, "end": {"line": 237, "column": 88}}, "78": {"start": {"line": 240, "column": 6}, "end": {"line": 240, "column": 59}}, "79": {"start": {"line": 242, "column": 6}, "end": {"line": 242, "column": 47}}, "80": {"start": {"line": 243, "column": 6}, "end": {"line": 243, "column": 18}}, "81": {"start": {"line": 245, "column": 6}, "end": {"line": 245, "column": 47}}, "82": {"start": {"line": 246, "column": 6}, "end": {"line": 246, "column": 19}}, "83": {"start": {"line": 251, "column": 0}, "end": {"line": 251, "column": 35}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 3}}, "loc": {"start": {"line": 17, "column": 26}, "end": {"line": 51, "column": 3}}, "line": 17}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 3}}, "loc": {"start": {"line": 58, "column": 27}, "end": {"line": 78, "column": 3}}, "line": 58}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 66, "column": 16}, "end": {"line": 66, "column": 17}}, "loc": {"start": {"line": 66, "column": 29}, "end": {"line": 66, "column": 55}}, "line": 66}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 67, "column": 17}, "end": {"line": 67, "column": 18}}, "loc": {"start": {"line": 67, "column": 26}, "end": {"line": 70, "column": 11}}, "line": 67}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 3}}, "loc": {"start": {"line": 86, "column": 46}, "end": {"line": 115, "column": 3}}, "line": 86}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 122, "column": 2}, "end": {"line": 122, "column": 3}}, "loc": {"start": {"line": 122, "column": 36}, "end": {"line": 157, "column": 3}}, "line": 122}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 164, "column": 2}, "end": {"line": 164, "column": 3}}, "loc": {"start": {"line": 164, "column": 30}, "end": {"line": 172, "column": 3}}, "line": 164}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 182, "column": 2}, "end": {"line": 182, "column": 3}}, "loc": {"start": {"line": 182, "column": 48}, "end": {"line": 200, "column": 3}}, "line": 182}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 208, "column": 2}, "end": {"line": 208, "column": 3}}, "loc": {"start": {"line": 208, "column": 39}, "end": {"line": 221, "column": 3}}, "line": 208}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 230, "column": 2}, "end": {"line": 230, "column": 3}}, "loc": {"start": {"line": 230, "column": 34}, "end": {"line": 248, "column": 3}}, "line": 230}}, "branchMap": {"0": {"loc": {"start": {"line": 37, "column": 6}, "end": {"line": 40, "column": 7}}, "type": "if", "locations": [{"start": {"line": 37, "column": 6}, "end": {"line": 40, "column": 7}}, {"start": {}, "end": {}}], "line": 37}, "1": {"loc": {"start": {"line": 64, "column": 6}, "end": {"line": 71, "column": 7}}, "type": "if", "locations": [{"start": {"line": 64, "column": 6}, "end": {"line": 71, "column": 7}}, {"start": {}, "end": {}}], "line": 64}, "2": {"loc": {"start": {"line": 66, "column": 29}, "end": {"line": 66, "column": 55}}, "type": "cond-expr", "locations": [{"start": {"line": 66, "column": 41}, "end": {"line": 66, "column": 45}}, {"start": {"line": 66, "column": 48}, "end": {"line": 66, "column": 55}}], "line": 66}, "3": {"loc": {"start": {"line": 92, "column": 6}, "end": {"line": 95, "column": 7}}, "type": "if", "locations": [{"start": {"line": 92, "column": 6}, "end": {"line": 95, "column": 7}}, {"start": {}, "end": {}}], "line": 92}, "4": {"loc": {"start": {"line": 92, "column": 10}, "end": {"line": 92, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 92, "column": 10}, "end": {"line": 92, "column": 25}}, {"start": {"line": 92, "column": 29}, "end": {"line": 92, "column": 41}}], "line": 92}, "5": {"loc": {"start": {"line": 98, "column": 6}, "end": {"line": 107, "column": 7}}, "type": "if", "locations": [{"start": {"line": 98, "column": 6}, "end": {"line": 107, "column": 7}}, {"start": {}, "end": {}}], "line": 98}, "6": {"loc": {"start": {"line": 100, "column": 8}, "end": {"line": 103, "column": 9}}, "type": "if", "locations": [{"start": {"line": 100, "column": 8}, "end": {"line": 103, "column": 9}}, {"start": {}, "end": {}}], "line": 100}, "7": {"loc": {"start": {"line": 124, "column": 6}, "end": {"line": 127, "column": 7}}, "type": "if", "locations": [{"start": {"line": 124, "column": 6}, "end": {"line": 127, "column": 7}}, {"start": {}, "end": {}}], "line": 124}, "8": {"loc": {"start": {"line": 133, "column": 6}, "end": {"line": 135, "column": 7}}, "type": "if", "locations": [{"start": {"line": 133, "column": 6}, "end": {"line": 135, "column": 7}}, {"start": {}, "end": {}}], "line": 133}, "9": {"loc": {"start": {"line": 167, "column": 4}, "end": {"line": 169, "column": 5}}, "type": "if", "locations": [{"start": {"line": 167, "column": 4}, "end": {"line": 169, "column": 5}}, {"start": {}, "end": {}}], "line": 167}, "10": {"loc": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 19}}, {"start": {"line": 167, "column": 23}, "end": {"line": 167, "column": 56}}], "line": 167}, "11": {"loc": {"start": {"line": 183, "column": 4}, "end": {"line": 185, "column": 5}}, "type": "if", "locations": [{"start": {"line": 183, "column": 4}, "end": {"line": 185, "column": 5}}, {"start": {}, "end": {}}], "line": 183}, "12": {"loc": {"start": {"line": 209, "column": 4}, "end": {"line": 211, "column": 5}}, "type": "if", "locations": [{"start": {"line": 209, "column": 4}, "end": {"line": 211, "column": 5}}, {"start": {}, "end": {}}], "line": 209}, "13": {"loc": {"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": 27}}, {"start": {"line": 209, "column": 31}, "end": {"line": 209, "column": 43}}], "line": 209}, "14": {"loc": {"start": {"line": 231, "column": 4}, "end": {"line": 233, "column": 5}}, "type": "if", "locations": [{"start": {"line": 231, "column": 4}, "end": {"line": 233, "column": 5}}, {"start": {}, "end": {}}], "line": 231}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/errorHandler.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/errorHandler.js", "statementMap": {"0": {"start": {"line": 11, "column": 21}, "end": {"line": 11, "column": 47}}, "1": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 82}}, "2": {"start": {"line": 17, "column": 0}, "end": {"line": 31, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "9c61519c776c658e2ee0886f422ccefeaacf2eff"}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/errorMonitor.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/errorMonitor.js", "statementMap": {"0": {"start": {"line": 11, "column": 21}, "end": {"line": 11, "column": 47}}, "1": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 82}}, "2": {"start": {"line": 17, "column": 0}, "end": {"line": 24, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/html-sanitizer.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/html-sanitizer.js", "statementMap": {"0": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 44}}, "1": {"start": {"line": 6, "column": 18}, "end": {"line": 6, "column": 34}}, "2": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 42}}, "3": {"start": {"line": 8, "column": 19}, "end": {"line": 8, "column": 43}}, "4": {"start": {"line": 11, "column": 15}, "end": {"line": 11, "column": 35}}, "5": {"start": {"line": 12, "column": 18}, "end": {"line": 12, "column": 41}}, "6": {"start": {"line": 20, "column": 21}, "end": {"line": 38, "column": 1}}, "7": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 31}}, "8": {"start": {"line": 21, "column": 16}, "end": {"line": 21, "column": 31}}, "9": {"start": {"line": 23, "column": 2}, "end": {"line": 37, "column": 3}}, "10": {"start": {"line": 24, "column": 19}, "end": {"line": 30, "column": 5}}, "11": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 47}}, "12": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 47}}, "13": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 45}}, "14": {"start": {"line": 46, "column": 23}, "end": {"line": 63, "column": 1}}, "15": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 50}}, "16": {"start": {"line": 47, "column": 39}, "end": {"line": 47, "column": 50}}, "17": {"start": {"line": 49, "column": 17}, "end": {"line": 49, "column": 19}}, "18": {"start": {"line": 51, "column": 2}, "end": {"line": 60, "column": 3}}, "19": {"start": {"line": 53, "column": 4}, "end": {"line": 59, "column": 5}}, "20": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 85}}, "21": {"start": {"line": 55, "column": 11}, "end": {"line": 59, "column": 5}}, "22": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 73}}, "23": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 45}}, "24": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 16}}, "25": {"start": {"line": 71, "column": 22}, "end": {"line": 82, "column": 1}}, "26": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 38}}, "27": {"start": {"line": 72, "column": 27}, "end": {"line": 72, "column": 38}}, "28": {"start": {"line": 74, "column": 2}, "end": {"line": 81, "column": 5}}, "29": {"start": {"line": 75, "column": 4}, "end": {"line": 79, "column": 5}}, "30": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 39}}, "31": {"start": {"line": 77, "column": 11}, "end": {"line": 79, "column": 5}}, "32": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 46}}, "33": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 16}}, "34": {"start": {"line": 84, "column": 0}, "end": {"line": 88, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 22}}, "loc": {"start": {"line": 20, "column": 57}, "end": {"line": 38, "column": 1}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 46, "column": 23}, "end": {"line": 46, "column": 24}}, "loc": {"start": {"line": 46, "column": 49}, "end": {"line": 63, "column": 1}}, "line": 46}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 71, "column": 22}, "end": {"line": 71, "column": 23}}, "loc": {"start": {"line": 71, "column": 48}, "end": {"line": 82, "column": 1}}, "line": 71}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 74, "column": 17}, "end": {"line": 74, "column": 18}}, "loc": {"start": {"line": 74, "column": 25}, "end": {"line": 81, "column": 3}}, "line": 74}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 31}, "end": {"line": 20, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 47}, "end": {"line": 20, "column": 52}}], "line": 20}, "1": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 31}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 31}}, {"start": {}, "end": {}}], "line": 21}, "2": {"loc": {"start": {"line": 24, "column": 19}, "end": {"line": 30, "column": 5}}, "type": "cond-expr", "locations": [{"start": {"line": 24, "column": 35}, "end": {"line": 27, "column": 5}}, {"start": {"line": 27, "column": 8}, "end": {"line": 30, "column": 5}}], "line": 24}, "3": {"loc": {"start": {"line": 46, "column": 29}, "end": {"line": 46, "column": 44}}, "type": "default-arg", "locations": [{"start": {"line": 46, "column": 42}, "end": {"line": 46, "column": 44}}], "line": 46}, "4": {"loc": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 50}}, "type": "if", "locations": [{"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 50}}, {"start": {}, "end": {}}], "line": 47}, "5": {"loc": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 10}}, {"start": {"line": 47, "column": 14}, "end": {"line": 47, "column": 37}}], "line": 47}, "6": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 53, "column": 4}, "end": {"line": 59, "column": 5}}, {"start": {"line": 55, "column": 11}, "end": {"line": 59, "column": 5}}], "line": 53}, "7": {"loc": {"start": {"line": 55, "column": 11}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 55, "column": 11}, "end": {"line": 59, "column": 5}}, {"start": {"line": 57, "column": 11}, "end": {"line": 59, "column": 5}}], "line": 55}, "8": {"loc": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": 40}}, {"start": {"line": 55, "column": 44}, "end": {"line": 55, "column": 58}}], "line": 55}, "9": {"loc": {"start": {"line": 71, "column": 28}, "end": {"line": 71, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 71, "column": 41}, "end": {"line": 71, "column": 43}}], "line": 71}, "10": {"loc": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 38}}, "type": "if", "locations": [{"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 38}}, {"start": {}, "end": {}}], "line": 72}, "11": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 79, "column": 5}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 79, "column": 5}}, {"start": {"line": 77, "column": 11}, "end": {"line": 79, "column": 5}}], "line": 75}, "12": {"loc": {"start": {"line": 77, "column": 11}, "end": {"line": 79, "column": 5}}, "type": "if", "locations": [{"start": {"line": 77, "column": 11}, "end": {"line": 79, "column": 5}}, {"start": {}, "end": {}}], "line": 77}, "13": {"loc": {"start": {"line": 77, "column": 15}, "end": {"line": 77, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 15}, "end": {"line": 77, "column": 39}}, {"start": {"line": 77, "column": 43}, "end": {"line": 77, "column": 56}}], "line": 77}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/index.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/index.js", "statementMap": {"0": {"start": {"line": 7, "column": 21}, "end": {"line": 7, "column": 47}}, "1": {"start": {"line": 10, "column": 28}, "end": {"line": 10, "column": 66}}, "2": {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 45}}, "3": {"start": {"line": 16, "column": 17}, "end": {"line": 16, "column": 38}}, "4": {"start": {"line": 19, "column": 20}, "end": {"line": 19, "column": 44}}, "5": {"start": {"line": 22, "column": 15}, "end": {"line": 22, "column": 34}}, "6": {"start": {"line": 25, "column": 30}, "end": {"line": 25, "column": 66}}, "7": {"start": {"line": 28, "column": 0}, "end": {"line": 49, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {}, "b": {}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/register-version-routes.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/register-version-routes.js", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 34}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 73}}, "3": {"start": {"line": 13, "column": 30}, "end": {"line": 45, "column": 1}}, "4": {"start": {"line": 14, "column": 29}, "end": {"line": 14, "column": 42}}, "5": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 41}}, "6": {"start": {"line": 20, "column": 2}, "end": {"line": 42, "column": 5}}, "7": {"start": {"line": 21, "column": 31}, "end": {"line": 21, "column": 36}}, "8": {"start": {"line": 24, "column": 4}, "end": {"line": 41, "column": 7}}, "9": {"start": {"line": 25, "column": 6}, "end": {"line": 40, "column": 7}}, "10": {"start": {"line": 27, "column": 28}, "end": {"line": 27, "column": 79}}, "11": {"start": {"line": 29, "column": 8}, "end": {"line": 37, "column": 19}}, "12": {"start": {"line": 31, "column": 10}, "end": {"line": 33, "column": 11}}, "13": {"start": {"line": 32, "column": 12}, "end": {"line": 32, "column": 26}}, "14": {"start": {"line": 36, "column": 10}, "end": {"line": 36, "column": 31}}, "15": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 65}}, "16": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 29}}, "17": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 39}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 30}, "end": {"line": 13, "column": 31}}, "loc": {"start": {"line": 13, "column": 54}, "end": {"line": 45, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 18}}, "loc": {"start": {"line": 20, "column": 26}, "end": {"line": 42, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 24, "column": 37}, "end": {"line": 24, "column": 38}}, "loc": {"start": {"line": 24, "column": 60}, "end": {"line": 41, "column": 5}}, "line": 24}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 29, "column": 29}, "end": {"line": 29, "column": 30}}, "loc": {"start": {"line": 29, "column": 49}, "end": {"line": 37, "column": 9}}, "line": 29}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 6}, "end": {"line": 40, "column": 7}}, "type": "if", "locations": [{"start": {"line": 25, "column": 6}, "end": {"line": 40, "column": 7}}, {"start": {}, "end": {}}], "line": 25}, "1": {"loc": {"start": {"line": 27, "column": 28}, "end": {"line": 27, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 27, "column": 35}, "end": {"line": 27, "column": 59}}, {"start": {"line": 27, "column": 62}, "end": {"line": 27, "column": 79}}], "line": 27}, "2": {"loc": {"start": {"line": 31, "column": 10}, "end": {"line": 33, "column": 11}}, "type": "if", "locations": [{"start": {"line": 31, "column": 10}, "end": {"line": 33, "column": 11}}, {"start": {}, "end": {}}], "line": 31}, "3": {"loc": {"start": {"line": 39, "column": 30}, "end": {"line": 39, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 30}, "end": {"line": 39, "column": 41}}, {"start": {"line": 39, "column": 45}, "end": {"line": 39, "column": 48}}], "line": 39}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/safe-fs.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/safe-fs.js", "statementMap": {"0": {"start": {"line": 6, "column": 11}, "end": {"line": 6, "column": 24}}, "1": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 28}}, "2": {"start": {"line": 18, "column": 29}, "end": {"line": 18, "column": 51}}, "3": {"start": {"line": 21, "column": 19}, "end": {"line": 21, "column": 65}}, "4": {"start": {"line": 24, "column": 2}, "end": {"line": 26, "column": 3}}, "5": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 47}}, "6": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 18}}, "7": {"start": {"line": 38, "column": 2}, "end": {"line": 43, "column": 3}}, "8": {"start": {"line": 39, "column": 21}, "end": {"line": 39, "column": 60}}, "9": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 35}}, "10": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 17}}, "11": {"start": {"line": 53, "column": 2}, "end": {"line": 58, "column": 3}}, "12": {"start": {"line": 54, "column": 21}, "end": {"line": 54, "column": 60}}, "13": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 33}}, "14": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 16}}, "15": {"start": {"line": 68, "column": 2}, "end": {"line": 73, "column": 3}}, "16": {"start": {"line": 69, "column": 21}, "end": {"line": 69, "column": 60}}, "17": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 36}}, "18": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 14}}, "19": {"start": {"line": 84, "column": 2}, "end": {"line": 89, "column": 3}}, "20": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": 60}}, "21": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 46}}, "22": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 16}}, "23": {"start": {"line": 101, "column": 2}, "end": {"line": 107, "column": 3}}, "24": {"start": {"line": 102, "column": 21}, "end": {"line": 102, "column": 60}}, "25": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 46}}, "26": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 16}}, "27": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 17}}, "28": {"start": {"line": 118, "column": 2}, "end": {"line": 124, "column": 3}}, "29": {"start": {"line": 119, "column": 21}, "end": {"line": 119, "column": 60}}, "30": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 36}}, "31": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 16}}, "32": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 17}}, "33": {"start": {"line": 134, "column": 2}, "end": {"line": 140, "column": 3}}, "34": {"start": {"line": 135, "column": 21}, "end": {"line": 135, "column": 60}}, "35": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": 28}}, "36": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 16}}, "37": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": 17}}, "38": {"start": {"line": 150, "column": 2}, "end": {"line": 156, "column": 3}}, "39": {"start": {"line": 151, "column": 21}, "end": {"line": 151, "column": 60}}, "40": {"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": 27}}, "41": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 16}}, "42": {"start": {"line": 155, "column": 4}, "end": {"line": 155, "column": 17}}, "43": {"start": {"line": 159, "column": 0}, "end": {"line": 169, "column": 2}}}, "fnMap": {"0": {"name": "resolveSafePath", "decl": {"start": {"line": 16, "column": 9}, "end": {"line": 16, "column": 24}}, "loc": {"start": {"line": 16, "column": 49}, "end": {"line": 29, "column": 1}}, "line": 16}, "1": {"name": "existsSync", "decl": {"start": {"line": 37, "column": 9}, "end": {"line": 37, "column": 19}}, "loc": {"start": {"line": 37, "column": 44}, "end": {"line": 44, "column": 1}}, "line": 37}, "2": {"name": "statSync", "decl": {"start": {"line": 52, "column": 9}, "end": {"line": 52, "column": 17}}, "loc": {"start": {"line": 52, "column": 42}, "end": {"line": 59, "column": 1}}, "line": 52}, "3": {"name": "readdirSync", "decl": {"start": {"line": 67, "column": 9}, "end": {"line": 67, "column": 20}}, "loc": {"start": {"line": 67, "column": 45}, "end": {"line": 74, "column": 1}}, "line": 67}, "4": {"name": "readFileSync", "decl": {"start": {"line": 83, "column": 9}, "end": {"line": 83, "column": 21}}, "loc": {"start": {"line": 83, "column": 55}, "end": {"line": 90, "column": 1}}, "line": 83}, "5": {"name": "writeFileSync", "decl": {"start": {"line": 100, "column": 9}, "end": {"line": 100, "column": 22}}, "loc": {"start": {"line": 100, "column": 62}, "end": {"line": 108, "column": 1}}, "line": 100}, "6": {"name": "mkdirSync", "decl": {"start": {"line": 117, "column": 9}, "end": {"line": 117, "column": 18}}, "loc": {"start": {"line": 117, "column": 52}, "end": {"line": 125, "column": 1}}, "line": 117}, "7": {"name": "unlinkSync", "decl": {"start": {"line": 133, "column": 9}, "end": {"line": 133, "column": 19}}, "loc": {"start": {"line": 133, "column": 44}, "end": {"line": 141, "column": 1}}, "line": 133}, "8": {"name": "rmdirSync", "decl": {"start": {"line": 149, "column": 9}, "end": {"line": 149, "column": 18}}, "loc": {"start": {"line": 149, "column": 43}, "end": {"line": 157, "column": 1}}, "line": 149}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 2}, "end": {"line": 26, "column": 3}}, "type": "if", "locations": [{"start": {"line": 24, "column": 2}, "end": {"line": 26, "column": 3}}, {"start": {}, "end": {}}], "line": 24}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/safe-object.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/safe-object.js", "statementMap": {"0": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "1": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, "2": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 24}}, "3": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "4": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 21}}, "5": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 22}}, "6": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "7": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 17}}, "8": {"start": {"line": 42, "column": 2}, "end": {"line": 45, "column": 3}}, "9": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 37}}, "10": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 17}}, "11": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 20}}, "12": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 14}}, "13": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "14": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 18}}, "15": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, "16": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 18}}, "17": {"start": {"line": 67, "column": 17}, "end": {"line": 67, "column": 30}}, "18": {"start": {"line": 69, "column": 2}, "end": {"line": 87, "column": 3}}, "19": {"start": {"line": 70, "column": 4}, "end": {"line": 86, "column": 5}}, "20": {"start": {"line": 72, "column": 6}, "end": {"line": 75, "column": 7}}, "21": {"start": {"line": 73, "column": 8}, "end": {"line": 73, "column": 40}}, "22": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 17}}, "23": {"start": {"line": 77, "column": 20}, "end": {"line": 77, "column": 31}}, "24": {"start": {"line": 79, "column": 6}, "end": {"line": 85, "column": 7}}, "25": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 52}}, "26": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 28}}, "27": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 16}}, "28": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 29}}, "29": {"start": {"line": 109, "column": 2}, "end": {"line": 122, "column": 3}}, "30": {"start": {"line": 111, "column": 19}, "end": {"line": 111, "column": 44}}, "31": {"start": {"line": 114, "column": 4}, "end": {"line": 116, "column": 5}}, "32": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 36}}, "33": {"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": 18}}, "34": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 47}}, "35": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 16}}, "36": {"start": {"line": 131, "column": 2}, "end": {"line": 133, "column": 3}}, "37": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 15}}, "38": {"start": {"line": 136, "column": 2}, "end": {"line": 138, "column": 3}}, "39": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 49}}, "40": {"start": {"line": 137, "column": 27}, "end": {"line": 137, "column": 47}}, "41": {"start": {"line": 141, "column": 17}, "end": {"line": 141, "column": 35}}, "42": {"start": {"line": 144, "column": 2}, "end": {"line": 161, "column": 3}}, "43": {"start": {"line": 145, "column": 4}, "end": {"line": 160, "column": 5}}, "44": {"start": {"line": 147, "column": 6}, "end": {"line": 150, "column": 7}}, "45": {"start": {"line": 148, "column": 8}, "end": {"line": 148, "column": 38}}, "46": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 17}}, "47": {"start": {"line": 152, "column": 20}, "end": {"line": 152, "column": 28}}, "48": {"start": {"line": 155, "column": 6}, "end": {"line": 159, "column": 7}}, "49": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 44}}, "50": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 28}}, "51": {"start": {"line": 163, "column": 2}, "end": {"line": 163, "column": 16}}, "52": {"start": {"line": 166, "column": 0}, "end": {"line": 173, "column": 2}}}, "fnMap": {"0": {"name": "safeGet", "decl": {"start": {"line": 16, "column": 9}, "end": {"line": 16, "column": 16}}, "loc": {"start": {"line": 16, "column": 54}, "end": {"line": 26, "column": 1}}, "line": 16}, "1": {"name": "safeSet", "decl": {"start": {"line": 36, "column": 9}, "end": {"line": 36, "column": 16}}, "loc": {"start": {"line": 36, "column": 35}, "end": {"line": 49, "column": 1}}, "line": 36}, "2": {"name": "safeMerge", "decl": {"start": {"line": 58, "column": 9}, "end": {"line": 58, "column": 18}}, "loc": {"start": {"line": 58, "column": 35}, "end": {"line": 90, "column": 1}}, "line": 58}, "3": {"name": "createSafeObject", "decl": {"start": {"line": 97, "column": 9}, "end": {"line": 97, "column": 25}}, "loc": {"start": {"line": 97, "column": 28}, "end": {"line": 99, "column": 1}}, "line": 97}, "4": {"name": "safeJsonParse", "decl": {"start": {"line": 108, "column": 9}, "end": {"line": 108, "column": 22}}, "loc": {"start": {"line": 108, "column": 50}, "end": {"line": 123, "column": 1}}, "line": 108}, "5": {"name": "sanitizeObject", "decl": {"start": {"line": 130, "column": 9}, "end": {"line": 130, "column": 23}}, "loc": {"start": {"line": 130, "column": 29}, "end": {"line": 164, "column": 1}}, "line": 130}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 137, "column": 19}, "end": {"line": 137, "column": 20}}, "loc": {"start": {"line": 137, "column": 27}, "end": {"line": 137, "column": 47}}, "line": 137}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 28}, "end": {"line": 16, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 16, "column": 43}, "end": {"line": 16, "column": 52}}], "line": 16}, "1": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, "type": "if", "locations": [{"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, {"start": {}, "end": {}}], "line": 17}, "2": {"loc": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 10}}, {"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 37}}], "line": 17}, "3": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, {"start": {}, "end": {}}], "line": 21}, "4": {"loc": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "type": "if", "locations": [{"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, {"start": {}, "end": {}}], "line": 37}, "5": {"loc": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 10}}, {"start": {"line": 37, "column": 14}, "end": {"line": 37, "column": 37}}], "line": 37}, "6": {"loc": {"start": {"line": 42, "column": 2}, "end": {"line": 45, "column": 3}}, "type": "if", "locations": [{"start": {"line": 42, "column": 2}, "end": {"line": 45, "column": 3}}, {"start": {}, "end": {}}], "line": 42}, "7": {"loc": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 26}}, {"start": {"line": 42, "column": 30}, "end": {"line": 42, "column": 52}}, {"start": {"line": 42, "column": 56}, "end": {"line": 42, "column": 76}}], "line": 42}, "8": {"loc": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {}, "end": {}}], "line": 59}, "9": {"loc": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 13}}, {"start": {"line": 59, "column": 17}, "end": {"line": 59, "column": 43}}], "line": 59}, "10": {"loc": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, "type": "if", "locations": [{"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, {"start": {}, "end": {}}], "line": 63}, "11": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 13}}, {"start": {"line": 63, "column": 17}, "end": {"line": 63, "column": 43}}], "line": 63}, "12": {"loc": {"start": {"line": 70, "column": 4}, "end": {"line": 86, "column": 5}}, "type": "if", "locations": [{"start": {"line": 70, "column": 4}, "end": {"line": 86, "column": 5}}, {"start": {}, "end": {}}], "line": 70}, "13": {"loc": {"start": {"line": 72, "column": 6}, "end": {"line": 75, "column": 7}}, "type": "if", "locations": [{"start": {"line": 72, "column": 6}, "end": {"line": 75, "column": 7}}, {"start": {}, "end": {}}], "line": 72}, "14": {"loc": {"start": {"line": 72, "column": 10}, "end": {"line": 72, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 10}, "end": {"line": 72, "column": 29}}, {"start": {"line": 72, "column": 33}, "end": {"line": 72, "column": 54}}, {"start": {"line": 72, "column": 58}, "end": {"line": 72, "column": 77}}], "line": 72}, "15": {"loc": {"start": {"line": 79, "column": 6}, "end": {"line": 85, "column": 7}}, "type": "if", "locations": [{"start": {"line": 79, "column": 6}, "end": {"line": 85, "column": 7}}, {"start": {"line": 83, "column": 13}, "end": {"line": 85, "column": 7}}], "line": 79}, "16": {"loc": {"start": {"line": 79, "column": 10}, "end": {"line": 80, "column": 96}}, "type": "binary-expr", "locations": [{"start": {"line": 79, "column": 10}, "end": {"line": 79, "column": 24}}, {"start": {"line": 79, "column": 28}, "end": {"line": 79, "column": 53}}, {"start": {"line": 79, "column": 57}, "end": {"line": 79, "column": 78}}, {"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 30}}, {"start": {"line": 80, "column": 34}, "end": {"line": 80, "column": 65}}, {"start": {"line": 80, "column": 69}, "end": {"line": 80, "column": 96}}], "line": 79}, "17": {"loc": {"start": {"line": 108, "column": 29}, "end": {"line": 108, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 108, "column": 39}, "end": {"line": 108, "column": 48}}], "line": 108}, "18": {"loc": {"start": {"line": 114, "column": 4}, "end": {"line": 116, "column": 5}}, "type": "if", "locations": [{"start": {"line": 114, "column": 4}, "end": {"line": 116, "column": 5}}, {"start": {}, "end": {}}], "line": 114}, "19": {"loc": {"start": {"line": 114, "column": 8}, "end": {"line": 114, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 8}, "end": {"line": 114, "column": 14}}, {"start": {"line": 114, "column": 18}, "end": {"line": 114, "column": 44}}], "line": 114}, "20": {"loc": {"start": {"line": 131, "column": 2}, "end": {"line": 133, "column": 3}}, "type": "if", "locations": [{"start": {"line": 131, "column": 2}, "end": {"line": 133, "column": 3}}, {"start": {}, "end": {}}], "line": 131}, "21": {"loc": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 10}}, {"start": {"line": 131, "column": 14}, "end": {"line": 131, "column": 37}}], "line": 131}, "22": {"loc": {"start": {"line": 136, "column": 2}, "end": {"line": 138, "column": 3}}, "type": "if", "locations": [{"start": {"line": 136, "column": 2}, "end": {"line": 138, "column": 3}}, {"start": {}, "end": {}}], "line": 136}, "23": {"loc": {"start": {"line": 145, "column": 4}, "end": {"line": 160, "column": 5}}, "type": "if", "locations": [{"start": {"line": 145, "column": 4}, "end": {"line": 160, "column": 5}}, {"start": {}, "end": {}}], "line": 145}, "24": {"loc": {"start": {"line": 147, "column": 6}, "end": {"line": 150, "column": 7}}, "type": "if", "locations": [{"start": {"line": 147, "column": 6}, "end": {"line": 150, "column": 7}}, {"start": {}, "end": {}}], "line": 147}, "25": {"loc": {"start": {"line": 147, "column": 10}, "end": {"line": 147, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 147, "column": 10}, "end": {"line": 147, "column": 29}}, {"start": {"line": 147, "column": 33}, "end": {"line": 147, "column": 54}}, {"start": {"line": 147, "column": 58}, "end": {"line": 147, "column": 77}}], "line": 147}, "26": {"loc": {"start": {"line": 155, "column": 6}, "end": {"line": 159, "column": 7}}, "type": "if", "locations": [{"start": {"line": 155, "column": 6}, "end": {"line": 159, "column": 7}}, {"start": {"line": 157, "column": 13}, "end": {"line": 159, "column": 7}}], "line": 155}, "27": {"loc": {"start": {"line": 155, "column": 10}, "end": {"line": 155, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 155, "column": 10}, "end": {"line": 155, "column": 24}}, {"start": {"line": 155, "column": 28}, "end": {"line": 155, "column": 53}}], "line": 155}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0, 0], "15": [0, 0], "16": [0, 0, 0, 0, 0, 0], "17": [0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0, 0], "26": [0, 0], "27": [0, 0]}}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/transaction-manager.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/transaction-manager.js", "statementMap": {"0": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 42}}, "1": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "2": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 15}}, "3": {"start": {"line": 32, "column": 31}, "end": {"line": 36, "column": 5}}, "4": {"start": {"line": 39, "column": 4}, "end": {"line": 43, "column": 7}}, "5": {"start": {"line": 40, "column": 6}, "end": {"line": 42, "column": 7}}, "6": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 39}}, "7": {"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": 71}}, "8": {"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 32}}, "9": {"start": {"line": 49, "column": 4}, "end": {"line": 76, "column": 5}}, "10": {"start": {"line": 51, "column": 21}, "end": {"line": 51, "column": 48}}, "11": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 33}}, "12": {"start": {"line": 57, "column": 23}, "end": {"line": 57, "column": 45}}, "13": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 58}}, "14": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 20}}, "15": {"start": {"line": 63, "column": 6}, "end": {"line": 68, "column": 7}}, "16": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 37}}, "17": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 61}}, "18": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 70}}, "19": {"start": {"line": 71, "column": 23}, "end": {"line": 71, "column": 45}}, "20": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 74}}, "21": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 18}}, "22": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 75}}, "23": {"start": {"line": 97, "column": 4}, "end": {"line": 104, "column": 16}}, "24": {"start": {"line": 98, "column": 22}, "end": {"line": 98, "column": 24}}, "25": {"start": {"line": 99, "column": 6}, "end": {"line": 102, "column": 7}}, "26": {"start": {"line": 100, "column": 23}, "end": {"line": 100, "column": 51}}, "27": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 29}}, "28": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 21}}, "29": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 15}}, "30": {"start": {"line": 123, "column": 20}, "end": {"line": 126, "column": 5}}, "31": {"start": {"line": 129, "column": 4}, "end": {"line": 146, "column": 5}}, "32": {"start": {"line": 129, "column": 17}, "end": {"line": 129, "column": 18}}, "33": {"start": {"line": 130, "column": 20}, "end": {"line": 130, "column": 49}}, "34": {"start": {"line": 132, "column": 6}, "end": {"line": 145, "column": 83}}, "35": {"start": {"line": 133, "column": 8}, "end": {"line": 144, "column": 9}}, "36": {"start": {"line": 134, "column": 10}, "end": {"line": 143, "column": 11}}, "37": {"start": {"line": 135, "column": 27}, "end": {"line": 135, "column": 63}}, "38": {"start": {"line": 136, "column": 12}, "end": {"line": 136, "column": 51}}, "39": {"start": {"line": 138, "column": 12}, "end": {"line": 138, "column": 50}}, "40": {"start": {"line": 140, "column": 12}, "end": {"line": 142, "column": 13}}, "41": {"start": {"line": 141, "column": 14}, "end": {"line": 141, "column": 26}}, "42": {"start": {"line": 148, "column": 4}, "end": {"line": 148, "column": 19}}, "43": {"start": {"line": 153, "column": 27}, "end": {"line": 153, "column": 51}}, "44": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 36}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 3}}, "loc": {"start": {"line": 23, "column": 49}, "end": {"line": 77, "column": 3}}, "line": 23}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 39, "column": 44}, "end": {"line": 39, "column": 45}}, "loc": {"start": {"line": 39, "column": 51}, "end": {"line": 43, "column": 5}}, "line": 39}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 3}}, "loc": {"start": {"line": 85, "column": 57}, "end": {"line": 87, "column": 3}}, "line": 85}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 3}}, "loc": {"start": {"line": 96, "column": 61}, "end": {"line": 105, "column": 3}}, "line": 96}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 97, "column": 33}, "end": {"line": 97, "column": 34}}, "loc": {"start": {"line": 97, "column": 56}, "end": {"line": 104, "column": 5}}, "line": 97}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 116, "column": 2}, "end": {"line": 116, "column": 3}}, "loc": {"start": {"line": 116, "column": 73}, "end": {"line": 149, "column": 3}}, "line": 116}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 132, "column": 34}, "end": {"line": 132, "column": 35}}, "loc": {"start": {"line": 132, "column": 57}, "end": {"line": 145, "column": 7}}, "line": 132}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 35}, "end": {"line": 23, "column": 47}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 45}, "end": {"line": 23, "column": 47}}], "line": 23}, "1": {"loc": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 22}}, "type": "default-arg", "locations": [{"start": {"line": 25, "column": 17}, "end": {"line": 25, "column": 22}}], "line": 25}, "2": {"loc": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 25}}, "type": "default-arg", "locations": [{"start": {"line": 28, "column": 16}, "end": {"line": 28, "column": 25}}], "line": 28}, "3": {"loc": {"start": {"line": 40, "column": 6}, "end": {"line": 42, "column": 7}}, "type": "if", "locations": [{"start": {"line": 40, "column": 6}, "end": {"line": 42, "column": 7}}, {"start": {}, "end": {}}], "line": 40}, "4": {"loc": {"start": {"line": 85, "column": 43}, "end": {"line": 85, "column": 55}}, "type": "default-arg", "locations": [{"start": {"line": 85, "column": 53}, "end": {"line": 85, "column": 55}}], "line": 85}, "5": {"loc": {"start": {"line": 96, "column": 47}, "end": {"line": 96, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 96, "column": 57}, "end": {"line": 96, "column": 59}}], "line": 96}, "6": {"loc": {"start": {"line": 116, "column": 59}, "end": {"line": 116, "column": 71}}, "type": "default-arg", "locations": [{"start": {"line": 116, "column": 69}, "end": {"line": 116, "column": 71}}], "line": 116}, "7": {"loc": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 118, "column": 18}, "end": {"line": 118, "column": 21}}], "line": 118}, "8": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 119, "column": 24}, "end": {"line": 119, "column": 29}}], "line": 119}, "9": {"loc": {"start": {"line": 140, "column": 12}, "end": {"line": 142, "column": 13}}, "type": "if", "locations": [{"start": {"line": 140, "column": 12}, "end": {"line": 142, "column": 13}}, {"start": {}, "end": {}}], "line": 140}}, "s": {"0": 1, "1": 1, "2": 13, "3": 13, "4": 13, "5": 39, "6": 24, "7": 13, "8": 13, "9": 13, "10": 13, "11": 10, "12": 10, "13": 10, "14": 10, "15": 3, "16": 3, "17": 3, "18": 0, "19": 3, "20": 3, "21": 3, "22": 1, "23": 2, "24": 2, "25": 2, "26": 5, "27": 4, "28": 1, "29": 3, "30": 3, "31": 3, "32": 3, "33": 7, "34": 7, "35": 7, "36": 12, "37": 12, "38": 10, "39": 2, "40": 2, "41": 1, "42": 2, "43": 1, "44": 1}, "f": {"0": 13, "1": 39, "2": 1, "3": 2, "4": 2, "5": 3, "6": 7}, "b": {"0": [0], "1": [11], "2": [0], "3": [24, 15], "4": [0], "5": [0], "6": [0], "7": [0], "8": [1], "9": [1, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "f5268fbe961e48bda83c805d44a60a9b2eda13fe"}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/unified-error.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/unified-error.js", "statementMap": {"0": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 42}}, "1": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 44}}, "2": {"start": {"line": 8, "column": 11}, "end": {"line": 8, "column": 24}}, "3": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 28}}, "4": {"start": {"line": 12, "column": 19}, "end": {"line": 25, "column": 1}}, "5": {"start": {"line": 28, "column": 26}, "end": {"line": 28, "column": 29}}, "6": {"start": {"line": 31, "column": 22}, "end": {"line": 31, "column": 63}}, "7": {"start": {"line": 36, "column": 21}, "end": {"line": 40, "column": 1}}, "8": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "9": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 53}}, "10": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 19}}, "11": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 27}}, "12": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 21}}, "13": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 25}}, "14": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 27}}, "15": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 39}}, "16": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 52}}, "17": {"start": {"line": 74, "column": 20}, "end": {"line": 76, "column": 1}}, "18": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 54}}, "19": {"start": {"line": 85, "column": 20}, "end": {"line": 143, "column": 1}}, "20": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 27}}, "21": {"start": {"line": 90, "column": 20}, "end": {"line": 90, "column": 43}}, "22": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 73}}, "23": {"start": {"line": 94, "column": 20}, "end": {"line": 94, "column": 49}}, "24": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 73}}, "25": {"start": {"line": 98, "column": 15}, "end": {"line": 98, "column": 64}}, "26": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 63}}, "27": {"start": {"line": 102, "column": 20}, "end": {"line": 102, "column": 44}}, "28": {"start": {"line": 103, "column": 22}, "end": {"line": 114, "column": 3}}, "29": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": 47}}, "30": {"start": {"line": 120, "column": 2}, "end": {"line": 122, "column": 3}}, "31": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 34}}, "32": {"start": {"line": 125, "column": 2}, "end": {"line": 140, "column": 3}}, "33": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 19}}, "34": {"start": {"line": 127, "column": 17}, "end": {"line": 127, "column": 40}}, "35": {"start": {"line": 128, "column": 20}, "end": {"line": 128, "column": 60}}, "36": {"start": {"line": 130, "column": 15}, "end": {"line": 130, "column": 17}}, "37": {"start": {"line": 131, "column": 4}, "end": {"line": 134, "column": 5}}, "38": {"start": {"line": 132, "column": 22}, "end": {"line": 132, "column": 54}}, "39": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 33}}, "40": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": 27}}, "41": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 69}}, "42": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": 44}}, "43": {"start": {"line": 142, "column": 2}, "end": {"line": 142, "column": 21}}, "44": {"start": {"line": 149, "column": 22}, "end": {"line": 154, "column": 1}}, "45": {"start": {"line": 150, "column": 2}, "end": {"line": 153, "column": 4}}, "46": {"start": {"line": 159, "column": 24}, "end": {"line": 168, "column": 1}}, "47": {"start": {"line": 160, "column": 2}, "end": {"line": 160, "column": 29}}, "48": {"start": {"line": 161, "column": 2}, "end": {"line": 161, "column": 25}}, "49": {"start": {"line": 162, "column": 2}, "end": {"line": 162, "column": 25}}, "50": {"start": {"line": 163, "column": 2}, "end": {"line": 163, "column": 25}}, "51": {"start": {"line": 164, "column": 2}, "end": {"line": 164, "column": 31}}, "52": {"start": {"line": 165, "column": 2}, "end": {"line": 165, "column": 36}}, "53": {"start": {"line": 167, "column": 2}, "end": {"line": 167, "column": 25}}, "54": {"start": {"line": 175, "column": 27}, "end": {"line": 188, "column": 1}}, "55": {"start": {"line": 176, "column": 2}, "end": {"line": 187, "column": 3}}, "56": {"start": {"line": 177, "column": 20}, "end": {"line": 177, "column": 60}}, "57": {"start": {"line": 178, "column": 4}, "end": {"line": 180, "column": 5}}, "58": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 16}}, "59": {"start": {"line": 182, "column": 20}, "end": {"line": 182, "column": 52}}, "60": {"start": {"line": 183, "column": 4}, "end": {"line": 183, "column": 31}}, "61": {"start": {"line": 185, "column": 4}, "end": {"line": 185, "column": 43}}, "62": {"start": {"line": 186, "column": 4}, "end": {"line": 186, "column": 14}}, "63": {"start": {"line": 194, "column": 34}, "end": {"line": 207, "column": 1}}, "64": {"start": {"line": 195, "column": 2}, "end": {"line": 206, "column": 3}}, "65": {"start": {"line": 196, "column": 4}, "end": {"line": 196, "column": 19}}, "66": {"start": {"line": 197, "column": 18}, "end": {"line": 197, "column": 47}}, "67": {"start": {"line": 198, "column": 4}, "end": {"line": 202, "column": 17}}, "68": {"start": {"line": 199, "column": 22}, "end": {"line": 199, "column": 44}}, "69": {"start": {"line": 200, "column": 19}, "end": {"line": 200, "column": 44}}, "70": {"start": {"line": 204, "column": 4}, "end": {"line": 204, "column": 47}}, "71": {"start": {"line": 205, "column": 4}, "end": {"line": 205, "column": 14}}, "72": {"start": {"line": 215, "column": 20}, "end": {"line": 237, "column": 1}}, "73": {"start": {"line": 216, "column": 23}, "end": {"line": 216, "column": 53}}, "74": {"start": {"line": 219, "column": 2}, "end": {"line": 219, "column": 50}}, "75": {"start": {"line": 221, "column": 2}, "end": {"line": 223, "column": 3}}, "76": {"start": {"line": 222, "column": 4}, "end": {"line": 222, "column": 30}}, "77": {"start": {"line": 226, "column": 2}, "end": {"line": 228, "column": 3}}, "78": {"start": {"line": 227, "column": 4}, "end": {"line": 227, "column": 46}}, "79": {"start": {"line": 231, "column": 2}, "end": {"line": 236, "column": 4}}, "80": {"start": {"line": 247, "column": 23}, "end": {"line": 260, "column": 1}}, "81": {"start": {"line": 249, "column": 2}, "end": {"line": 249, "column": 39}}, "82": {"start": {"line": 251, "column": 25}, "end": {"line": 251, "column": 52}}, "83": {"start": {"line": 253, "column": 2}, "end": {"line": 259, "column": 4}}, "84": {"start": {"line": 269, "column": 30}, "end": {"line": 288, "column": 1}}, "85": {"start": {"line": 270, "column": 24}, "end": {"line": 270, "column": 61}}, "86": {"start": {"line": 270, "column": 42}, "end": {"line": 270, "column": 49}}, "87": {"start": {"line": 271, "column": 2}, "end": {"line": 271, "column": 40}}, "88": {"start": {"line": 274, "column": 16}, "end": {"line": 282, "column": 3}}, "89": {"start": {"line": 279, "column": 6}, "end": {"line": 279, "column": 31}}, "90": {"start": {"line": 280, "column": 6}, "end": {"line": 280, "column": 17}}, "91": {"start": {"line": 285, "column": 2}, "end": {"line": 285, "column": 53}}, "92": {"start": {"line": 287, "column": 2}, "end": {"line": 287, "column": 72}}, "93": {"start": {"line": 298, "column": 28}, "end": {"line": 338, "column": 1}}, "94": {"start": {"line": 299, "column": 16}, "end": {"line": 299, "column": 25}}, "95": {"start": {"line": 300, "column": 13}, "end": {"line": 300, "column": 29}}, "96": {"start": {"line": 301, "column": 15}, "end": {"line": 301, "column": 18}}, "97": {"start": {"line": 302, "column": 16}, "end": {"line": 302, "column": 18}}, "98": {"start": {"line": 305, "column": 2}, "end": {"line": 325, "column": 3}}, "99": {"start": {"line": 306, "column": 4}, "end": {"line": 306, "column": 31}}, "100": {"start": {"line": 307, "column": 4}, "end": {"line": 307, "column": 30}}, "101": {"start": {"line": 308, "column": 4}, "end": {"line": 308, "column": 17}}, "102": {"start": {"line": 309, "column": 4}, "end": {"line": 312, "column": 17}}, "103": {"start": {"line": 310, "column": 6}, "end": {"line": 310, "column": 34}}, "104": {"start": {"line": 311, "column": 6}, "end": {"line": 311, "column": 17}}, "105": {"start": {"line": 313, "column": 9}, "end": {"line": 325, "column": 3}}, "106": {"start": {"line": 314, "column": 4}, "end": {"line": 314, "column": 29}}, "107": {"start": {"line": 315, "column": 4}, "end": {"line": 315, "column": 31}}, "108": {"start": {"line": 316, "column": 4}, "end": {"line": 316, "column": 17}}, "109": {"start": {"line": 317, "column": 9}, "end": {"line": 325, "column": 3}}, "110": {"start": {"line": 318, "column": 4}, "end": {"line": 318, "column": 41}}, "111": {"start": {"line": 319, "column": 4}, "end": {"line": 319, "column": 30}}, "112": {"start": {"line": 320, "column": 4}, "end": {"line": 320, "column": 17}}, "113": {"start": {"line": 321, "column": 4}, "end": {"line": 324, "column": 17}}, "114": {"start": {"line": 322, "column": 6}, "end": {"line": 322, "column": 34}}, "115": {"start": {"line": 323, "column": 6}, "end": {"line": 323, "column": 17}}, "116": {"start": {"line": 328, "column": 19}, "end": {"line": 328, "column": 63}}, "117": {"start": {"line": 331, "column": 2}, "end": {"line": 331, "column": 65}}, "118": {"start": {"line": 334, "column": 2}, "end": {"line": 334, "column": 69}}, "119": {"start": {"line": 335, "column": 2}, "end": {"line": 335, "column": 28}}, "120": {"start": {"line": 337, "column": 2}, "end": {"line": 337, "column": 64}}, "121": {"start": {"line": 348, "column": 29}, "end": {"line": 379, "column": 1}}, "122": {"start": {"line": 349, "column": 16}, "end": {"line": 349, "column": 26}}, "123": {"start": {"line": 350, "column": 13}, "end": {"line": 350, "column": 31}}, "124": {"start": {"line": 351, "column": 15}, "end": {"line": 351, "column": 18}}, "125": {"start": {"line": 352, "column": 16}, "end": {"line": 352, "column": 35}}, "126": {"start": {"line": 355, "column": 2}, "end": {"line": 364, "column": 3}}, "127": {"start": {"line": 356, "column": 4}, "end": {"line": 356, "column": 33}}, "128": {"start": {"line": 357, "column": 4}, "end": {"line": 357, "column": 17}}, "129": {"start": {"line": 358, "column": 9}, "end": {"line": 364, "column": 3}}, "130": {"start": {"line": 359, "column": 4}, "end": {"line": 359, "column": 25}}, "131": {"start": {"line": 360, "column": 4}, "end": {"line": 360, "column": 17}}, "132": {"start": {"line": 361, "column": 9}, "end": {"line": 364, "column": 3}}, "133": {"start": {"line": 362, "column": 4}, "end": {"line": 362, "column": 32}}, "134": {"start": {"line": 363, "column": 4}, "end": {"line": 363, "column": 17}}, "135": {"start": {"line": 367, "column": 19}, "end": {"line": 367, "column": 63}}, "136": {"start": {"line": 370, "column": 2}, "end": {"line": 370, "column": 67}}, "137": {"start": {"line": 373, "column": 2}, "end": {"line": 373, "column": 72}}, "138": {"start": {"line": 374, "column": 2}, "end": {"line": 376, "column": 3}}, "139": {"start": {"line": 375, "column": 4}, "end": {"line": 375, "column": 30}}, "140": {"start": {"line": 378, "column": 2}, "end": {"line": 378, "column": 64}}, "141": {"start": {"line": 388, "column": 32}, "end": {"line": 398, "column": 1}}, "142": {"start": {"line": 389, "column": 2}, "end": {"line": 389, "column": 35}}, "143": {"start": {"line": 392, "column": 16}, "end": {"line": 392, "column": 58}}, "144": {"start": {"line": 395, "column": 2}, "end": {"line": 395, "column": 56}}, "145": {"start": {"line": 397, "column": 2}, "end": {"line": 397, "column": 48}}, "146": {"start": {"line": 407, "column": 28}, "end": {"line": 417, "column": 1}}, "147": {"start": {"line": 408, "column": 2}, "end": {"line": 408, "column": 35}}, "148": {"start": {"line": 411, "column": 16}, "end": {"line": 411, "column": 55}}, "149": {"start": {"line": 414, "column": 2}, "end": {"line": 414, "column": 52}}, "150": {"start": {"line": 416, "column": 2}, "end": {"line": 416, "column": 44}}, "151": {"start": {"line": 427, "column": 30}, "end": {"line": 437, "column": 1}}, "152": {"start": {"line": 428, "column": 2}, "end": {"line": 428, "column": 36}}, "153": {"start": {"line": 431, "column": 16}, "end": {"line": 431, "column": 66}}, "154": {"start": {"line": 434, "column": 2}, "end": {"line": 434, "column": 54}}, "155": {"start": {"line": 436, "column": 2}, "end": {"line": 436, "column": 55}}, "156": {"start": {"line": 447, "column": 28}, "end": {"line": 457, "column": 1}}, "157": {"start": {"line": 448, "column": 2}, "end": {"line": 448, "column": 34}}, "158": {"start": {"line": 451, "column": 16}, "end": {"line": 451, "column": 63}}, "159": {"start": {"line": 454, "column": 2}, "end": {"line": 454, "column": 51}}, "160": {"start": {"line": 456, "column": 2}, "end": {"line": 456, "column": 53}}, "161": {"start": {"line": 466, "column": 38}, "end": {"line": 476, "column": 1}}, "162": {"start": {"line": 467, "column": 2}, "end": {"line": 467, "column": 36}}, "163": {"start": {"line": 470, "column": 16}, "end": {"line": 470, "column": 65}}, "164": {"start": {"line": 473, "column": 2}, "end": {"line": 473, "column": 62}}, "165": {"start": {"line": 475, "column": 2}, "end": {"line": 475, "column": 69}}, "166": {"start": {"line": 486, "column": 30}, "end": {"line": 504, "column": 1}}, "167": {"start": {"line": 487, "column": 2}, "end": {"line": 487, "column": 74}}, "168": {"start": {"line": 488, "column": 2}, "end": {"line": 490, "column": 3}}, "169": {"start": {"line": 489, "column": 4}, "end": {"line": 489, "column": 30}}, "170": {"start": {"line": 493, "column": 19}, "end": {"line": 498, "column": 3}}, "171": {"start": {"line": 501, "column": 2}, "end": {"line": 501, "column": 68}}, "172": {"start": {"line": 503, "column": 2}, "end": {"line": 503, "column": 100}}, "173": {"start": {"line": 510, "column": 31}, "end": {"line": 518, "column": 1}}, "174": {"start": {"line": 511, "column": 2}, "end": {"line": 517, "column": 4}}, "175": {"start": {"line": 513, "column": 4}, "end": {"line": 513, "column": 26}}, "176": {"start": {"line": 516, "column": 4}, "end": {"line": 516, "column": 14}}, "177": {"start": {"line": 525, "column": 28}, "end": {"line": 544, "column": 1}}, "178": {"start": {"line": 526, "column": 2}, "end": {"line": 526, "column": 45}}, "179": {"start": {"line": 526, "column": 22}, "end": {"line": 526, "column": 45}}, "180": {"start": {"line": 529, "column": 2}, "end": {"line": 541, "column": 3}}, "181": {"start": {"line": 531, "column": 4}, "end": {"line": 531, "column": 47}}, "182": {"start": {"line": 533, "column": 4}, "end": {"line": 540, "column": 5}}, "183": {"start": {"line": 535, "column": 21}, "end": {"line": 535, "column": 55}}, "184": {"start": {"line": 536, "column": 6}, "end": {"line": 536, "column": 37}}, "185": {"start": {"line": 538, "column": 6}, "end": {"line": 538, "column": 35}}, "186": {"start": {"line": 539, "column": 6}, "end": {"line": 539, "column": 27}}, "187": {"start": {"line": 543, "column": 2}, "end": {"line": 543, "column": 23}}, "188": {"start": {"line": 546, "column": 0}, "end": {"line": 567, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 36, "column": 21}, "end": {"line": 36, "column": 22}}, "loc": {"start": {"line": 36, "column": 27}, "end": {"line": 40, "column": 1}}, "line": 36}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 3}}, "loc": {"start": {"line": 55, "column": 96}, "end": {"line": 63, "column": 3}}, "line": 55}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 74, "column": 20}, "end": {"line": 74, "column": 21}}, "loc": {"start": {"line": 74, "column": 86}, "end": {"line": 76, "column": 1}}, "line": 74}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 85, "column": 20}, "end": {"line": 85, "column": 21}}, "loc": {"start": {"line": 85, "column": 55}, "end": {"line": 143, "column": 1}}, "line": 85}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 149, "column": 22}, "end": {"line": 149, "column": 23}}, "loc": {"start": {"line": 149, "column": 28}, "end": {"line": 154, "column": 1}}, "line": 149}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 159, "column": 24}, "end": {"line": 159, "column": 25}}, "loc": {"start": {"line": 159, "column": 30}, "end": {"line": 168, "column": 1}}, "line": 159}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 175, "column": 27}, "end": {"line": 175, "column": 28}}, "loc": {"start": {"line": 175, "column": 37}, "end": {"line": 188, "column": 1}}, "line": 175}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 194, "column": 34}, "end": {"line": 194, "column": 35}}, "loc": {"start": {"line": 194, "column": 40}, "end": {"line": 207, "column": 1}}, "line": 194}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 199, "column": 14}, "end": {"line": 199, "column": 15}}, "loc": {"start": {"line": 199, "column": 22}, "end": {"line": 199, "column": 44}}, "line": 199}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 200, "column": 11}, "end": {"line": 200, "column": 12}}, "loc": {"start": {"line": 200, "column": 19}, "end": {"line": 200, "column": 44}}, "line": 200}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 215, "column": 20}, "end": {"line": 215, "column": 21}}, "loc": {"start": {"line": 215, "column": 45}, "end": {"line": 237, "column": 1}}, "line": 215}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 247, "column": 23}, "end": {"line": 247, "column": 24}}, "loc": {"start": {"line": 247, "column": 63}, "end": {"line": 260, "column": 1}}, "line": 247}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 269, "column": 30}, "end": {"line": 269, "column": 31}}, "loc": {"start": {"line": 269, "column": 57}, "end": {"line": 288, "column": 1}}, "line": 269}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 270, "column": 35}, "end": {"line": 270, "column": 36}}, "loc": {"start": {"line": 270, "column": 42}, "end": {"line": 270, "column": 49}}, "line": 270}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 278, "column": 18}, "end": {"line": 278, "column": 19}}, "loc": {"start": {"line": 278, "column": 32}, "end": {"line": 281, "column": 5}}, "line": 278}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 298, "column": 28}, "end": {"line": 298, "column": 29}}, "loc": {"start": {"line": 298, "column": 68}, "end": {"line": 338, "column": 1}}, "line": 298}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 309, "column": 35}, "end": {"line": 309, "column": 36}}, "loc": {"start": {"line": 309, "column": 49}, "end": {"line": 312, "column": 5}}, "line": 309}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 321, "column": 35}, "end": {"line": 321, "column": 36}}, "loc": {"start": {"line": 321, "column": 49}, "end": {"line": 324, "column": 5}}, "line": 321}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 348, "column": 29}, "end": {"line": 348, "column": 30}}, "loc": {"start": {"line": 348, "column": 69}, "end": {"line": 379, "column": 1}}, "line": 348}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 388, "column": 32}, "end": {"line": 388, "column": 33}}, "loc": {"start": {"line": 388, "column": 70}, "end": {"line": 398, "column": 1}}, "line": 388}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 407, "column": 28}, "end": {"line": 407, "column": 29}}, "loc": {"start": {"line": 407, "column": 66}, "end": {"line": 417, "column": 1}}, "line": 407}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 427, "column": 30}, "end": {"line": 427, "column": 31}}, "loc": {"start": {"line": 427, "column": 83}, "end": {"line": 437, "column": 1}}, "line": 427}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 447, "column": 28}, "end": {"line": 447, "column": 29}}, "loc": {"start": {"line": 447, "column": 79}, "end": {"line": 457, "column": 1}}, "line": 447}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 466, "column": 38}, "end": {"line": 466, "column": 39}}, "loc": {"start": {"line": 466, "column": 78}, "end": {"line": 476, "column": 1}}, "line": 466}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 486, "column": 30}, "end": {"line": 486, "column": 31}}, "loc": {"start": {"line": 486, "column": 70}, "end": {"line": 504, "column": 1}}, "line": 486}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 510, "column": 31}, "end": {"line": 510, "column": 32}}, "loc": {"start": {"line": 510, "column": 37}, "end": {"line": 518, "column": 1}}, "line": 510}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 511, "column": 9}, "end": {"line": 511, "column": 10}}, "loc": {"start": {"line": 511, "column": 34}, "end": {"line": 517, "column": 3}}, "line": 511}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 525, "column": 28}, "end": {"line": 525, "column": 29}}, "loc": {"start": {"line": 525, "column": 39}, "end": {"line": 544, "column": 1}}, "line": 525}}, "branchMap": {"0": {"loc": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "type": "if", "locations": [{"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, {"start": {}, "end": {}}], "line": 37}, "1": {"loc": {"start": {"line": 55, "column": 23}, "end": {"line": 55, "column": 44}}, "type": "default-arg", "locations": [{"start": {"line": 55, "column": 30}, "end": {"line": 55, "column": 44}}], "line": 55}, "2": {"loc": {"start": {"line": 55, "column": 46}, "end": {"line": 55, "column": 58}}, "type": "default-arg", "locations": [{"start": {"line": 55, "column": 55}, "end": {"line": 55, "column": 58}}], "line": 55}, "3": {"loc": {"start": {"line": 55, "column": 60}, "end": {"line": 55, "column": 72}}, "type": "default-arg", "locations": [{"start": {"line": 55, "column": 70}, "end": {"line": 55, "column": 72}}], "line": 55}, "4": {"loc": {"start": {"line": 55, "column": 74}, "end": {"line": 55, "column": 94}}, "type": "default-arg", "locations": [{"start": {"line": 55, "column": 90}, "end": {"line": 55, "column": 94}}], "line": 55}, "5": {"loc": {"start": {"line": 74, "column": 30}, "end": {"line": 74, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 74, "column": 37}, "end": {"line": 74, "column": 53}}], "line": 74}, "6": {"loc": {"start": {"line": 74, "column": 55}, "end": {"line": 74, "column": 67}}, "type": "default-arg", "locations": [{"start": {"line": 74, "column": 64}, "end": {"line": 74, "column": 67}}], "line": 74}, "7": {"loc": {"start": {"line": 74, "column": 69}, "end": {"line": 74, "column": 81}}, "type": "default-arg", "locations": [{"start": {"line": 74, "column": 79}, "end": {"line": 74, "column": 81}}], "line": 74}, "8": {"loc": {"start": {"line": 85, "column": 28}, "end": {"line": 85, "column": 36}}, "type": "default-arg", "locations": [{"start": {"line": 85, "column": 34}, "end": {"line": 85, "column": 36}}], "line": 85}, "9": {"loc": {"start": {"line": 85, "column": 38}, "end": {"line": 85, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 85, "column": 48}, "end": {"line": 85, "column": 50}}], "line": 85}, "10": {"loc": {"start": {"line": 90, "column": 20}, "end": {"line": 90, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 20}, "end": {"line": 90, "column": 30}}, {"start": {"line": 90, "column": 34}, "end": {"line": 90, "column": 43}}], "line": 90}, "11": {"loc": {"start": {"line": 91, "column": 34}, "end": {"line": 91, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 34}, "end": {"line": 91, "column": 62}}, {"start": {"line": 91, "column": 66}, "end": {"line": 91, "column": 67}}], "line": 91}, "12": {"loc": {"start": {"line": 94, "column": 20}, "end": {"line": 94, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 94, "column": 20}, "end": {"line": 94, "column": 30}}, {"start": {"line": 94, "column": 34}, "end": {"line": 94, "column": 49}}], "line": 94}, "13": {"loc": {"start": {"line": 95, "column": 34}, "end": {"line": 95, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 34}, "end": {"line": 95, "column": 62}}, {"start": {"line": 95, "column": 66}, "end": {"line": 95, "column": 67}}], "line": 95}, "14": {"loc": {"start": {"line": 98, "column": 15}, "end": {"line": 98, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 15}, "end": {"line": 98, "column": 30}}, {"start": {"line": 98, "column": 34}, "end": {"line": 98, "column": 46}}, {"start": {"line": 98, "column": 50}, "end": {"line": 98, "column": 64}}], "line": 98}, "15": {"loc": {"start": {"line": 99, "column": 29}, "end": {"line": 99, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 29}, "end": {"line": 99, "column": 52}}, {"start": {"line": 99, "column": 56}, "end": {"line": 99, "column": 57}}], "line": 99}, "16": {"loc": {"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 22}}, {"start": {"line": 109, "column": 26}, "end": {"line": 109, "column": 40}}, {"start": {"line": 109, "column": 44}, "end": {"line": 109, "column": 53}}], "line": 109}, "17": {"loc": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 14}}, {"start": {"line": 110, "column": 18}, "end": {"line": 110, "column": 28}}, {"start": {"line": 110, "column": 32}, "end": {"line": 110, "column": 41}}], "line": 110}, "18": {"loc": {"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 28}}, {"start": {"line": 111, "column": 32}, "end": {"line": 111, "column": 46}}, {"start": {"line": 111, "column": 50}, "end": {"line": 111, "column": 61}}], "line": 111}, "19": {"loc": {"start": {"line": 113, "column": 13}, "end": {"line": 113, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 113, "column": 13}, "end": {"line": 113, "column": 26}}, {"start": {"line": 113, "column": 30}, "end": {"line": 113, "column": 32}}], "line": 113}, "20": {"loc": {"start": {"line": 120, "column": 2}, "end": {"line": 122, "column": 3}}, "type": "if", "locations": [{"start": {"line": 120, "column": 2}, "end": {"line": 122, "column": 3}}, {"start": {}, "end": {}}], "line": 120}, "21": {"loc": {"start": {"line": 131, "column": 4}, "end": {"line": 134, "column": 5}}, "type": "if", "locations": [{"start": {"line": 131, "column": 4}, "end": {"line": 134, "column": 5}}, {"start": {}, "end": {}}], "line": 131}, "22": {"loc": {"start": {"line": 178, "column": 4}, "end": {"line": 180, "column": 5}}, "type": "if", "locations": [{"start": {"line": 178, "column": 4}, "end": {"line": 180, "column": 5}}, {"start": {}, "end": {}}], "line": 178}, "23": {"loc": {"start": {"line": 215, "column": 28}, "end": {"line": 215, "column": 40}}, "type": "default-arg", "locations": [{"start": {"line": 215, "column": 38}, "end": {"line": 215, "column": 40}}], "line": 215}, "24": {"loc": {"start": {"line": 216, "column": 23}, "end": {"line": 216, "column": 53}}, "type": "cond-expr", "locations": [{"start": {"line": 216, "column": 33}, "end": {"line": 216, "column": 48}}, {"start": {"line": 216, "column": 51}, "end": {"line": 216, "column": 53}}], "line": 216}, "25": {"loc": {"start": {"line": 221, "column": 2}, "end": {"line": 223, "column": 3}}, "type": "if", "locations": [{"start": {"line": 221, "column": 2}, "end": {"line": 223, "column": 3}}, {"start": {}, "end": {}}], "line": 221}, "26": {"loc": {"start": {"line": 226, "column": 2}, "end": {"line": 228, "column": 3}}, "type": "if", "locations": [{"start": {"line": 226, "column": 2}, "end": {"line": 228, "column": 3}}, {"start": {}, "end": {}}], "line": 226}, "27": {"loc": {"start": {"line": 233, "column": 10}, "end": {"line": 233, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 233, "column": 10}, "end": {"line": 233, "column": 20}}, {"start": {"line": 233, "column": 24}, "end": {"line": 233, "column": 40}}], "line": 233}, "28": {"loc": {"start": {"line": 234, "column": 12}, "end": {"line": 234, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 234, "column": 12}, "end": {"line": 234, "column": 24}}, {"start": {"line": 234, "column": 28}, "end": {"line": 234, "column": 31}}], "line": 234}, "29": {"loc": {"start": {"line": 235, "column": 13}, "end": {"line": 235, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 235, "column": 13}, "end": {"line": 235, "column": 26}}, {"start": {"line": 235, "column": 30}, "end": {"line": 235, "column": 32}}], "line": 235}, "30": {"loc": {"start": {"line": 247, "column": 36}, "end": {"line": 247, "column": 44}}, "type": "default-arg", "locations": [{"start": {"line": 247, "column": 42}, "end": {"line": 247, "column": 44}}], "line": 247}, "31": {"loc": {"start": {"line": 247, "column": 46}, "end": {"line": 247, "column": 58}}, "type": "default-arg", "locations": [{"start": {"line": 247, "column": 56}, "end": {"line": 247, "column": 58}}], "line": 247}, "32": {"loc": {"start": {"line": 269, "column": 44}, "end": {"line": 269, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 269, "column": 50}, "end": {"line": 269, "column": 52}}], "line": 269}, "33": {"loc": {"start": {"line": 298, "column": 41}, "end": {"line": 298, "column": 49}}, "type": "default-arg", "locations": [{"start": {"line": 298, "column": 47}, "end": {"line": 298, "column": 49}}], "line": 298}, "34": {"loc": {"start": {"line": 298, "column": 51}, "end": {"line": 298, "column": 63}}, "type": "default-arg", "locations": [{"start": {"line": 298, "column": 61}, "end": {"line": 298, "column": 63}}], "line": 298}, "35": {"loc": {"start": {"line": 305, "column": 2}, "end": {"line": 325, "column": 3}}, "type": "if", "locations": [{"start": {"line": 305, "column": 2}, "end": {"line": 325, "column": 3}}, {"start": {"line": 313, "column": 9}, "end": {"line": 325, "column": 3}}], "line": 305}, "36": {"loc": {"start": {"line": 309, "column": 14}, "end": {"line": 312, "column": 16}}, "type": "binary-expr", "locations": [{"start": {"line": 309, "column": 14}, "end": {"line": 312, "column": 10}}, {"start": {"line": 312, "column": 14}, "end": {"line": 312, "column": 16}}], "line": 309}, "37": {"loc": {"start": {"line": 313, "column": 9}, "end": {"line": 325, "column": 3}}, "type": "if", "locations": [{"start": {"line": 313, "column": 9}, "end": {"line": 325, "column": 3}}, {"start": {"line": 317, "column": 9}, "end": {"line": 325, "column": 3}}], "line": 313}, "38": {"loc": {"start": {"line": 317, "column": 9}, "end": {"line": 325, "column": 3}}, "type": "if", "locations": [{"start": {"line": 317, "column": 9}, "end": {"line": 325, "column": 3}}, {"start": {}, "end": {}}], "line": 317}, "39": {"loc": {"start": {"line": 321, "column": 14}, "end": {"line": 324, "column": 16}}, "type": "binary-expr", "locations": [{"start": {"line": 321, "column": 14}, "end": {"line": 324, "column": 10}}, {"start": {"line": 324, "column": 14}, "end": {"line": 324, "column": 16}}], "line": 321}, "40": {"loc": {"start": {"line": 331, "column": 40}, "end": {"line": 331, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 331, "column": 40}, "end": {"line": 331, "column": 47}}, {"start": {"line": 331, "column": 51}, "end": {"line": 331, "column": 61}}], "line": 331}, "41": {"loc": {"start": {"line": 334, "column": 19}, "end": {"line": 334, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 334, "column": 19}, "end": {"line": 334, "column": 26}}, {"start": {"line": 334, "column": 30}, "end": {"line": 334, "column": 40}}], "line": 334}, "42": {"loc": {"start": {"line": 348, "column": 42}, "end": {"line": 348, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 348, "column": 48}, "end": {"line": 348, "column": 50}}], "line": 348}, "43": {"loc": {"start": {"line": 348, "column": 52}, "end": {"line": 348, "column": 64}}, "type": "default-arg", "locations": [{"start": {"line": 348, "column": 62}, "end": {"line": 348, "column": 64}}], "line": 348}, "44": {"loc": {"start": {"line": 352, "column": 16}, "end": {"line": 352, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 352, "column": 16}, "end": {"line": 352, "column": 29}}, {"start": {"line": 352, "column": 33}, "end": {"line": 352, "column": 35}}], "line": 352}, "45": {"loc": {"start": {"line": 355, "column": 2}, "end": {"line": 364, "column": 3}}, "type": "if", "locations": [{"start": {"line": 355, "column": 2}, "end": {"line": 364, "column": 3}}, {"start": {"line": 358, "column": 9}, "end": {"line": 364, "column": 3}}], "line": 355}, "46": {"loc": {"start": {"line": 358, "column": 9}, "end": {"line": 364, "column": 3}}, "type": "if", "locations": [{"start": {"line": 358, "column": 9}, "end": {"line": 364, "column": 3}}, {"start": {"line": 361, "column": 9}, "end": {"line": 364, "column": 3}}], "line": 358}, "47": {"loc": {"start": {"line": 361, "column": 9}, "end": {"line": 364, "column": 3}}, "type": "if", "locations": [{"start": {"line": 361, "column": 9}, "end": {"line": 364, "column": 3}}, {"start": {}, "end": {}}], "line": 361}, "48": {"loc": {"start": {"line": 370, "column": 40}, "end": {"line": 370, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 370, "column": 40}, "end": {"line": 370, "column": 47}}, {"start": {"line": 370, "column": 51}, "end": {"line": 370, "column": 63}}], "line": 370}, "49": {"loc": {"start": {"line": 373, "column": 19}, "end": {"line": 373, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 373, "column": 19}, "end": {"line": 373, "column": 26}}, {"start": {"line": 373, "column": 30}, "end": {"line": 373, "column": 42}}], "line": 373}, "50": {"loc": {"start": {"line": 374, "column": 2}, "end": {"line": 376, "column": 3}}, "type": "if", "locations": [{"start": {"line": 374, "column": 2}, "end": {"line": 376, "column": 3}}, {"start": {}, "end": {}}], "line": 374}, "51": {"loc": {"start": {"line": 388, "column": 38}, "end": {"line": 388, "column": 46}}, "type": "default-arg", "locations": [{"start": {"line": 388, "column": 44}, "end": {"line": 388, "column": 46}}], "line": 388}, "52": {"loc": {"start": {"line": 388, "column": 48}, "end": {"line": 388, "column": 65}}, "type": "default-arg", "locations": [{"start": {"line": 388, "column": 58}, "end": {"line": 388, "column": 65}}], "line": 388}, "53": {"loc": {"start": {"line": 407, "column": 34}, "end": {"line": 407, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 407, "column": 40}, "end": {"line": 407, "column": 42}}], "line": 407}, "54": {"loc": {"start": {"line": 407, "column": 44}, "end": {"line": 407, "column": 61}}, "type": "default-arg", "locations": [{"start": {"line": 407, "column": 54}, "end": {"line": 407, "column": 61}}], "line": 407}, "55": {"loc": {"start": {"line": 427, "column": 36}, "end": {"line": 427, "column": 44}}, "type": "default-arg", "locations": [{"start": {"line": 427, "column": 42}, "end": {"line": 427, "column": 44}}], "line": 427}, "56": {"loc": {"start": {"line": 427, "column": 46}, "end": {"line": 427, "column": 64}}, "type": "default-arg", "locations": [{"start": {"line": 427, "column": 56}, "end": {"line": 427, "column": 64}}], "line": 427}, "57": {"loc": {"start": {"line": 427, "column": 66}, "end": {"line": 427, "column": 78}}, "type": "default-arg", "locations": [{"start": {"line": 427, "column": 76}, "end": {"line": 427, "column": 78}}], "line": 427}, "58": {"loc": {"start": {"line": 447, "column": 34}, "end": {"line": 447, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 447, "column": 40}, "end": {"line": 447, "column": 42}}], "line": 447}, "59": {"loc": {"start": {"line": 447, "column": 44}, "end": {"line": 447, "column": 60}}, "type": "default-arg", "locations": [{"start": {"line": 447, "column": 54}, "end": {"line": 447, "column": 60}}], "line": 447}, "60": {"loc": {"start": {"line": 447, "column": 62}, "end": {"line": 447, "column": 74}}, "type": "default-arg", "locations": [{"start": {"line": 447, "column": 72}, "end": {"line": 447, "column": 74}}], "line": 447}, "61": {"loc": {"start": {"line": 466, "column": 44}, "end": {"line": 466, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 466, "column": 50}, "end": {"line": 466, "column": 52}}], "line": 466}, "62": {"loc": {"start": {"line": 466, "column": 54}, "end": {"line": 466, "column": 73}}, "type": "default-arg", "locations": [{"start": {"line": 466, "column": 64}, "end": {"line": 466, "column": 73}}], "line": 466}, "63": {"loc": {"start": {"line": 486, "column": 43}, "end": {"line": 486, "column": 51}}, "type": "default-arg", "locations": [{"start": {"line": 486, "column": 49}, "end": {"line": 486, "column": 51}}], "line": 486}, "64": {"loc": {"start": {"line": 486, "column": 53}, "end": {"line": 486, "column": 65}}, "type": "default-arg", "locations": [{"start": {"line": 486, "column": 63}, "end": {"line": 486, "column": 65}}], "line": 486}, "65": {"loc": {"start": {"line": 487, "column": 19}, "end": {"line": 487, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 487, "column": 19}, "end": {"line": 487, "column": 26}}, {"start": {"line": 487, "column": 30}, "end": {"line": 487, "column": 43}}], "line": 487}, "66": {"loc": {"start": {"line": 488, "column": 2}, "end": {"line": 490, "column": 3}}, "type": "if", "locations": [{"start": {"line": 488, "column": 2}, "end": {"line": 490, "column": 3}}, {"start": {}, "end": {}}], "line": 488}, "67": {"loc": {"start": {"line": 497, "column": 4}, "end": {"line": 497, "column": 23}}, "type": "binary-expr", "locations": [{"start": {"line": 497, "column": 4}, "end": {"line": 497, "column": 17}}, {"start": {"line": 497, "column": 21}, "end": {"line": 497, "column": 23}}], "line": 497}, "68": {"loc": {"start": {"line": 501, "column": 40}, "end": {"line": 501, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 501, "column": 40}, "end": {"line": 501, "column": 47}}, {"start": {"line": 501, "column": 51}, "end": {"line": 501, "column": 64}}], "line": 501}, "69": {"loc": {"start": {"line": 526, "column": 2}, "end": {"line": 526, "column": 45}}, "type": "if", "locations": [{"start": {"line": 526, "column": 2}, "end": {"line": 526, "column": 45}}, {"start": {}, "end": {}}], "line": 526}, "70": {"loc": {"start": {"line": 529, "column": 2}, "end": {"line": 541, "column": 3}}, "type": "if", "locations": [{"start": {"line": 529, "column": 2}, "end": {"line": 541, "column": 3}}, {"start": {}, "end": {}}], "line": 529}, "71": {"loc": {"start": {"line": 529, "column": 6}, "end": {"line": 530, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 529, "column": 6}, "end": {"line": 529, "column": 38}}, {"start": {"line": 529, "column": 42}, "end": {"line": 529, "column": 74}}, {"start": {"line": 530, "column": 6}, "end": {"line": 530, "column": 41}}, {"start": {"line": 530, "column": 45}, "end": {"line": 530, "column": 78}}], "line": 529}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 20, "9": 0, "10": 23, "11": 23, "12": 23, "13": 23, "14": 23, "15": 23, "16": 23, "17": 1, "18": 2, "19": 1, "20": 20, "21": 20, "22": 20, "23": 20, "24": 20, "25": 20, "26": 20, "27": 20, "28": 20, "29": 20, "30": 20, "31": 0, "32": 20, "33": 20, "34": 20, "35": 20, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 20, "43": 20, "44": 1, "45": 0, "46": 1, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 1, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 1, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 1, "73": 7, "74": 7, "75": 7, "76": 4, "77": 7, "78": 1, "79": 7, "80": 1, "81": 2, "82": 2, "83": 2, "84": 1, "85": 1, "86": 2, "87": 1, "88": 1, "89": 2, "90": 2, "91": 1, "92": 1, "93": 1, "94": 4, "95": 4, "96": 4, "97": 4, "98": 4, "99": 1, "100": 1, "101": 1, "102": 1, "103": 0, "104": 0, "105": 3, "106": 1, "107": 1, "108": 1, "109": 2, "110": 1, "111": 1, "112": 1, "113": 1, "114": 0, "115": 0, "116": 4, "117": 4, "118": 4, "119": 4, "120": 4, "121": 1, "122": 1, "123": 1, "124": 1, "125": 1, "126": 1, "127": 0, "128": 0, "129": 1, "130": 0, "131": 0, "132": 1, "133": 0, "134": 0, "135": 1, "136": 1, "137": 1, "138": 1, "139": 1, "140": 1, "141": 1, "142": 2, "143": 2, "144": 2, "145": 2, "146": 1, "147": 2, "148": 2, "149": 2, "150": 2, "151": 1, "152": 2, "153": 2, "154": 2, "155": 2, "156": 1, "157": 2, "158": 2, "159": 2, "160": 2, "161": 1, "162": 2, "163": 2, "164": 2, "165": 2, "166": 1, "167": 1, "168": 1, "169": 1, "170": 1, "171": 1, "172": 1, "173": 1, "174": 0, "175": 0, "176": 0, "177": 1, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 1}, "f": {"0": 20, "1": 23, "2": 2, "3": 20, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 7, "11": 2, "12": 1, "13": 2, "14": 2, "15": 4, "16": 0, "17": 0, "18": 1, "19": 2, "20": 2, "21": 2, "22": 2, "23": 2, "24": 1, "25": 0, "26": 0, "27": 0}, "b": {"0": [0, 20], "1": [1], "2": [1], "3": [9], "4": [23], "5": [1], "6": [1], "7": [1], "8": [0], "9": [0], "10": [20, 0], "11": [20, 2], "12": [20, 2], "13": [20, 13], "14": [20, 20, 19], "15": [20, 2], "16": [20, 20, 20], "17": [20, 20, 20], "18": [20, 20, 20], "19": [20, 2], "20": [0, 20], "21": [0, 0], "22": [0, 0], "23": [4], "24": [1, 6], "25": [4, 3], "26": [1, 6], "27": [7, 5], "28": [7, 5], "29": [7, 5], "30": [1], "31": [2], "32": [1], "33": [3], "34": [4], "35": [1, 3], "36": [1, 1], "37": [1, 2], "38": [1, 1], "39": [1, 1], "40": [4, 4], "41": [4, 4], "42": [0], "43": [1], "44": [1, 1], "45": [0, 1], "46": [0, 1], "47": [0, 1], "48": [1, 1], "49": [1, 1], "50": [1, 0], "51": [1], "52": [2], "53": [1], "54": [2], "55": [1], "56": [1], "57": [2], "58": [1], "59": [2], "60": [2], "61": [1], "62": [2], "63": [0], "64": [1], "65": [1, 1], "66": [1, 0], "67": [1, 1], "68": [1, 1], "69": [0, 0], "70": [0, 0], "71": [0, 0, 0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "7d727b9141d375898ba7e532b92631a8c4c4afa3"}, "/Users/<USER>/xiangmu/AIBUBB/backend/utils/wechat.js": {"path": "/Users/<USER>/xiangmu/AIBUBB/backend/utils/wechat.js", "statementMap": {"0": {"start": {"line": 1, "column": 14}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 42}}, "3": {"start": {"line": 6, "column": 17}, "end": {"line": 6, "column": 52}}, "4": {"start": {"line": 13, "column": 25}, "end": {"line": 65, "column": 1}}, "5": {"start": {"line": 15, "column": 2}, "end": {"line": 33, "column": 3}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 50}}, "7": {"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": 39}}, "8": {"start": {"line": 22, "column": 4}, "end": {"line": 27, "column": 5}}, "9": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 96}}, "10": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 44}}, "11": {"start": {"line": 29, "column": 4}, "end": {"line": 32, "column": 6}}, "12": {"start": {"line": 35, "column": 2}, "end": {"line": 64, "column": 3}}, "13": {"start": {"line": 36, "column": 16}, "end": {"line": 36, "column": 171}}, "14": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 36}}, "15": {"start": {"line": 39, "column": 21}, "end": {"line": 39, "column": 41}}, "16": {"start": {"line": 40, "column": 17}, "end": {"line": 40, "column": 30}}, "17": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "18": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 64}}, "19": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 49}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 50, "column": 6}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 47}}, "22": {"start": {"line": 55, "column": 4}, "end": {"line": 61, "column": 5}}, "23": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 41}}, "24": {"start": {"line": 57, "column": 6}, "end": {"line": 60, "column": 8}}, "25": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 16}}, "26": {"start": {"line": 67, "column": 0}, "end": {"line": 69, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 26}}, "loc": {"start": {"line": 13, "column": 41}, "end": {"line": 65, "column": 1}}, "line": 13}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 2}, "end": {"line": 33, "column": 3}}, "type": "if", "locations": [{"start": {"line": 15, "column": 2}, "end": {"line": 33, "column": 3}}, {"start": {}, "end": {}}], "line": 15}, "1": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 27, "column": 5}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 27, "column": 5}}, {"start": {"line": 24, "column": 11}, "end": {"line": 27, "column": 5}}], "line": 22}, "2": {"loc": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 95}}, "type": "cond-expr", "locations": [{"start": {"line": 23, "column": 44}, "end": {"line": 23, "column": 78}}, {"start": {"line": 23, "column": 81}, "end": {"line": 23, "column": 95}}], "line": 23}, "3": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, {"start": {}, "end": {}}], "line": 42}, "4": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 61, "column": 5}}, "type": "if", "locations": [{"start": {"line": 55, "column": 4}, "end": {"line": 61, "column": 5}}, {"start": {}, "end": {}}], "line": 55}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}}