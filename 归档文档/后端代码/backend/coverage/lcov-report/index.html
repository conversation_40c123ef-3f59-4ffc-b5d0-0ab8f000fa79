
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.57% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>294/2540</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.07% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>163/902</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.02% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>51/268</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.52% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>288/2498</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="controllers"><a href="controllers/index.html">controllers</a></td>
	<td data-value="3.54" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.54" class="pct low">3.54%</td>
	<td data-value="988" class="abs low">35/988</td>
	<td data-value="2.15" class="pct low">2.15%</td>
	<td data-value="325" class="abs low">7/325</td>
	<td data-value="3.77" class="pct low">3.77%</td>
	<td data-value="106" class="abs low">4/106</td>
	<td data-value="3.49" class="pct low">3.49%</td>
	<td data-value="974" class="abs low">34/974</td>
	</tr>

<tr>
	<td class="file low" data-value="middlewares"><a href="middlewares/index.html">middlewares</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="43" class="abs low">0/43</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="43" class="abs low">0/43</td>
	</tr>

<tr>
	<td class="file low" data-value="models"><a href="models/index.html">models</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="240" class="abs low">0/240</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="240" class="abs low">0/240</td>
	</tr>

<tr>
	<td class="file low" data-value="routes"><a href="routes/index.html">routes</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="326" class="abs low">0/326</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="46" class="abs low">0/46</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="325" class="abs low">0/325</td>
	</tr>

<tr>
	<td class="file low" data-value="services"><a href="services/index.html">services</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="307" class="abs low">0/307</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="151" class="abs low">0/151</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="296" class="abs low">0/296</td>
	</tr>

<tr>
	<td class="file low" data-value="utils"><a href="utils/index.html">utils</a></td>
	<td data-value="40.72" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 40%"></div><div class="cover-empty" style="width: 60%"></div></div>
	</td>
	<td data-value="40.72" class="pct low">40.72%</td>
	<td data-value="636" class="abs low">259/636</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="364" class="abs low">156/364</td>
	<td data-value="47" class="pct low">47%</td>
	<td data-value="100" class="abs low">47/100</td>
	<td data-value="40.96" class="pct low">40.96%</td>
	<td data-value="620" class="abs low">254/620</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-27T11:57:45.594Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    