# AIBUBB领域驱动设计 - 领域模型设计

## 一、领域模型概述

基于前期的领域知识梳理，本文档进一步细化AIBUBB系统的领域模型，明确实体、值对象、领域服务和聚合根之间的关系，为后续的代码实现提供指导。

## 二、核心领域模型

### 1. 学习内容领域模型

#### 1.1 实体

- **Theme（主题）**
  - 属性：id, name, description, icon, status, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore
  - 关系：一个主题可以包含多个学习模板

- **LearningTemplate（学习模板）**
  - 属性：id, themeId, title, description, coverImage, difficulty, estimatedDays, status, creatorId, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore, publish, unpublish, createPlan
  - 关系：属于一个主题，包含多个标签，可以创建多个学习计划

- **LearningPlan（学习计划）**
  - 属性：id, userId, templateId, title, description, startDate, endDate, status, progress, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore, start, complete, addTag, removeTag, createDailyContent
  - 关系：属于一个用户，基于一个模板（可选），包含多个每日内容，关联多个标签

- **DailyContent（每日内容）**
  - 属性：id, planId, dayNumber, title, isCompleted, completionDate, createdAt, updatedAt, deletedAt
  - 行为：create, update, complete, addContent, removeContent
  - 关系：属于一个学习计划，关联多个内容项（练习、观点、笔记）

#### 1.2 值对象

- **Difficulty（难度）**
  - 属性：level, description
  - 不可变，表示学习模板的难度级别

- **Progress（进度）**
  - 属性：percentage, completedDays, totalDays
  - 不可变，表示学习计划的完成进度

#### 1.3 领域服务

- **LearningPathService（学习路径服务）**
  - 功能：基于用户历史和偏好，生成个性化学习路径
  - 方法：generatePath, recommendNextStep, evaluateProgress

- **ContentRecommendationService（内容推荐服务）**
  - 功能：为用户推荐合适的内容
  - 方法：recommendDaily, recommendByTag, recommendByUserHistory

### 2. 标签领域模型

#### 2.1 实体

- **Tag（标签）**
  - 属性：id, name, categoryId, description, creatorId, popularity, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore, increasePopularity
  - 关系：可以属于一个分类，可以有多个同义词，可以关联到多个内容项
  - 约束：name长度为2-4个字（以两个字为最佳）

- **TagCategory（标签分类）**
  - 属性：id, name, description, parentId, level, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore, addTag, removeTag
  - 关系：可以有父分类，可以有多个子分类，包含多个标签

- **TagSynonym（标签同义词）**
  - 属性：id, tagId, name, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore
  - 关系：属于一个标签

- **TagFeedback（标签反馈）**
  - 属性：id, tagId, userId, feedbackType, content, status, createdAt, updatedAt, deletedAt
  - 行为：create, update, process, resolve
  - 关系：关联一个标签和一个用户

#### 2.2 值对象

- **FeedbackType（反馈类型）**
  - 属性：type, description
  - 不可变，表示标签反馈的类型（如错误报告、改进建议等）

#### 2.3 领域服务

- **TagManagementService（标签管理服务）**
  - 功能：管理标签的创建、更新、合并和拆分
  - 方法：createTag, updateTag, mergeTag, splitTag, suggestRelatedTags

- **TagAnalysisService（标签分析服务）**
  - 功能：分析标签使用情况和关联关系
  - 方法：analyzePopularity, findRelatedTags, generateTagCloud

### 3. 内容形式领域模型

#### 3.1 实体

- **Exercise（练习）**
  - 属性：id, tagId, title, content, difficulty, timeEstimateMinutes, status, creatorId, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore, publish, unpublish
  - 关系：关联一个标签，由一个用户创建

- **Insight（观点）**
  - 属性：id, tagId, content, source, status, creatorId, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore, publish, unpublish
  - 关系：关联一个标签，由一个用户创建

- **Note（笔记）**
  - 属性：id, tagId, userId, title, content, isPublic, likeCount, commentCount, viewCount, createdAt, updatedAt, deletedAt
  - 行为：create, update, softDelete, restore, publish, unpublish, like, comment
  - 关系：关联一个标签，属于一个用户，可以有多个点赞和评论

#### 3.2 值对象

- **ContentStatus（内容状态）**
  - 属性：status, description
  - 不可变，表示内容的状态（如草稿、已发布、已归档等）

#### 3.3 领域服务

- **ContentQualityService（内容质量服务）**
  - 功能：评估和提升内容质量
  - 方法：evaluateQuality, suggestImprovements, detectDuplicates

- **ContentSearchService（内容搜索服务）**
  - 功能：提供内容搜索功能
  - 方法：searchByKeyword, searchByTag, searchByUser, advancedSearch

## 三、聚合设计

### 1. 学习内容聚合

**聚合根**：LearningPlan
**包含实体**：DailyContent, DailyContentRelation
**值对象**：Progress, Difficulty
**不变性规则**：
- 学习计划必须有标题和描述
- 每日内容必须关联到学习计划
- 学习计划的进度必须基于实际完成情况计算

### 2. 标签聚合

**聚合根**：Tag
**包含实体**：TagSynonym, TagFeedback
**值对象**：FeedbackType
**不变性规则**：
- 标签名称长度为2-4个字（以两个字为最佳）
- 标签可以归属于一个分类
- 标签可以有多个同义词，但同义词不能与其他标签名称重复

### 3. 内容聚合

**聚合根**：Exercise/Insight/Note（根据具体类型）
**包含实体**：根据内容类型不同而不同
**值对象**：ContentStatus
**不变性规则**：
- 内容必须关联到至少一个标签
- 内容必须有创建者
- 内容状态变更需遵循特定规则（如草稿→已发布→已归档）

## 四、领域事件

### 1. 学习内容领域事件

- **LearningPlanCreated**：学习计划创建事件
- **LearningPlanCompleted**：学习计划完成事件
- **DailyContentCompleted**：每日内容完成事件
- **TemplatePublished**：学习模板发布事件

### 2. 标签领域事件

- **TagCreated**：标签创建事件
- **TagPopularityChanged**：标签热度变化事件
- **TagFeedbackSubmitted**：标签反馈提交事件
- **TagCategoryUpdated**：标签分类更新事件

### 3. 内容领域事件

- **ContentPublished**：内容发布事件
- **ContentLiked**：内容被点赞事件
- **ContentCommented**：内容被评论事件
- **ContentViewed**：内容被查看事件

## 五、实现建议

### 1. 领域层实现

- 使用TypeScript定义清晰的领域模型接口和类
- 实体类应包含业务逻辑和验证规则
- 值对象应设计为不可变对象
- 领域服务应专注于跨实体的业务逻辑

### 2. 仓库接口设计

- 为每个聚合根定义仓库接口
- 仓库方法应反映领域语言和操作
- 支持事务和并发控制

### 3. 应用服务设计

- 应用服务协调领域对象完成用户用例
- 处理事务边界和安全控制
- 转换领域对象和DTO

## 六、下一步计划

1. **领域模型验证**：与领域专家一起验证领域模型的准确性和完整性
2. **用例分析**：详细分析核心用例，确保领域模型能够支持
3. **原型实现**：实现核心领域模型的原型，验证设计
4. **架构调整**：基于领域模型调整系统架构，明确各层职责
5. **代码重构计划**：制定基于DDD的代码重构计划
