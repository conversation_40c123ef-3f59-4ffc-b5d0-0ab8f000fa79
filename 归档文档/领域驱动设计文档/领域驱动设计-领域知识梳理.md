# AIBUBB领域驱动设计 - 领域知识梳理

## 一、领域术语表

### 核心领域概念

| 术语 | 中文名称 | 定义 | 所属上下文 | 关联概念 |
|------|---------|------|------------|---------|
| Theme | 主题 | 学习内容的最高层分类，定义学习的大方向 | 核心层次架构 | LearningTemplate |
| LearningTemplate | 学习模板 | 预设的学习路径框架，包含标准标签集和内容组织 | 核心层次架构 | Theme, LearningPlan, Tag |
| LearningPlan | 学习计划 | 用户个性化的学习实例，可基于模板创建或完全自定义 | 核心层次架构 | LearningTemplate, Tag, User, DailyContent |
| Tag | 标签 | 知识点单元，连接学习计划和具体内容 | 核心层次架构 | LearningPlan, Exercise, Insight, Note, TagCategory |
| TagCategory | 标签分类 | 标签的分类体系，形成层次分类 | 标签系统 | Tag |
| TagSynonym | 标签同义词 | 管理同义标签，减少概念冗余 | 标签系统 | Tag |
| Exercise | 练习 | 转化知识为行动的内容形式，强化实践能力 | 内容形式 | Tag, User |
| Insight | 观点 | 精炼的思想启发，促进认知转变的内容形式 | 内容形式 | Tag, User |
| Note | 笔记 | 系统化知识呈现，支持深度理解的内容形式 | 内容形式 | Tag, User |
| DailyContent | 每日内容 | 学习计划中每一天的安排框架 | 内容组织 | LearningPlan, Exercise, Insight, Note |
| User | 用户 | 系统的使用者，学习的主体 | 用户管理 | LearningPlan, Note, UserSetting |
| UserSetting | 用户设置 | 用户的个性化配置 | 用户管理 | User |
| Achievement | 成就 | 用户可以获得的成就，游戏化元素之一 | 游戏化系统 | User, UserAchievement |
| Level | 等级 | 用户等级系统，量化和可视化学习进步 | 游戏化系统 | User |
| Badge | 徽章 | 特定成就和里程碑的象征性奖励 | 游戏化系统 | User, UserBadge |
| LearningActivity | 学习活动 | 用户的学习行为记录 | 学习追踪 | User, Content |
| UserContentProgress | 用户内容进度 | 用户对特定内容的学习进度 | 学习追踪 | User, Content |
| BubbleInteraction | 泡泡交互 | 首页泡泡交互的记录 | 用户交互 | User, BubbleSession |

### 业务术语

| 术语 | 中文名称 | 定义 | 所属上下文 | 关联概念 |
|------|---------|------|------------|---------|
| SoftDelete | 软删除 | 通过deleted_at字段标记删除状态而非物理删除 | 数据管理 | 所有支持软删除的实体 |
| MultiLevelCache | 多级缓存 | 结合内存和Redis的缓存策略 | 性能优化 | - |
| CascadeSoftDelete | 级联软删除 | 主实体软删除时关联实体也软删除 | 数据管理 | 相关联的实体 |
| PersonalizedRecommendation | 个性化推荐 | 基于用户行为和偏好的内容推荐 | 内容分发 | User, Content |
| LearningPath | 学习路径 | 用户的学习轨迹和进程 | 学习追踪 | LearningPlan, UserContentProgress |
| ContentForm | 内容形式 | 练习、观点、笔记三种内容类型的总称 | 内容形式 | Exercise, Insight, Note |
| CommunityInteraction | 社区互动 | 用户间的互动，如点赞、评论、关注 | 社区功能 | User, Note, Comment |

## 二、领域分析

### 1. 核心子域

AIBUBB系统的核心子域是**学习内容组织与管理**，这是系统的核心竞争力所在。它包括：

1. **多层次知识组织**：通过主题→学习模板→学习计划→标签→内容的五层结构，实现知识的系统化组织。
2. **内容形式管理**：管理练习、观点、笔记三种内容形式，满足不同学习场景和认知需求。
3. **个性化学习路径**：基于用户行为和偏好，提供个性化的学习内容和路径。

### 2. 支撑子域

支撑核心业务的子域包括：

1. **用户管理**：用户注册、认证、个人信息管理等基础功能。
2. **游戏化系统**：通过成就、等级、徽章等游戏化元素，增强用户学习动力。
3. **学习追踪**：记录和分析用户学习行为，支持个性化推荐和学习优化。
4. **社区互动**：用户间的互动功能，如点赞、评论、关注等。

### 3. 通用子域

可以采用通用解决方案的子域：

1. **通知系统**：向用户推送各类通知。
2. **配置管理**：系统配置和功能开关管理。
3. **数据统计**：基础的数据统计和报表功能。

## 三、聚合与边界初步识别

### 1. 学习内容聚合

**根实体**：LearningPlan（学习计划）
**包含实体**：DailyContent（每日内容）, PlanTag（计划标签关联）
**不变性规则**：
- 学习计划必须有标题和描述
- 每日内容必须关联到学习计划
- 学习计划可以关联多个标签

### 2. 标签聚合

**根实体**：Tag（标签）
**包含实体**：TagSynonym（标签同义词）, TagFeedback（标签反馈）
**不变性规则**：
- 标签必须有名称，且名称长度为2-4个字（以两个字为最佳）
- 标签可以归属于一个分类
- 标签可以有多个同义词

### 3. 内容聚合

**根实体**：ContentForm（内容形式的抽象）具体为Exercise/Insight/Note
**包含实体**：根据内容类型不同而不同
**不变性规则**：
- 内容必须关联到至少一个标签
- 内容必须有创建者
- 内容状态变更需遵循特定规则

### 4. 用户聚合

**根实体**：User（用户）
**包含实体**：UserSetting（用户设置）, UserNotificationSetting（通知设置）
**不变性规则**：
- 用户ID必须唯一
- 用户可以有多个设置项
- 用户认证信息需要保密

### 5. 游戏化聚合

**根实体**：Achievement（成就）/ Badge（徽章）/ Level（等级）
**包含实体**：UserAchievement（用户成就）, UserBadge（用户徽章）
**不变性规则**：
- 成就/徽章获取需满足特定条件
- 等级提升需要累积足够经验值
- 用户成就状态需要实时更新

## 四、下一步计划

1. **深入领域专家访谈**：与产品经理和业务专家进行深入访谈，验证和完善领域模型。
2. **业务流程分析**：详细分析核心业务流程，如学习计划创建、内容推荐、成就获取等。
3. **现有模型评估**：评估现有代码中的模型实现与领域模型的匹配度，识别差距。
4. **领域模型细化**：基于访谈和分析结果，细化领域模型，明确实体、值对象、领域服务等。
5. **统一语言建立**：形成团队共享的领域术语表，确保沟通一致性。
