# 学习内容领域分析

## 1. 领域概述

学习内容领域是AIBUBB系统的核心领域之一，负责管理和组织学习内容，包括洞察（Insight）、练习（Exercise）、笔记（Note）等。学习内容是用户学习和成长的基础，也是系统提供价值的核心。

## 2. 核心概念

### 2.1 洞察（Insight）

洞察是系统中最基本的学习内容单元，代表一个知识点或者一个观点。

**属性**：
- ID：唯一标识
- 标题：洞察的标题
- 内容：洞察的详细内容
- 创建者：创建洞察的用户
- 标签：与洞察相关的标签
- 创建时间：洞察的创建时间
- 更新时间：洞察的最后更新时间
- 删除时间：洞察的删除时间（软删除）
- 状态：洞察的状态（草稿、已发布、已删除等）
- 可见性：洞察的可见性（公开、私有、仅好友可见等）
- 点赞数：洞察的点赞数
- 收藏数：洞察的收藏数
- 评论数：洞察的评论数
- 分享数：洞察的分享数

**业务规则**：
- 洞察必须有标题和内容
- 洞察可以被标记为多个标签
- 洞察可以被点赞、收藏、评论和分享
- 洞察可以被软删除和恢复
- 洞察的可见性决定了谁可以看到它
- 洞察的创建者可以编辑和删除它
- 管理员可以编辑和删除任何洞察

### 2.2 练习（Exercise）

练习是用户巩固知识的方式，通常与洞察相关联。

**属性**：
- ID：唯一标识
- 标题：练习的标题
- 内容：练习的详细内容
- 创建者：创建练习的用户
- 相关洞察：与练习相关的洞察
- 标签：与练习相关的标签
- 创建时间：练习的创建时间
- 更新时间：练习的最后更新时间
- 删除时间：练习的删除时间（软删除）
- 状态：练习的状态（草稿、已发布、已删除等）
- 可见性：练习的可见性（公开、私有、仅好友可见等）
- 难度：练习的难度（简单、中等、困难等）
- 完成次数：练习被完成的次数
- 平均得分：练习的平均得分

**业务规则**：
- 练习必须有标题和内容
- 练习可以关联一个或多个洞察
- 练习可以被标记为多个标签
- 练习可以被软删除和恢复
- 练习的可见性决定了谁可以看到它
- 练习的创建者可以编辑和删除它
- 管理员可以编辑和删除任何练习

### 2.3 笔记（Note）

笔记是用户记录学习心得和总结的方式。

**属性**：
- ID：唯一标识
- 标题：笔记的标题
- 内容：笔记的详细内容
- 创建者：创建笔记的用户
- 相关洞察：与笔记相关的洞察
- 相关练习：与笔记相关的练习
- 标签：与笔记相关的标签
- 创建时间：笔记的创建时间
- 更新时间：笔记的最后更新时间
- 删除时间：笔记的删除时间（软删除）
- 状态：笔记的状态（草稿、已发布、已删除等）
- 可见性：笔记的可见性（公开、私有、仅好友可见等）
- 点赞数：笔记的点赞数
- 收藏数：笔记的收藏数
- 评论数：笔记的评论数
- 分享数：笔记的分享数

**业务规则**：
- 笔记必须有标题和内容
- 笔记可以关联一个或多个洞察和练习
- 笔记可以被标记为多个标签
- 笔记可以被点赞、收藏、评论和分享
- 笔记可以被软删除和恢复
- 笔记的可见性决定了谁可以看到它
- 笔记的创建者可以编辑和删除它
- 管理员可以编辑和删除任何笔记

### 2.4 学习计划（LearningPlan）

学习计划是一系列学习内容的集合，用于指导用户的学习路径。

**属性**：
- ID：唯一标识
- 标题：学习计划的标题
- 描述：学习计划的详细描述
- 创建者：创建学习计划的用户
- 内容项：学习计划包含的内容项（洞察、练习、笔记等）
- 标签：与学习计划相关的标签
- 创建时间：学习计划的创建时间
- 更新时间：学习计划的最后更新时间
- 删除时间：学习计划的删除时间（软删除）
- 状态：学习计划的状态（草稿、已发布、已删除等）
- 可见性：学习计划的可见性（公开、私有、仅好友可见等）
- 参与人数：参与学习计划的用户数
- 完成人数：完成学习计划的用户数
- 平均完成时间：完成学习计划的平均时间

**业务规则**：
- 学习计划必须有标题和描述
- 学习计划必须包含至少一个内容项
- 学习计划可以被标记为多个标签
- 学习计划可以被软删除和恢复
- 学习计划的可见性决定了谁可以看到它
- 学习计划的创建者可以编辑和删除它
- 管理员可以编辑和删除任何学习计划

### 2.5 主题（Theme）

主题是一组相关学习内容的集合，用于组织和分类学习内容。

**属性**：
- ID：唯一标识
- 名称：主题的名称
- 描述：主题的详细描述
- 创建者：创建主题的用户
- 内容项：主题包含的内容项（洞察、练习、笔记等）
- 标签：与主题相关的标签
- 创建时间：主题的创建时间
- 更新时间：主题的最后更新时间
- 删除时间：主题的删除时间（软删除）
- 状态：主题的状态（草稿、已发布、已删除等）
- 可见性：主题的可见性（公开、私有、仅好友可见等）
- 关注人数：关注主题的用户数

**业务规则**：
- 主题必须有名称和描述
- 主题可以包含多个内容项
- 主题可以被标记为多个标签
- 主题可以被软删除和恢复
- 主题的可见性决定了谁可以看到它
- 主题的创建者可以编辑和删除它
- 管理员可以编辑和删除任何主题

## 3. 领域事件

### 3.1 洞察相关事件

- InsightCreatedEvent：洞察创建事件
- InsightUpdatedEvent：洞察更新事件
- InsightDeletedEvent：洞察删除事件
- InsightRestoredEvent：洞察恢复事件
- InsightPublishedEvent：洞察发布事件
- InsightLikedEvent：洞察被点赞事件
- InsightFavoritedEvent：洞察被收藏事件
- InsightCommentedEvent：洞察被评论事件
- InsightSharedEvent：洞察被分享事件

### 3.2 练习相关事件

- ExerciseCreatedEvent：练习创建事件
- ExerciseUpdatedEvent：练习更新事件
- ExerciseDeletedEvent：练习删除事件
- ExerciseRestoredEvent：练习恢复事件
- ExercisePublishedEvent：练习发布事件
- ExerciseCompletedEvent：练习完成事件

### 3.3 笔记相关事件

- NoteCreatedEvent：笔记创建事件
- NoteUpdatedEvent：笔记更新事件
- NoteDeletedEvent：笔记删除事件
- NoteRestoredEvent：笔记恢复事件
- NotePublishedEvent：笔记发布事件
- NoteLikedEvent：笔记被点赞事件
- NoteFavoritedEvent：笔记被收藏事件
- NoteCommentedEvent：笔记被评论事件
- NoteSharedEvent：笔记被分享事件

### 3.4 学习计划相关事件

- LearningPlanCreatedEvent：学习计划创建事件
- LearningPlanUpdatedEvent：学习计划更新事件
- LearningPlanDeletedEvent：学习计划删除事件
- LearningPlanRestoredEvent：学习计划恢复事件
- LearningPlanPublishedEvent：学习计划发布事件
- LearningPlanJoinedEvent：用户加入学习计划事件
- LearningPlanCompletedEvent：用户完成学习计划事件

### 3.5 主题相关事件

- ThemeCreatedEvent：主题创建事件
- ThemeUpdatedEvent：主题更新事件
- ThemeDeletedEvent：主题删除事件
- ThemeRestoredEvent：主题恢复事件
- ThemePublishedEvent：主题发布事件
- ThemeFollowedEvent：用户关注主题事件

## 4. 聚合根和实体

### 4.1 洞察聚合

- Insight（聚合根）：洞察实体
- InsightTag：洞察标签关联实体
- InsightLike：洞察点赞实体
- InsightFavorite：洞察收藏实体
- InsightComment：洞察评论实体
- InsightShare：洞察分享实体

### 4.2 练习聚合

- Exercise（聚合根）：练习实体
- ExerciseTag：练习标签关联实体
- ExerciseInsight：练习洞察关联实体
- ExerciseCompletion：练习完成记录实体

### 4.3 笔记聚合

- Note（聚合根）：笔记实体
- NoteTag：笔记标签关联实体
- NoteInsight：笔记洞察关联实体
- NoteExercise：笔记练习关联实体
- NoteLike：笔记点赞实体
- NoteFavorite：笔记收藏实体
- NoteComment：笔记评论实体
- NoteShare：笔记分享实体

### 4.4 学习计划聚合

- LearningPlan（聚合根）：学习计划实体
- LearningPlanItem：学习计划内容项实体
- LearningPlanTag：学习计划标签关联实体
- LearningPlanParticipant：学习计划参与者实体
- LearningPlanProgress：学习计划进度实体

### 4.5 主题聚合

- Theme（聚合根）：主题实体
- ThemeItem：主题内容项实体
- ThemeTag：主题标签关联实体
- ThemeFollower：主题关注者实体

## 5. 值对象

- ContentStatus：内容状态值对象（草稿、已发布、已删除等）
- Visibility：可见性值对象（公开、私有、仅好友可见等）
- Difficulty：难度值对象（简单、中等、困难等）
- Progress：进度值对象（未开始、进行中、已完成等）
- Rating：评分值对象（1-5星）
- Period：时间段值对象（开始时间和结束时间）

## 6. 仓库接口

- InsightRepository：洞察仓库接口
- ExerciseRepository：练习仓库接口
- NoteRepository：笔记仓库接口
- LearningPlanRepository：学习计划仓库接口
- ThemeRepository：主题仓库接口

## 7. 领域服务

- ContentRecommendationService：内容推荐服务
- ContentSearchService：内容搜索服务
- LearningPathService：学习路径服务
- ContentAnalysisService：内容分析服务
- UserProgressService：用户进度服务
