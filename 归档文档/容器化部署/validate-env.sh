#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}AIBUBB 环境变量验证工具${NC}"
echo -e "${YELLOW}此脚本将验证.env文件中的环境变量是否完整${NC}"
echo

# 检查.env文件是否存在
if [ ! -f .env ]; then
    echo -e "${RED}错误: 未找到.env文件${NC}"
    echo -e "${YELLOW}提示: 可以复制.env.example为.env，并填入实际值${NC}"
    exit 1
fi

# 定义必需的环境变量
REQUIRED_VARS=(
    "PORT"
    "NODE_ENV"
    "API_PREFIX"
    "DB_HOST"
    "DB_PORT"
    "DB_NAME"
    "DB_USER"
    "DB_PASSWORD"
    "REDIS_URL"
    "JWT_SECRET"
    "AI_PROVIDER"
)

# 定义基于AI_PROVIDER的条件必需变量
BYTEDANCE_VARS=(
    "ARK_API_KEY"
    "ARK_API_MODEL"
)

ALIYUN_VARS=(
    "DASHSCOPE_API_KEY"
    "DASHSCOPE_API_MODEL"
)

HUNYUAN_VARS=(
    "HUNYUAN_API_KEY"
    "HUNYUAN_API_MODEL"
)

# 验证必需的环境变量
echo -e "${GREEN}验证基本环境变量...${NC}"
MISSING_VARS=0

for var in "${REQUIRED_VARS[@]}"; do
    value=$(grep "^$var=" .env | cut -d '=' -f 2-)
    if [ -z "$value" ]; then
        echo -e "${RED}错误: 缺少必需的环境变量 $var${NC}"
        MISSING_VARS=$((MISSING_VARS + 1))
    else
        echo -e "${GREEN}✓ $var 已设置${NC}"
    fi
done

# 获取AI_PROVIDER的值
AI_PROVIDER=$(grep "^AI_PROVIDER=" .env | cut -d '=' -f 2-)

# 根据AI_PROVIDER验证相应的环境变量
echo -e "\n${GREEN}验证AI提供商特定的环境变量...${NC}"
echo -e "${YELLOW}检测到AI提供商: $AI_PROVIDER${NC}"

if [ "$AI_PROVIDER" = "bytedance" ]; then
    for var in "${BYTEDANCE_VARS[@]}"; do
        value=$(grep "^$var=" .env | cut -d '=' -f 2-)
        if [ -z "$value" ]; then
            echo -e "${RED}错误: 缺少字节大模型必需的环境变量 $var${NC}"
            MISSING_VARS=$((MISSING_VARS + 1))
        else
            echo -e "${GREEN}✓ $var 已设置${NC}"
        fi
    done
elif [ "$AI_PROVIDER" = "aliyun" ]; then
    for var in "${ALIYUN_VARS[@]}"; do
        value=$(grep "^$var=" .env | cut -d '=' -f 2-)
        if [ -z "$value" ]; then
            echo -e "${RED}错误: 缺少阿里云百炼必需的环境变量 $var${NC}"
            MISSING_VARS=$((MISSING_VARS + 1))
        else
            echo -e "${GREEN}✓ $var 已设置${NC}"
        fi
    done
elif [ "$AI_PROVIDER" = "hunyuan" ]; then
    for var in "${HUNYUAN_VARS[@]}"; do
        value=$(grep "^$var=" .env | cut -d '=' -f 2-)
        if [ -z "$value" ]; then
            echo -e "${RED}错误: 缺少腾讯混元必需的环境变量 $var${NC}"
            MISSING_VARS=$((MISSING_VARS + 1))
        else
            echo -e "${GREEN}✓ $var 已设置${NC}"
        fi
    done
else
    echo -e "${RED}错误: 不支持的AI提供商: $AI_PROVIDER${NC}"
    echo -e "${YELLOW}支持的值为: bytedance, aliyun, hunyuan${NC}"
    MISSING_VARS=$((MISSING_VARS + 1))
fi

# 输出验证结果
echo
if [ $MISSING_VARS -eq 0 ]; then
    echo -e "${GREEN}✓ 所有必需的环境变量都已正确设置！${NC}"
    exit 0
else
    echo -e "${RED}✗ 发现 $MISSING_VARS 个缺失或无效的环境变量${NC}"
    echo -e "${YELLOW}请修复上述问题后再继续${NC}"
    exit 1
fi
