version: "3.8"

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: aibubb-backend
    container_name: aibubb-backend
    restart: always
    ports:
      - "${PORT:-9090}:${PORT:-9090}"
    depends_on:
      - mysql
      - redis
    env_file:
      - ./.env
    environment:
      # 只保留Docker特定的变量，覆盖.env中的相应设置
      - NODE_ENV=production
      - DB_HOST=mysql
      - REDIS_URL=redis://redis:6379
      # 优化Node.js性能
      - NODE_OPTIONS=--max-old-space-size=1024
    volumes:
      - ./backend:/usr/src/app  # 挂载整个后端代码目录
      - /usr/src/app/node_modules # 排除 node_modules，使用容器内的
      - ./backend/logs:/usr/src/app/logs # 保留日志目录挂载
    networks:
      - aibubb-network
    healthcheck:
      test: ["CMD", "node", "./scripts/healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: aibubb-mysql
    restart: always
    ports:
      - "3306:3306"
    env_file:
      - ./.env
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d:ro
    networks:
      - aibubb-network
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --innodb-buffer-pool-size=256M --max-connections=1000
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u${DB_USER}", "-p${DB_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: aibubb-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - aibubb-network
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  mcp-mysql-server:
    build:
      context: ./mcp-mysql-server    # 指向上面克隆下来的目录
      dockerfile: Dockerfile         # 仓库里自带
    env_file:
      - ./.env
    depends_on:
      - mysql # 简单的 depends_on
    networks:
      - aibubb-network
    entrypoint: tail # Override entrypoint
    command: ["-f", "/dev/null"] # Arguments for tail

# 数据卷
volumes:
  mysql_data:
    name: aibubb-mysql-data
  redis-data:
    name: aibubb-redis-data

# 网络
networks:
  aibubb-network:
    name: aibubb-network
    driver: bridge
