#!/bin/bash

# AIBUBB启动脚本
# 用于在不同环境中启动应用

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 默认环境
ENV="development"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -e|--env)
      ENV="$2"
      shift 2
      ;;
    -h|--help)
      echo -e "${GREEN}AIBUBB启动脚本${NC}"
      echo "用法: ./start.sh [选项]"
      echo ""
      echo "选项:"
      echo "  -e, --env ENV    指定环境 (development, test, production)"
      echo "  -h, --help       显示帮助信息"
      exit 0
      ;;
    *)
      echo -e "${RED}错误: 未知选项 $1${NC}"
      exit 1
      ;;
  esac
done

# 验证环境
if [[ "$ENV" != "development" && "$ENV" != "test" && "$ENV" != "production" ]]; then
  echo -e "${RED}错误: 无效的环境 '$ENV'${NC}"
  echo "有效的环境: development, test, production"
  exit 1
fi

# 检查环境变量文件
ENV_FILE=".env.${ENV}"
if [[ ! -f "$ENV_FILE" ]]; then
  echo -e "${YELLOW}警告: 环境变量文件 '$ENV_FILE' 不存在${NC}"
  echo -e "将使用默认的 .env 文件（如果存在）"
fi

# 启动应用
echo -e "${GREEN}在 ${YELLOW}$ENV${GREEN} 环境中启动AIBUBB...${NC}"
NODE_ENV=$ENV node backend/server.js
