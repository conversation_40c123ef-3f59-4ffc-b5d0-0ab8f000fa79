#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}AIBUBB 环境变量迁移工具${NC}"
echo -e "${YELLOW}此脚本将帮助您将多个环境变量文件合并为一个统一的.env文件${NC}"
echo

# 检查是否已存在.env文件
if [ -f .env ]; then
    echo -e "${YELLOW}检测到已存在.env文件。${NC}"
    read -p "是否覆盖现有的.env文件？(y/n): " overwrite
    if [ "$overwrite" != "y" ]; then
        echo -e "${YELLOW}操作已取消。${NC}"
        exit 0
    fi
fi

# 创建新的.env文件
echo -e "${GREEN}创建新的.env文件...${NC}"
cp .env.example.new .env

# 定义可能的环境变量文件
ENV_FILES=(".env.docker" ".env.development" ".env.production" ".env.test" "backend/.env" "backend/.env.docker")

# 遍历所有可能的环境变量文件
for env_file in "${ENV_FILES[@]}"; do
    if [ -f "$env_file" ]; then
        echo -e "${GREEN}从 $env_file 中提取环境变量...${NC}"
        
        # 读取环境变量文件中的每一行
        while IFS= read -r line; do
            # 跳过注释和空行
            if [[ ! "$line" =~ ^#.*$ ]] && [[ ! -z "$line" ]]; then
                # 提取变量名和值
                var_name=$(echo "$line" | cut -d '=' -f 1)
                var_value=$(echo "$line" | cut -d '=' -f 2-)
                
                # 检查变量是否已经在.env文件中
                if grep -q "^$var_name=" .env; then
                    # 替换现有变量
                    sed -i '' "s|^$var_name=.*|$var_name=$var_value|" .env 2>/dev/null || sed -i "s|^$var_name=.*|$var_name=$var_value|" .env
                    echo -e "  ${YELLOW}更新变量: $var_name${NC}"
                fi
            fi
        done < "$env_file"
    fi
done

echo -e "${GREEN}环境变量迁移完成！${NC}"
echo -e "${YELLOW}请检查新的.env文件，确保所有变量都已正确设置。${NC}"
echo -e "${YELLOW}您可能需要手动调整一些特定于环境的变量，如数据库连接信息。${NC}"
echo
echo -e "${GREEN}下一步:${NC}"
echo -e "1. 检查并编辑.env文件，确保所有变量都已正确设置"
echo -e "2. 使用新的环境变量配置启动应用: ./docker-start.sh"
echo
echo -e "${RED}注意: 此脚本不会删除旧的环境变量文件，您可以在确认新配置正常工作后手动删除它们。${NC}"
