 AIBUBB 数据库设计 3.0 



# 第1部分：概述与设计背景

## 一、设计背景与目标

AIBUBB 是一个基于五层结构（主题→学习模板→学习计划→标签→内容）的综合学习生态系统，旨在提供个性化、系统化、有趣且有效的学习体验。本 V3.0 设计在 V2.0 基础上，根据结构化评估建议进行了全面优化，旨在提升规范性、性能、可维护性和扩展性。

### 设计目标
1.  完整实现五层核心架构
2.  支持三种内容形式（练习、观点、笔记）
3.  整合游戏化元素和社区互动功能
4.  提供灵活的扩展性和可维护性
5.  优化数据查询和处理效率
6.  统一命名规范 (`snake_case`)
7.  统一主键策略 (推荐 `BIGINT AUTO_INCREMENT`)
8.  审慎使用 JSON，拆分核心结构化数据
9.  明确数据聚合策略，采用单一可信源
10. 对大表预设分区策略
11. 完善软删除机制 (`deleted_at`)

## 二、数据库架构概览

新的数据库架构将包含以下主要模块：

1.  **用户与认证模块** (`user`, `user_setting`)
2.  **核心层次模块** (`theme`, `learning_template`, `learning_plan`, `tag`, `tag_category`, `template_tag`, `plan_tag`)
3.  **内容形式模块** (`exercise`, `insight`, `note`, `daily_content`)
4.  **游戏化元素模块** (`achievement`, `user_achievement`, `level`, `badge`, `user_badge`, `reward`, `user_reward`)
5.  **社区互动模块** (`note_like`, `tag_like`, `note_comment`, `comment_like`, `user_follow`, `tag_feedback`, `notification`)
6.  **学习追踪模块** (`learning_activity`, `daily_record`, `user_content_progress`, `user_learning_stats`)
7.  **系统配置模块** (`system_config`, `feature_flag`, `user_feature_access`)
8.  **泡泡交互模块** (`bubble_interaction`, `bubble_content`, `bubble_session`)
9.  **模板市场模块** (`template_review`, `template_transaction`, `template_access`)


## 三、核心架构说明

### 1. 五层核心架构

NebulaLearn的核心架构基于五层结构，从抽象到具体，从整体到局部：

1. **主题层（Theme）**：
    - 最高层分类，定义学习的大方向（如"人际沟通"、"职场技能"）
    - 提供学习内容的整体框架和分类

2. **学习模板层（LearningTemplate）**：
    - 预设的学习路径框架，包含标准标签集和内容组织
    - 由专业设计，确保学习路径的科学性和系统性
    - 可在模板市场中分享和获取

3. **学习计划层（LearningPlan）**：
    - 用户个性化的学习实例，可基于模板创建或完全自定义
    - 包含具体的学习目标、时间安排和进度跟踪

4. **标签层（Tag）**：
    - 知识点单元，连接学习计划和具体内容
    - 形成知识网络，支持内容的分类和关联

5. **内容层（Content）**：
    - 具体的学习材料，分为三种形式：
        - 练习(Exercise)：转化知识为行动，强化实践能力
        - 观点(Insight)：精炼的思想启发，促进认知转变
        - 笔记(Note)：系统化知识呈现，支持深度理解

### 2. 三种内容形式的设计原理

三种内容形式针对不同学习需求和认知过程设计，共同构成完整学习体验：

- **练习(Exercise)**：
    - 本质：转化知识为行动，强化实践能力
    - 结构：包含任务描述、执行指南、完成标准和反馈机制
    - 交互：需用户主动参与，产生输出，有明确完成状态
    - 认知目标：应用、分析和创造层次的能力培养

- **观点(Insight)**：
    - 本质：精炼的思想启发，促进认知转变
    - 结构：简洁有力的核心观点，可能附带来源和背景解释
    - 交互：轻量级接收，引发思考，无需复杂操作
    - 认知目标：理解和反思层次的能力培养

- **笔记(Note)**：
    - 本质：系统化知识呈现，支持深度理解
    - 结构：标题、内容、配图组成的微型文章
    - 交互：沉浸式阅读体验，支持社区互动
    - 认知目标：综合和评价层次的能力培养

### 3. 游戏化与社区互动设计

为增强学习动力和效果，系统整合了游戏化元素和社区互动功能：

- **游戏化元素**：
    - 成就系统：提供多样化的成就体系，包含不同难度和类别的成就目标
    - 等级与经验：通过经验值累积和等级提升，量化和可视化学习进步
    - 徽章收集：特定成就和里程碑的象征性奖励，可在社区中展示
    - 奖励机制：完成学习任务获得的各类奖励

- **社区互动**：
    - 内容分享：学习成果和心得可以便捷分享到社区
    - 社区反馈：点赞、评论等形成正向激励
    - 用户关注：建立学习伙伴关系，促进社交学习
    - 标签反馈：用户对标签的反馈，促进标签系统优化



## 四、数据库关系概览 (将同步更新表名)

```mermaid
erDiagram
    user ||--o{ learning_plan : creates
    user ||--o{ user_notification_setting : has
    theme ||--o{ learning_template : has
    learning_template ||--o{ learning_plan : instantiates
    tag }o--o{ learning_template : associated_with
    tag }o--o{ learning_plan : associated_with
    tag ||--o{ exercise : categorizes
    tag ||--o{ insight : categorizes
    tag ||--o{ note : categorizes
    user ||--o{ note : creates
    user ||--o{ user_achievement : earns
    user ||--o{ learning_activity : performs
    daily_content ||--o{ daily_content_relation : contains
    daily_content_relation }o--o{ exercise : references
    daily_content_relation }o--o{ insight : references
    daily_content_relation }o--o{ note : references
    user ||--o{ template_access : has
    learning_template ||--o{ template_access : grants
```

以上关系图展示了核心实体之间的主要关联，完整的关系将在后续各部分中详细说明。



# 第2部分：核心表结构设计

本部分详细定义了AIBUBB核心架构的表结构，**所有表名和字段名已统一为 `snake_case` 规范**。

## 一、用户与认证模块

### 1.1 user（用户表）
```sql
CREATE TABLE user (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户唯一标识，建议使用自增 BIGINT',
  openid VARCHAR(64) UNIQUE COMMENT '微信 OpenID',
  phone VARCHAR(20) UNIQUE COMMENT '手机号',
  nickname VARCHAR(50) COMMENT '用户昵称',
  avatar_url VARCHAR(255) COMMENT '头像URL',
  gender TINYINT COMMENT '性别：0未知，1男，2女',
  password_hash VARCHAR(100) COMMENT '密码哈希 (如果支持密码登录)',
  login_type ENUM('wechat', 'phone', 'github', 'apple') COMMENT '最近一次登录类型',
  last_login_at DATETIME COMMENT '最后登录时间',
  study_days INT DEFAULT 0 COMMENT '学习天数 (聚合数据)',
  level_id INT DEFAULT 1 COMMENT '用户等级ID',
  exp_points INT DEFAULT 0 COMMENT '经验值',
  is_admin BOOLEAN DEFAULT FALSE COMMENT '是否管理员',
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '用户状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_nickname (nickname),
  INDEX idx_level_id (level_id),
  INDEX idx_created_at (created_at),
  INDEX idx_deleted_at (deleted_at), -- 软删除索引
  FOREIGN KEY (level_id) REFERENCES level(id) -- 关联等级表
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';
```
*说明：主键已改为 `BIGINT AUTO_INCREMENT`。原 `id` 字段内容拆分为 `openid` 和 `phone`，并设为唯一索引。增加了 `deleted_at` 字段。关联了 `level` 表。*

### 1.2 user_setting（用户设置表）
```sql
CREATE TABLE user_setting (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT NOT NULL COMMENT '用户ID',
  theme_preference VARCHAR(20) DEFAULT 'system' COMMENT '主题偏好：light/dark/system',
  privacy_settings JSON COMMENT '隐私设置 (JSON 结构需定义，未来可考虑拆分)',
  learning_preferences JSON COMMENT '学习偏好设置 (JSON 结构需定义，未来可考虑拆分)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_user_id (user_id), -- 使用 uk_ 前缀表示唯一键
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户个性化设置表';
```
*说明：`user_id` 类型改为 `BIGINT`，外键关联 `user` 表。移除了 `notification_settings`，添加了 JSON 拆分相关的注释。唯一键命名改为 `uk_` 前缀。*

### 1.3 user_notification_setting（用户通知设置表）- 新增
```sql
CREATE TABLE user_notification_setting (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT NOT NULL COMMENT '用户ID',
  notification_type ENUM('like', 'comment', 'follow', 'achievement', 'system', 'reminder', 'template_update') NOT NULL COMMENT '通知类型',
  is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用此类通知',
  -- params JSON COMMENT '特定类型通知的额外参数 (例如提醒时间)', -- 可选
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  UNIQUE KEY uk_user_type (user_id, notification_type),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知偏好设置表 (从 user_setting 拆分)';
```
*说明：这是根据建议新增的表，用于替代原 `user_setting` 中的 `notification_settings` JSON 字段。*


## 二、核心层次模块

### 2.1 theme（主题表）
```sql
CREATE TABLE theme (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主题ID',
  name VARCHAR(50) NOT NULL COMMENT '主题名称',
  english_name VARCHAR(50) COMMENT '主题英文名称',
  description TEXT COMMENT '主题描述',
  icon VARCHAR(50) COMMENT '主题图标',
  color VARCHAR(20) COMMENT '主题颜色',
  cover_image_url VARCHAR(255) COMMENT '封面图片URL',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  parent_id INT COMMENT '父主题ID，用于主题分类',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_name (name),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order),
  INDEX idx_parent_id (parent_id),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (parent_id) REFERENCES theme(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习主题表';
```
*说明：字段名改为 `snake_case` (如 `cover_image_url`)。增加了 `deleted_at` 字段。*

### 2.2 learning_template（学习模板表）
```sql
CREATE TABLE learning_template (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID',
  theme_id INT NOT NULL COMMENT '关联的主题ID',
  title VARCHAR(100) NOT NULL COMMENT '模板标题',
  description TEXT COMMENT '模板描述',
  cover_image_url VARCHAR(255) COMMENT '封面图片URL',
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度级别',
  estimated_days INT DEFAULT 7 COMMENT '预计完成天数',
  daily_goal_minutes INT DEFAULT 15 COMMENT '每日学习时间目标(分钟)',
  is_official BOOLEAN DEFAULT FALSE COMMENT '是否官方模板',
  creator_id BIGINT COMMENT '创建者ID（官方模板为NULL）',
  popularity INT DEFAULT 0 COMMENT '使用人数/受欢迎程度 (聚合数据)',
  rating DECIMAL(2,1) DEFAULT 5.0 COMMENT '评分(1-5，允许半星，聚合数据)',
  rating_count INT DEFAULT 0 COMMENT '评分人数 (聚合数据)',
  price DECIMAL(10,2) DEFAULT 0.00 COMMENT '价格（0表示免费）',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_theme_id (theme_id),
  INDEX idx_difficulty (difficulty),
  INDEX idx_is_official (is_official),
  INDEX idx_popularity (popularity),
  INDEX idx_creator_id (creator_id),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (theme_id) REFERENCES theme(id) ON DELETE CASCADE,
  FOREIGN KEY (creator_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习模板定义表';
```
*说明：表名、字段名改为 `snake_case`。`creator_id` 类型改为 `BIGINT`。增加了 `deleted_at` 字段。为聚合字段添加了注释。*

### 2.3 learning_plan（学习计划表）
```sql
CREATE TABLE learning_plan (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '计划ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  template_id INT COMMENT '关联的模板ID (可为空，表示自定义计划)',
  theme_id INT COMMENT '关联的主题ID (冗余字段，也可通过 template_id 获取)',
  title VARCHAR(100) NOT NULL COMMENT '计划标题',
  description TEXT COMMENT '计划描述',
  cover_image_url VARCHAR(255) COMMENT '封面图片URL',
  target_days INT DEFAULT 7 COMMENT '目标完成天数',
  completed_days INT DEFAULT 0 COMMENT '已完成天数 (聚合数据)',
  progress INT DEFAULT 0 COMMENT '进度百分比 (聚合数据, 从 user_content_progress 计算)',
  daily_goal_exercises INT DEFAULT 3 COMMENT '每日练习目标数量',
  daily_goal_insights INT DEFAULT 5 COMMENT '每日观点目标数量',
  daily_goal_minutes INT DEFAULT 15 COMMENT '每日学习时间目标(分钟)',
  status ENUM('not_started', 'in_progress', 'completed', 'paused', 'abandoned') DEFAULT 'not_started' COMMENT '状态',
  start_date DATE COMMENT '开始日期',
  end_date DATE COMMENT '结束日期',
  is_current BOOLEAN DEFAULT FALSE COMMENT '是否为当前活跃计划',
  is_system_default BOOLEAN DEFAULT FALSE COMMENT '是否为系统默认计划',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_user_id (user_id),
  INDEX idx_template_id (template_id),
  INDEX idx_theme_id (theme_id),
  INDEX idx_status (status),
  INDEX idx_is_current (is_current),
  INDEX idx_is_system_default (is_system_default),
  INDEX idx_is_public (is_public),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (template_id) REFERENCES learning_template(id) ON DELETE SET NULL,
  FOREIGN KEY (theme_id) REFERENCES theme(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习计划实例表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。增加了 `deleted_at` 字段。为聚合字段添加了注释和来源说明。*

### 2.4 tag_category（标签分类表）
```sql
CREATE TABLE tag_category (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
  name VARCHAR(50) NOT NULL COMMENT '分类名称',
  description TEXT COMMENT '分类描述',
  parent_id INT COMMENT '父分类ID',
  theme_id INT COMMENT '所属主题ID (可选，用于按主题过滤)',
  level INT DEFAULT 1 COMMENT '层级，1为顶级分类',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_name (name),
  INDEX idx_parent_id (parent_id),
  INDEX idx_theme_id (theme_id),
  INDEX idx_level (level),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (parent_id) REFERENCES tag_category(id) ON DELETE SET NULL,
  FOREIGN KEY (theme_id) REFERENCES theme(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签分类层级表';
```
*说明：表名改为 `snake_case`。增加了 `deleted_at` 字段。*

### 2.5 tag（标签表）
```sql
CREATE TABLE tag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
  name VARCHAR(50) NOT NULL COMMENT '标签名称',
  category_id INT COMMENT '关联的标签分类ID',
  creator_id BIGINT COMMENT '创建者ID (NULL表示官方)',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1, 可由算法或反馈调整)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)，用于排序和推荐',
  usage_count INT DEFAULT 0 COMMENT '使用次数 (聚合数据)',
  is_verified BOOLEAN DEFAULT FALSE COMMENT '是否经过验证 (例如由管理员)',
  is_official BOOLEAN DEFAULT TRUE COMMENT '是否官方标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_name (name),
  INDEX idx_category_id (category_id),
  INDEX idx_creator_id (creator_id),
  INDEX idx_weight (weight),
  INDEX idx_usage_count (usage_count),
  INDEX idx_is_verified (is_verified),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (category_id) REFERENCES tag_category(id) ON DELETE SET NULL,
  FOREIGN KEY (creator_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识标签表';
```
*说明：表名、字段名改为 `snake_case`。`creator_id` 类型改为 `BIGINT`。增加了 `deleted_at` 字段。`usage_count` 标记为聚合数据。*

### 2.6 tag_synonym（标签同义词表）
```sql
CREATE TABLE tag_synonym (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '同义词ID',
  primary_tag_id INT NOT NULL COMMENT '主标签ID',
  synonym_name VARCHAR(50) NOT NULL COMMENT '同义词名称',
  similarity_score FLOAT DEFAULT 0.8 COMMENT '相似度得分(0-1)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_primary_tag_id (primary_tag_id),
  INDEX idx_synonym_name (synonym_name),
  UNIQUE KEY uk_primary_synonym (primary_tag_id, synonym_name),
  FOREIGN KEY (primary_tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签同义词/别名表';
```
*说明：表名、字段名改为 `snake_case`。添加了唯一约束。*

### 2.7 template_tag（模板标签关联表）
```sql
CREATE TABLE template_tag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
  template_id INT NOT NULL COMMENT '模板ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)',
  is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为主要标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_template_id (template_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_is_primary (is_primary),
  UNIQUE KEY uk_template_tag (template_id, tag_id),
  FOREIGN KEY (template_id) REFERENCES learning_template(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习模板与标签的关联表';
```
*说明：表名改为 `snake_case`。唯一键命名改为 `uk_` 前缀。*

### 2.8 plan_tag（计划标签关联表）
```sql
CREATE TABLE plan_tag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
  plan_id INT NOT NULL COMMENT '学习计划ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)',
  is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为主要标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_is_primary (is_primary),
  UNIQUE KEY uk_plan_tag (plan_id, tag_id),
  FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习计划与标签的关联表';
```
*说明：表名改为 `snake_case`。唯一键命名改为 `uk_` 前缀。*

## 三、核心表关系说明

### 1. 主题与模板关系
- 一个主题 (`theme`) 可以包含多个学习模板 (`learning_template`)。
- 主题可以形成层级结构（通过 `parent_id` 自关联）。
- 主题与标签分类 (`tag_category`) 相关联，提供标签 (`tag`) 的分类框架。

### 2. 模板与计划关系
- 学习模板 (`learning_template`) 是学习计划 (`learning_plan`) 的模板，一个模板可以实例化为多个学习计划。
- 学习计划可以关联到模板，也可以独立存在（`template_id` 为空）。
- 学习计划同时也可以直接关联到主题（`theme_id`）。

### 3. 标签与内容关系
- 标签 (`tag`) 是连接学习计划 (`learning_plan`) 和具体内容（如 `exercise`, `insight`, `note`）的桥梁。
- 标签可以通过 `tag_category` 进行分类。
- 标签可以通过 `tag_synonym` 管理同义词。
- 标签通过 `template_tag` 关联到学习模板。
- 标签通过 `plan_tag` 关联到学习计划。

### 4. 用户与学习关系
- 用户 (`user`) 可以创建多个学习计划 (`learning_plan`)。
- 用户可以创建自定义标签 (`tag`，`creator_id` 非 NULL）。
- 用户可以创建学习模板 (`learning_template`，`creator_id` 非 NULL，`is_official` 为 FALSE）。
- 用户可以通过 `user_setting` 和 `user_notification_setting` 定制个性化设置。

## 四、核心表设计要点

### 1. 灵活性设计
- 审慎使用 JSON 字段存储灵活结构数据（如 `user_setting.privacy_settings`），这提供了良好的扩展性，但也需注意 JSON 内数据查询、索引和约束的复杂性，以及可能带来的性能影响。应确保其结构有一定规范，避免成为无结构的"垃圾桶"。对于结构化或需频繁查询的数据（如原 `notification_settings`），已拆分为独立表 (`user_notification_setting`)。
- 预留扩展字段和状态枚举值。
- 完善软删除机制，统一使用 `deleted_at` 字段标记删除时间。

### 2. 性能优化
- 为常用查询过滤、排序字段添加适当索引（如 `status`, `type`, `created_at`）。
- 使用复合索引优化多字段组合查询，注意字段顺序。
- 为所有外键添加索引。
- 对潜在的大表（如 `learning_activity`）预设分区策略。
- 为 `deleted_at` 字段添加索引，优化活跃数据查询。

### 3. 数据完整性
- 使用外键约束确保引用完整性 (`FOREIGN KEY`)。
- 使用唯一约束 (`UNIQUE KEY`) 防止业务逻辑上的重复数据。
- 为重要字段设置合理的默认值 (`DEFAULT`)。

### 4. 时间追踪与审计
- 所有表（或绝大多数）都包含 `created_at` 和 `updated_at` 字段，追踪记录创建和最后更新时间。
- 使用 `deleted_at` 字段进行软删除，保留删除时间戳，便于数据审计和恢复。
- 特定业务表包含更具体的业务时间字段（如 `user_achievement.achieved_at`）。





# 第3部分：内容表结构设计

本部分详细定义了AIBUBB三种内容形式（练习、观点、笔记）的表结构设计，以及每日内容相关的表设计。

## 一、内容形式模块

### 1.1 exercise（练习表）
```sql
CREATE TABLE exercise (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '练习ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  title VARCHAR(100) NOT NULL COMMENT '练习标题',
  description TEXT NOT NULL COMMENT '练习描述',
  expected_result TEXT COMMENT '预期结果',
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度级别',
  time_estimate_minutes INT DEFAULT 5 COMMENT '预计完成时间(分钟)',
  creator_id BIGINT COMMENT '创建者ID (NULL表示官方)',
  is_official BOOLEAN DEFAULT TRUE COMMENT '是否官方内容',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_tag_id (tag_id),
  INDEX idx_difficulty (difficulty),
  INDEX idx_creator_id (creator_id),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FULLTEXT INDEX ft_title_description (title, description),
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE,
  FOREIGN KEY (creator_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='练习内容表';
```
*说明：表名、字段名改为 `snake_case`。`creator_id` 类型改为 `BIGINT`。增加了 `deleted_at` 字段。`time_estimate` 重命名为 `time_estimate_minutes`。*

### 1.2 insight（观点表）
```sql
CREATE TABLE insight (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '观点ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  content TEXT NOT NULL COMMENT '观点内容',
  source VARCHAR(100) COMMENT '来源',
  background TEXT COMMENT '背景解释',
  creator_id BIGINT COMMENT '创建者ID (NULL表示官方)',
  is_official BOOLEAN DEFAULT TRUE COMMENT '是否官方内容',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_tag_id (tag_id),
  INDEX idx_creator_id (creator_id),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FULLTEXT INDEX ft_content (content),
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE,
  FOREIGN KEY (creator_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='观点内容表';
```
*说明：表名、字段名改为 `snake_case`。`creator_id` 类型改为 `BIGINT`。增加了 `deleted_at` 字段。*

### 1.3 note（笔记表）
```sql
CREATE TABLE note (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '笔记ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  title VARCHAR(100) NOT NULL COMMENT '笔记标题',
  content TEXT NOT NULL COMMENT '笔记内容',
  image_urls JSON COMMENT '配图URL数组',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
  like_count INT DEFAULT 0 COMMENT '点赞数 (聚合数据)',
  comment_count INT DEFAULT 0 COMMENT '评论数 (聚合数据)',
  view_count INT DEFAULT 0 COMMENT '查看次数 (聚合数据)',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_user_id (user_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_is_public (is_public),
  INDEX idx_like_count (like_count),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FULLTEXT INDEX ft_title_content (title, content),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户笔记表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。增加了 `deleted_at` 字段。为聚合字段添加了注释。*

### 1.4 daily_content（每日内容表）
```sql
CREATE TABLE daily_content (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '每日内容ID',
  plan_id INT NOT NULL COMMENT '学习计划ID',
  day_number INT NOT NULL COMMENT '天数 (从1开始)',
  title VARCHAR(100) NOT NULL COMMENT '标题',
  content TEXT COMMENT '当天的主要文本内容或说明',
  content_type ENUM('text', 'exercise', 'insight', 'note', 'mixed') DEFAULT 'text' COMMENT '主要内容类型 (关联内容见 daily_content_relation)',
  -- related_content_ids JSON COMMENT '关联内容ID (V3.0 已拆分为 daily_content_relation 表)',
  is_completed BOOLEAN DEFAULT FALSE COMMENT '是否已完成 (基于 user_content_progress)',
  completion_date DATETIME COMMENT '完成时间 (聚合数据)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_day_number (day_number),
  INDEX idx_is_completed (is_completed),
  FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习计划每日安排表';
```
*说明：表名、字段名改为 `snake_case`。移除了 `related_content_ids`，添加了 JSON 拆分相关的注释。聚合字段添加注释。*

### 1.5 daily_content_relation（每日内容关联表）- 新增
```sql
CREATE TABLE daily_content_relation (
  id INT AUTO_INCREMENT PRIMARY KEY,
  daily_content_id INT NOT NULL COMMENT '每日内容ID',
  related_content_type ENUM('exercise', 'insight', 'note') NOT NULL COMMENT '关联内容的类型',
  related_content_id INT NOT NULL COMMENT '关联内容的ID',
  sort_order INT DEFAULT 0 COMMENT '关联内容的显示顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_daily_content_id (daily_content_id),
  INDEX idx_related_content (related_content_type, related_content_id),
  UNIQUE KEY uk_daily_content_relation (daily_content_id, related_content_type, related_content_id),
  FOREIGN KEY (daily_content_id) REFERENCES daily_content(id) ON DELETE CASCADE
  -- 注意：这里不直接加外键到 exercise/insight/note 表，避免跨类型外键的复杂性，由应用层保证关联 ID 的有效性
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='每日内容与具体学习内容的关联表';
```
*说明：这是根据建议新增的表，用于替代原 `daily_content` 中的 `related_content_ids` JSON 字段。*


## 二、内容表设计说明

(内容更新以反映 V3.0 的变化)

### 1. 三种内容形式的共同特点

三种核心内容形式（`exercise`, `insight`, `note`）都具有以下共同特点：

1.  **标签关联**：通过 `tag_id` 关联到 `tag` 表。
2.  **创建者追踪**：通过 `creator_id` (`exercise`, `insight`) 或 `user_id` (`note`) 关联到 `user` 表。
3.  **状态管理**：通过 `status` 字段管理生命周期。
4.  **时间追踪**：包含 `created_at` 和 `updated_at`。
5.  **软删除**：增加了 `deleted_at` 字段支持软删除。
6.  **全文索引**：为主要文本内容添加了 `FULLTEXT` 索引以支持搜索。

### 2. 各内容形式的特殊设计

#### 2.1 练习(exercise)特殊设计
- ...
- **时间估计**：通过 `time_estimate_minutes` 字段指示预计完成时间(分钟)
- ...

#### 2.2 观点(insight)特殊设计
- ...

#### 2.3 笔记(note)特殊设计
- ...
- **社区互动计数**：包含 `like_count`, `comment_count`, `view_count`。这些是聚合字段，其更新应通过后台任务、触发器或应用层事务逻辑来维护，确保与 `note_like`, `note_comment` 等表的数据一致性。**在高并发场景下，需要特别注意更新策略的原子性和性能，例如使用数据库事务配合乐观锁或悲观锁，或采用消息队列异步更新等方式保证最终一致性。**
- ...

### 3. 每日内容表设计 (`daily_content`, `daily_content_relation`)

`daily_content` 表定义了学习计划中每一天的安排框架，而 `daily_content_relation` 表则具体关联了当天需要学习的内容项：

1.  **计划关联**：`daily_content` 通过 `plan_id` 和 `day_number` 定位到计划的某一天。
2.  **内容关联**：`daily_content_relation` 存储当天需要关联的练习、观点或笔记的 ID 和类型。
3.  **完成状态**：`daily_content` 的 `is_completed` 字段是一个聚合状态，应根据 `user_content_progress` 表中当天所有关联内容的完成情况来更新。

## 三、内容关系与流动

(内容保持不变，但涉及表名/字段名的地方会同步修改)

## 四、内容表索引优化

(内容更新以反映 V3.0 的变化)

除了主键、外键和 `deleted_at` 索引外，关键索引包括：
- `exercise`, `insight`, `note` 的 `tag_id` 和 `status` 索引。
- `note` 的 `user_id`, `is_public` 索引。
- 为 `exercise`, `insight`, `note` 添加了 `FULLTEXT` 索引。
- `daily_content` 的 `plan_id`, `day_number` 索引。
- `daily_content_relation` 的 `daily_content_id` 和 `related_content` 复合索引。

## 五、内容表扩展性考虑

(内容保持不变)



    


# 第4部分：游戏化与社区表结构设计

本部分详细定义了AIBUBB的游戏化元素和社区互动功能相关的表结构设计。

## 一、游戏化元素模块

### 1.1 achievement（成就表）
```sql
CREATE TABLE achievement (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '成就ID',
  name VARCHAR(50) NOT NULL COMMENT '成就名称',
  description TEXT NOT NULL COMMENT '成就描述',
  icon VARCHAR(50) COMMENT '成就图标',
  category ENUM('learning', 'social', 'creation', 'special') NOT NULL COMMENT '成就类别',
  difficulty ENUM('easy', 'medium', 'hard', 'expert') NOT NULL COMMENT '难度',
  points INT DEFAULT 10 COMMENT '获得点数',
  condition_type VARCHAR(50) NOT NULL COMMENT '触发条件类型 (如 complete_exercises, consecutive_days)',
  condition_value JSON COMMENT '触发条件值 (JSON结构需定义，未来可考虑拆分)',
  is_hidden BOOLEAN DEFAULT FALSE COMMENT '是否隐藏成就',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_difficulty (difficulty),
  INDEX idx_is_hidden (is_hidden),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='成就定义表';
```
*说明：表名、字段名改为 `snake_case`。为 JSON 字段添加了注释。*

### 1.2 user_achievement（用户成就表）
```sql
CREATE TABLE user_achievement (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户成就ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  achievement_id INT NOT NULL COMMENT '成就ID',
  achieved_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  progress INT DEFAULT 100 COMMENT '完成进度百分比 (对于需要累积的成就)',
  is_notified BOOLEAN DEFAULT FALSE COMMENT '是否已通知用户',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_achievement_id (achievement_id),
  INDEX idx_achieved_at (achieved_at),
  UNIQUE KEY uk_user_achievement (user_id, achievement_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (achievement_id) REFERENCES achievement(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户已获成就记录表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。唯一键命名改为 `uk_` 前缀。*

### 1.3 level（等级表）
```sql
CREATE TABLE level (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '等级ID',
  level_number INT NOT NULL COMMENT '等级数值',
  name VARCHAR(50) NOT NULL COMMENT '等级名称',
  required_exp INT NOT NULL COMMENT '达到该等级所需总经验值',
  icon VARCHAR(50) COMMENT '等级图标',
  rewards JSON COMMENT '等级奖励 (JSON结构需定义，未来可考虑拆分)',
  description TEXT COMMENT '等级描述',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_level_number (level_number),
  INDEX idx_required_exp (required_exp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户等级定义表';
```
*说明：表名、字段名改为 `snake_case`。为 JSON 字段添加了注释。唯一键命名改为 `uk_` 前缀。*

### 1.4 badge（徽章表）
```sql
CREATE TABLE badge (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '徽章ID',
  name VARCHAR(50) NOT NULL COMMENT '徽章名称',
  description TEXT NOT NULL COMMENT '徽章描述',
  icon VARCHAR(50) NOT NULL COMMENT '徽章图标',
  category ENUM('theme', 'skill', 'event', 'special') NOT NULL COMMENT '徽章类别',
  rarity ENUM('common', 'uncommon', 'rare', 'epic', 'legendary') DEFAULT 'common' COMMENT '稀有度',
  is_displayable BOOLEAN DEFAULT TRUE COMMENT '是否可在个人资料页展示',
  unlock_condition TEXT COMMENT '解锁条件描述 (给用户看)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_rarity (rarity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='徽章定义表';
```
*说明：表名、字段名改为 `snake_case`。`unlock_condition` 改为 `TEXT`。*

### 1.5 user_badge（用户徽章表）
```sql
CREATE TABLE user_badge (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户徽章ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  badge_id INT NOT NULL COMMENT '徽章ID',
  acquired_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  is_equipped BOOLEAN DEFAULT FALSE COMMENT '是否装备中 (用于个人资料页展示)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_badge_id (badge_id),
  INDEX idx_is_equipped (is_equipped),
  UNIQUE KEY uk_user_badge (user_id, badge_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (badge_id) REFERENCES badge(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户已获徽章记录表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。唯一键命名改为 `uk_` 前缀。*

### 1.6 reward（奖励表）
```sql
CREATE TABLE reward (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '奖励ID',
  name VARCHAR(50) NOT NULL COMMENT '奖励名称',
  description TEXT NOT NULL COMMENT '奖励描述',
  reward_type ENUM('badge', 'exp', 'theme', 'feature', 'template', 'other') NOT NULL COMMENT '奖励类型',
  value JSON COMMENT '奖励值 (例如 badge_id, exp_points, theme_id 等，JSON结构需定义)',
  icon VARCHAR(50) COMMENT '奖励图标',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_reward_type (reward_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='奖励定义表';
```
*说明：表名、字段名改为 `snake_case`。为 JSON 字段添加了注释。*

### 1.7 user_reward（用户奖励表）
```sql
CREATE TABLE user_reward (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户奖励ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  reward_id INT NOT NULL COMMENT '奖励ID',
  source_type VARCHAR(50) NOT NULL COMMENT '奖励来源类型 (如 achievement, level_up, admin)',
  source_id INT COMMENT '奖励来源ID (如 achievement_id)',
  received_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  is_claimed BOOLEAN DEFAULT FALSE COMMENT '是否已领取',
  claimed_at DATETIME COMMENT '领取时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_reward_id (reward_id),
  INDEX idx_source_type (source_type),
  INDEX idx_is_claimed (is_claimed),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (reward_id) REFERENCES reward(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户获得奖励记录表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。*


## 二、社区互动模块

### 2.1 note_like（笔记点赞表）
```sql
CREATE TABLE note_like (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '笔记点赞ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  note_id INT NOT NULL COMMENT '笔记ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_note_id (note_id),
  UNIQUE KEY uk_user_note (user_id, note_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (note_id) REFERENCES note(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='笔记点赞记录表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。唯一键命名改为 `uk_` 前缀。*

### 2.2 tag_like（标签点赞表）
```sql
CREATE TABLE tag_like (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签点赞ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_tag_id (tag_id),
  UNIQUE KEY uk_user_tag (user_id, tag_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签点赞记录表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。唯一键命名改为 `uk_` 前缀。*

### 2.3 note_comment（笔记评论表）
```sql
CREATE TABLE note_comment (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '评论ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  note_id INT NOT NULL COMMENT '笔记ID',
  parent_id INT COMMENT '父评论ID（用于回复嵌套）',
  content TEXT NOT NULL COMMENT '评论内容',
  like_count INT DEFAULT 0 COMMENT '点赞数 (聚合数据)',
  status ENUM('active', 'hidden_by_user', 'hidden_by_admin', 'deleted') DEFAULT 'active' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_user_id (user_id),
  INDEX idx_note_id (note_id),
  INDEX idx_parent_id (parent_id),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (note_id) REFERENCES note(id) ON DELETE CASCADE,
  FOREIGN KEY (parent_id) REFERENCES note_comment(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='笔记评论表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。增加了 `deleted_at` 字段。改进了 `status` 枚举值。*

### 2.4 comment_like（评论点赞表）
```sql
CREATE TABLE comment_like (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '评论点赞ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  comment_id INT NOT NULL COMMENT '评论ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_comment_id (comment_id),
  UNIQUE KEY uk_user_comment (user_id, comment_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (comment_id) REFERENCES note_comment(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论点赞记录表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。唯一键命名改为 `uk_` 前缀。*

### 2.5 user_follow（用户关注表）
```sql
CREATE TABLE user_follow (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关注ID',
  follower_id BIGINT NOT NULL COMMENT '关注者ID',
  following_id BIGINT NOT NULL COMMENT '被关注者ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_follower_id (follower_id),
  INDEX idx_following_id (following_id),
  UNIQUE KEY uk_follow (follower_id, following_id),
  FOREIGN KEY (follower_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (following_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关注关系表';
```
*说明：表名、字段名改为 `snake_case`。`follower_id` 和 `following_id` 类型改为 `BIGINT`。唯一键命名改为 `uk_` 前缀。*

### 2.6 tag_feedback（标签反馈表）
```sql
CREATE TABLE tag_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签反馈ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  feedback_type ENUM('relevance', 'suggestion', 'report_error', 'report_duplicate') NOT NULL COMMENT '反馈类型',
  content TEXT COMMENT '反馈内容',
  status ENUM('pending', 'reviewed', 'implemented', 'rejected') DEFAULT 'pending' COMMENT '处理状态',
  reviewer_id BIGINT COMMENT '处理人ID (管理员)',
  reviewed_at DATETIME COMMENT '处理时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_feedback_type (feedback_type),
  INDEX idx_status (status),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE,
  FOREIGN KEY (reviewer_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户对标签的反馈表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 和 `reviewer_id` 类型改为 `BIGINT`。改进了 `feedback_type` 枚举值。增加了处理人相关字段。*

### 2.7 notification（通知表）
```sql
CREATE TABLE notification (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '通知ID',
  user_id BIGINT NOT NULL COMMENT '接收用户ID',
  sender_id BIGINT COMMENT '发送者ID (NULL表示系统)',
  notification_type ENUM('like', 'comment', 'follow', 'achievement', 'system', 'reminder', 'badge', 'level_up') NOT NULL COMMENT '通知类型',
  content TEXT NOT NULL COMMENT '通知内容',
  reference_type VARCHAR(50) COMMENT '引用对象类型 (如 note, comment, user, achievement)',
  reference_id VARCHAR(50) COMMENT '引用对象ID (使用VARCHAR兼容不同类型主键，如 user_id<BIGINT>, note_id<INT>)',
  is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
  read_at DATETIME COMMENT '阅读时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_sender_id (sender_id),
  INDEX idx_notification_type (notification_type),
  INDEX idx_is_read (is_read),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (sender_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知消息表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 和 `sender_id` 类型改为 `BIGINT`。`reference_id` 类型改为 `VARCHAR` 以兼容不同类型 ID。*


## 三、游戏化元素设计说明

(内容更新以反映 V3.0 的变化)

### 1. 成就系统设计 (`achievement`, `user_achievement`)

成就系统是游戏化的核心元素，具有以下特点：

1.  **多样化成就类别**：`category` 字段定义。
2.  **难度梯度**：`difficulty` 字段定义。
3.  **条件触发机制**：`condition_type` 定义类型，`condition_value` (JSON) 存储参数。*注意：此 JSON 字段未来可根据复杂度考虑拆分。*
4.  **隐藏成就**：通过 `is_hidden` 字段支持。
5.  **进度追踪**：`user_achievement` 表的 `progress` 字段支持。

### 2. 等级与徽章系统 (`level`, `badge`, `user_badge`)

等级和徽章系统提供了可视化的进步和成就展示：

1.  **等级系统**：`level` 表定义等级体系和所需经验 (`required_exp`)。用户当前等级通过 `user.level_id` 关联。
2.  **徽章系统**：`badge` 表定义徽章属性，`user_badge` 记录用户获得的徽章。

### 3. 奖励系统 (`reward`, `user_reward`)

通过 `reward` 和 `user_reward` 表实现奖励机制。`reward.value` 是 JSON 字段，*未来可根据复杂度考虑拆分。*


## 四、社区互动设计说明

(内容更新以反映 V3.0 的变化)

### 1. 点赞与评论系统 (`note_like`, `tag_like`, `note_comment`, `comment_like`)

点赞和评论是基础的社区互动功能：

1.  **点赞机制**：通过 `note_like`, `tag_like`, `comment_like` 表记录。相关聚合字段 (`note.like_count`, `note_comment.like_count`) 需要同步更新 (通过应用层事务或触发器)。**实现时需确保操作的原子性，避免丢失更新。**
2.  **评论系统**：`note_comment` 表支持评论和回复 (`parent_id`)。评论状态增加了更细致的隐藏类型。增加了 `deleted_at` 字段。**聚合字段 `note.comment_count` 的更新也需要保证原子性。**

### 2. 用户关注与通知 (`user_follow`, `notification`)

用户关注和通知系统增强社区连接：

1.  **用户关注**：`user_follow` 表记录关注关系。
2.  **通知系统**：`notification` 表实现全面的通知功能。`reference_id` 改为 `VARCHAR` 以增强灵活性。

### 3. 标签反馈机制 (`tag_feedback`)

`tag_feedback` 表允许用户参与标签系统优化。增加了处理人 (`reviewer_id`) 和处理时间 (`reviewed_at`) 字段。


## 五、游戏化与社区表关系

(内容保持不变，但涉及表名/字段名的地方会同步修改)

## 六、索引优化与扩展性

(内容保持不变，但涉及表名/字段名的地方会同步修改)





# 第5部分：学习追踪与系统配置表结构设计

本部分详细定义了AIBUBB的学习追踪、泡泡交互和系统配置相关的表结构设计。

## 一、学习追踪模块

### 1.1 learning_activity（学习活动表）
```sql
CREATE TABLE learning_activity (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '活动ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  plan_id INT COMMENT '学习计划ID (某些活动可能无计划)',
  activity_type ENUM('exercise', 'insight', 'note', 'daily_content', 'template', 'bubble', 'plan_action', 'login', 'search') NOT NULL COMMENT '活动类型',
  content_id VARCHAR(50) COMMENT '关联内容/对象ID (VARCHAR 兼容不同类型ID)',
  action ENUM('view', 'complete', 'create', 'like', 'comment', 'share', 'start', 'pause', 'resume', 'abandon', 'tap', 'hold', 'merge', 'dismiss') NOT NULL COMMENT '具体行为',
  duration_seconds INT DEFAULT 0 COMMENT '持续时间(秒)',
  details JSON COMMENT '活动详情 (JSON结构需定义，避免滥用)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_plan_id (plan_id),
  INDEX idx_activity_type (activity_type),
  INDEX idx_content_id (content_id),
  INDEX idx_action (action),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习活动流水表 (大表，需分区)'
PARTITION BY RANGE (YEAR(created_at)) (
  PARTITION p_init VALUES LESS THAN (2024), -- 初始分区，捕获旧数据
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026),
  PARTITION p2026 VALUES LESS THAN (2027)
  -- 后续每年需要手动添加新年份的分区
  -- PARTITION p_future VALUES LESS THAN MAXVALUE -- 可选，捕获未来数据
);
```
*说明：表名、字段名改为 `snake_case`。`id` 和 `user_id` 类型改为 `BIGINT`。`plan_id` 设为可空。`content_id` 改为 `VARCHAR`。扩展了 `activity_type` 和 `action` 枚举。为 `details` 添加注释。**增加了按年份 `created_at` 的范围分区策略**。*

### 1.2 daily_record（每日记录表）
```sql
CREATE TABLE daily_record (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '每日记录ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  record_date DATE NOT NULL COMMENT '记录日期',
  total_time_minutes INT DEFAULT 0 COMMENT '学习总时间(分钟) (聚合数据)',
  exercises_completed INT DEFAULT 0 COMMENT '完成练习数 (聚合数据)',
  insights_viewed INT DEFAULT 0 COMMENT '查看观点数 (聚合数据)',
  notes_created INT DEFAULT 0 COMMENT '创建笔记数 (聚合数据)',
  exp_gained INT DEFAULT 0 COMMENT '获得经验值 (聚合数据)',
  streak_days INT DEFAULT 1 COMMENT '连续学习天数 (聚合数据)',
  mood ENUM('great', 'good', 'neutral', 'bad', 'terrible') COMMENT '心情记录',
  summary TEXT COMMENT '学习总结',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_record_date (record_date),
  UNIQUE KEY uk_user_date (user_id, record_date),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户每日学习活动聚合记录表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。为所有统计字段添加了"聚合数据"注释，强调其非实时性。唯一键命名改为 `uk_` 前缀。*

### 1.3 user_content_progress（用户内容进度表）
```sql
CREATE TABLE user_content_progress (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '进度ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  content_type ENUM('exercise', 'insight', 'daily_content', 'note') NOT NULL COMMENT '内容类型',
  content_id INT NOT NULL COMMENT '内容ID',
  plan_id INT NOT NULL COMMENT '学习计划ID',
  progress INT DEFAULT 0 COMMENT '进度百分比 (例如对于练习的多步骤)',
  status ENUM('not_started', 'in_progress', 'completed', 'skipped') DEFAULT 'not_started' COMMENT '内容完成状态',
  completion_date DATETIME COMMENT '完成时间',
  last_interaction_at DATETIME COMMENT '最后交互时间',
  proficiency_level ENUM('not_started', 'beginner', 'familiar', 'proficient', 'expert') DEFAULT 'not_started' COMMENT '熟练度 (可由算法评估)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_content_type_id (content_type, content_id),
  INDEX idx_plan_id (plan_id),
  INDEX idx_status (status),
  UNIQUE KEY uk_user_content_plan (user_id, content_type, content_id, plan_id), -- 同一计划下同一内容的进度唯一
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE CASCADE
  -- 注意：不直接加外键到 exercise/insight/note 表
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户对具体内容的学习进度表 (单一可信源)';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。`is_completed` 改为 `status` 枚举。`last_interaction_date` 改为 `last_interaction_at`。唯一键增加了 `plan_id`。强调了其作为"单一可信源"的角色。*

### 1.4 user_learning_stats（用户学习统计表）
```sql
CREATE TABLE user_learning_stats (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '统计ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  total_learning_time_minutes INT DEFAULT 0 COMMENT '总学习时间(分钟) (聚合数据)',
  total_exercises_completed INT DEFAULT 0 COMMENT '完成练习总数 (聚合数据)',
  total_insights_viewed INT DEFAULT 0 COMMENT '查看观点总数 (聚合数据)',
  total_notes_created INT DEFAULT 0 COMMENT '创建笔记总数 (聚合数据)',
  total_plans_completed INT DEFAULT 0 COMMENT '完成学习计划数 (聚合数据)',
  current_streak_days INT DEFAULT 0 COMMENT '当前连续学习天数 (聚合数据)',
  max_streak_days INT DEFAULT 0 COMMENT '最长连续学习天数 (聚合数据)',
  total_study_days INT DEFAULT 0 COMMENT '总学习天数 (聚合数据)',
  last_active_date DATE COMMENT '最后活跃日期 (聚合数据)',
  favorite_tags JSON COMMENT '喜爱的标签 (聚合数据, 结构需定义)',
  learning_patterns JSON COMMENT '学习模式数据 (聚合数据, 结构需定义)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_current_streak_days (current_streak_days),
  INDEX idx_total_study_days (total_study_days),
  UNIQUE KEY uk_user_id (user_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户全局学习统计聚合表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。为所有统计字段添加了"聚合数据"注释。`consecutive_days` 改为 `current_streak_days`，`total_days` 改为 `total_study_days`，`max_consecutive_days` 改为 `max_streak_days`。唯一键命名改为 `uk_` 前缀。*


## 二、泡泡交互模块

### 2.1 bubble_interaction（泡泡交互表）
```sql
CREATE TABLE bubble_interaction (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '交互ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  tag_id INT COMMENT '关联的标签ID (交互时选中的标签)',
  interaction_type ENUM('tap', 'hold', 'merge', 'dismiss') NOT NULL COMMENT '交互类型',
  session_id VARCHAR(50) NOT NULL COMMENT '会话ID (关联 bubble_session.session_uuid)',
  bubble_id VARCHAR(50) NOT NULL COMMENT '泡泡自身ID',
  position_data JSON COMMENT '位置数据 (x, y, size等，JSON结构需定义)',
  result_content_type VARCHAR(50) COMMENT '交互结果内容类型 (如 exercise, insight)',
  result_content_id VARCHAR(50) COMMENT '交互结果内容ID (VARCHAR兼容不同类型)',
  duration_ms INT DEFAULT 0 COMMENT '交互持续时间(毫秒)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_interaction_type (interaction_type),
  INDEX idx_session_id (session_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE SET NULL
  -- 不建议加外键到 bubble_session，因为 session_id 可能非主键
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='泡泡界面交互流水表 (大表，可考虑分区)';
-- 可选分区策略： PARTITION BY HASH(user_id) PARTITIONS 16; 或按时间分区
```
*说明：表名、字段名改为 `snake_case`。`id` 和 `user_id` 类型改为 `BIGINT`。`session_id` 关联 `bubble_session.session_uuid`。`result_content_id` 改为 `VARCHAR`。`duration` 改为 `duration_ms`。为 JSON 字段添加注释。增加了分区考虑的注释。*

### 2.2 bubble_content（泡泡内容表）
```sql
CREATE TABLE bubble_content (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '泡泡内容ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  content_type ENUM('exercise', 'insight', 'note', 'mixed') NOT NULL COMMENT '内容类型',
  content_id INT COMMENT '关联的具体内容ID (如果非mixed)',
  title VARCHAR(100) COMMENT '泡泡显示的标题 (可冗余)',
  summary TEXT COMMENT '泡泡显示的摘要 (可冗余)',
  visual_properties JSON COMMENT '视觉属性 (大小, 颜色, 动效等，JSON结构需定义)',
  priority INT DEFAULT 5 COMMENT '优先级(1-10)，影响出现概率或顺序',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活可用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tag_id (tag_id),
  INDEX idx_content_type (content_type),
  INDEX idx_content_id (content_id),
  INDEX idx_priority (priority),
  INDEX idx_is_active (is_active),
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定义可在泡泡界面展示的内容源';
```
*说明：表名、字段名改为 `snake_case`。为 JSON 字段添加注释。*

### 2.3 bubble_session（泡泡会话表）
```sql
CREATE TABLE bubble_session (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '会话记录ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  session_uuid VARCHAR(50) NOT NULL COMMENT '会话唯一标识 (UUID)',
  plan_id INT COMMENT '关联的学习计划ID (可选)',
  start_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  end_time DATETIME COMMENT '结束时间',
  duration_seconds INT COMMENT '持续时间(秒)',
  bubble_count INT DEFAULT 0 COMMENT '本次会话生成的泡泡数量',
  interaction_count INT DEFAULT 0 COMMENT '本次会话的交互次数',
  session_data JSON COMMENT '会话相关的聚合数据 (例如生成的标签云，JSON结构需定义)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_session_uuid (session_uuid), -- 会话UUID需要索引
  INDEX idx_plan_id (plan_id),
  INDEX idx_start_time (start_time),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='泡泡交互会话记录表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。`session_id` 重命名为 `session_uuid` 并添加索引。`duration` 改为 `duration_seconds`。为 JSON 字段添加注释。*


## 三、系统配置模块

### 3.1 system_config（系统配置表）
```sql
CREATE TABLE system_config (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
  config_key VARCHAR(50) NOT NULL COMMENT '配置键 (例如: max_daily_exp)',
  config_value TEXT NOT NULL COMMENT '配置值',
  description TEXT COMMENT '配置描述',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否对客户端公开',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_config_key (config_key),
  INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统全局配置表';
```
*说明：表名、字段名改为 `snake_case`。唯一键命名改为 `uk_` 前缀。*

### 3.2 feature_flag（功能开关表）
```sql
CREATE TABLE feature_flag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '功能开关ID',
  flag_key VARCHAR(50) NOT NULL COMMENT '开关键 (例如: enable_template_market)',
  is_enabled BOOLEAN DEFAULT FALSE COMMENT '是否全局启用',
  description TEXT COMMENT '功能描述',
  user_percentage INT DEFAULT 100 COMMENT '灰度发布的用户百分比(0-100)',
  start_time DATETIME COMMENT '功能生效开始时间',
  end_time DATETIME COMMENT '功能生效结束时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_flag_key (flag_key),
  INDEX idx_is_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能开关控制表 (灰度发布)';
```
*说明：表名、字段名改为 `snake_case`。唯一键命名改为 `uk_` 前缀。*

### 3.3 user_feature_access（用户功能访问表）
```sql
CREATE TABLE user_feature_access (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '访问记录ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  feature_id INT NOT NULL COMMENT '功能开关ID',
  has_access BOOLEAN DEFAULT TRUE COMMENT '是否有访问权限 (独立于全局开关和百分比)',
  access_reason VARCHAR(50) COMMENT '特定访问原因 (例如: beta_tester, purchased)',
  granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  expires_at DATETIME COMMENT '过期时间 (用于临时权限)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_feature_id (feature_id),
  INDEX idx_has_access (has_access),
  UNIQUE KEY uk_user_feature (user_id, feature_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (feature_id) REFERENCES feature_flag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='特定用户功能访问权限表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。唯一键命名改为 `uk_` 前缀。*


## 四、模板市场模块

### 4.1 template_review（模板评价表）
```sql
CREATE TABLE template_review (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '评价ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  template_id INT NOT NULL COMMENT '模板ID',
  rating DECIMAL(2,1) NOT NULL COMMENT '评分(1-5，允许半星)',
  content TEXT COMMENT '评价内容',
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved' COMMENT '审核状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_user_id (user_id),
  INDEX idx_template_id (template_id),
  INDEX idx_rating (rating),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  UNIQUE KEY uk_user_template (user_id, template_id), -- 一个用户对一个模板只能评价一次
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (template_id) REFERENCES learning_template(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习模板评价表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。增加了 `deleted_at` 字段。唯一键命名改为 `uk_` 前缀。*

### 4.2 template_transaction（模板交易表）
```sql
CREATE TABLE template_transaction (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '交易ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  template_id INT NOT NULL COMMENT '模板ID',
  transaction_type ENUM('purchase', 'gift_send', 'gift_receive', 'reward', 'refund') NOT NULL COMMENT '交易类型',
  amount DECIMAL(10,2) NOT NULL COMMENT '金额 (正数表示收入/购买, 负数表示支出/退款)',
  currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币',
  payment_method VARCHAR(50) COMMENT '支付方式 (如 wechat_pay, alipay)',
  transaction_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending' COMMENT '交易状态',
  external_transaction_id VARCHAR(100) COMMENT '外部支付平台交易ID',
  related_transaction_id INT COMMENT '关联交易ID (如退款关联原购买)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_template_id (template_id),
  INDEX idx_transaction_type (transaction_type),
  INDEX idx_transaction_status (transaction_status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (template_id) REFERENCES learning_template(id) ON DELETE CASCADE,
  FOREIGN KEY (related_transaction_id) REFERENCES template_transaction(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习模板交易流水表';
```
*说明：表名、字段名改为 `snake_case`。`user_id` 类型改为 `BIGINT`。`external_transaction_id` 替代了 `transaction_id`。增加了 `related_transaction_id`。**修正了重复和大小写不一致的外键引用**。*

### 4.3 template_access（模板访问表）
```sql
CREATE TABLE template_access (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '访问权限ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  template_id INT NOT NULL COMMENT '模板ID',
  access_type ENUM('purchased', 'gifted', 'free', 'trial', 'rewarded') NOT NULL COMMENT '访问权限来源类型',
  granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  expires_at DATETIME COMMENT '过期时间 (NULL表示永不过期)',
  is_active BOOLEAN DEFAULT TRUE COMMENT '当前是否有效 (可用于手动禁用)',
  related_transaction_id INT COMMENT '关联的交易ID (可选)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_template_id (template_id),
  INDEX idx_access_type (access_type),
  INDEX idx_is_active (is_active),
  UNIQUE KEY uk_user_template (user_id, template_id), -- 一个用户对一个模板只有一条有效访问记录
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (template_id) REFERENCES learning_template(id) ON DELETE CASCADE,
  FOREIGN KEY (related_transaction_id) REFERENCES template_transaction(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户模板访问权限表';
```
*说明：**表名改为 `template_access`**。`user_id` 类型改为 `BIGINT`。增加了 `related_transaction_id`。唯一键命名改为 `uk_` 前缀。**修正了外键引用的大小写**。*

## 五、学习追踪设计说明

### 1. 学习活动追踪

LearningActivity表是学习追踪的核心，具有以下特点：

1. **全面活动记录**：
    - 记录用户与各类内容的交互活动
    - 支持多种活动类型和行为
    - 记录活动持续时间和详细信息

2. **活动类型**：
    - 练习(exercise)：与练习内容的交互
    - 观点(insight)：与观点内容的交互
    - 笔记(note)：与笔记内容的交互
    - 每日内容(daily_content)：与每日内容的交互
    - 模板(template)：与学习模板的交互
    - 泡泡(bubble)：与泡泡界面的交互

3. **行为类型**：
    - 查看(view)：浏览内容
    - 完成(complete)：完成内容（如练习）
    - 创建(create)：创建内容（如笔记）
    - 点赞(like)：点赞内容
    - 评论(comment)：评论内容
    - 分享(share)：分享内容
    - 开始/暂停/恢复(start/pause/resume)：学习计划状态变更

### 2. 每日记录与统计

DailyRecord和UserLearningStats表提供了不同粒度的学习数据统计：

1. **每日记录**：
    - 记录用户每天的学习情况
    - 包括学习时间、完成内容数量、获得经验值等
    - 支持连续学习天数(streak_days)追踪
    - 记录用户心情和学习总结

2. **总体统计**：
    - 汇总用户的整体学习数据
    - 记录最长连续学习天数和总学习天数
    - 存储用户喜爱的标签和学习模式数据
    - 用于生成学习报告和个性化推荐

### 3. 内容进度追踪

UserContentProgress表提供了细粒度的内容进度追踪：

1. **多类型内容进度**：
    - 支持练习、观点和每日内容的进度追踪
    - 记录进度百分比和完成状态

2. **熟练度评估**：
    - 通过proficiency_level字段评估用户对内容的掌握程度
    - 从未开始(not_started)到专家(expert)的五级评估

3. **交互时间记录**：
    - 记录完成时间和最后交互时间
    - 用于间隔重复学习算法和内容推荐

## 六、泡泡交互设计说明

泡泡交互是AIBUBB的特色功能，通过以下表结构支持：

### 1. 泡泡交互记录

BubbleInteraction表记录用户与泡泡界面的交互：

1. **交互类型**：
    - 点击(tap)：点击泡泡
    - 长按(hold)：长按泡泡
    - 合并(merge)：合并泡泡
    - 关闭(dismiss)：关闭泡泡

2. **位置数据**：
    - 通过position_data字段记录交互的位置信息
    - 支持分析用户交互模式和偏好

3. **结果追踪**：
    - 记录交互结果的内容类型和ID
    - 用于分析交互效果和优化推荐

### 2. 泡泡内容管理

BubbleContent表管理泡泡展示的内容：

1. **内容关联**：
    - 通过tag_id关联到特定标签
    - 支持多种内容类型的展示

2. **视觉属性**：
    - 通过visual_properties字段定义泡泡的视觉特性
    - 包括大小、颜色、动效等属性

3. **优先级管理**：
    - 通过priority字段控制内容展示优先级
    - 支持内容的动态调整和个性化推荐

### 3. 泡泡会话管理

BubbleSession表管理泡泡交互会话：

1. **会话追踪**：
    - 记录会话开始和结束时间
    - 计算会话持续时间和交互次数

2. **计划关联**：
    - 通过plan_id关联到学习计划
    - 支持基于学习计划的内容推荐

3. **会话数据**：
    - 通过session_data字段存储会话的详细数据
    - 用于分析用户交互模式和学习行为

## 七、系统配置设计说明

系统配置模块提供了灵活的系统设置和功能控制：

### 1. 系统配置管理

SystemConfig表提供了全局配置管理：

1. **键值存储**：
    - 通过config_key和config_value实现键值对存储
    - 支持各类系统参数的配置

2. **公开设置**：
    - 通过is_public字段控制配置是否对客户端可见
    - 保护敏感配置信息

### 2. 功能开关管理

FeatureFlag和UserFeatureAccess表实现了功能开关和访问控制：

1. **功能开关**：
    - 控制功能的启用和禁用
    - 支持按用户百分比逐步推出功能
    - 支持设置功能的有效时间范围

2. **用户访问控制**：
    - 控制特定用户对功能的访问权限
    - 记录授权原因和有效期
    - 支持基于用户行为的功能解锁

## 八、模板市场设计说明

模板市场模块支持学习模板的评价、交易和访问控制：

### 1. 模板评价系统

TemplateReview表实现了模板评价功能：

1. **评分机制**：
    - 用户可以对模板进行1-5星评分
    - 评分汇总显示在模板信息中

2. **评价内容**：
    - 用户可以提供文字评价
    - 评价需要经过审核（通过status字段控制）

### 2. 模板交易系统

TemplateTransaction表记录模板交易：

1. **交易类型**：
    - 购买(purchase)：用户购买模板
    - 赠送(gift)：用户赠送模板给其他用户
    - 奖励(reward)：系统奖励模板给用户
    - 退款(refund)：交易退款

2. **支付信息**：
    - 记录交易金额、货币和支付方式
    - 记录外部交易ID和交易状态

### 3. 模板访问控制

TemplateAccess表控制用户对模板的访问权限：

1. **访问类型**：
    - 购买(purchased)：通过购买获得访问权
    - 赠送(gifted)：通过赠送获得访问权
    - 免费(free)：免费获得访问权
    - 试用(trial)：临时试用访问权

2. **时间控制**：
    - 记录授权时间和过期时间
    - 支持临时访问和永久访问

3. **状态管理**：
    - 通过is_active字段控制访问权限的有效性
    - 支持访问权限的暂停和恢复





# 第6部分：迁移策略、索引与维护 (V3.0)

本部分详细定义了AIBUBB数据库 V3.0 版本的迁移策略、索引优化和维护建议，基于 V2.0 的基础并融入了新的设计原则。

## 一、迁移策略 (从 V2.0 或早期版本)

由于 V3.0 引入了主键变更、命名规范统一、JSON 拆分、`deleted_at` 字段等重大变化，从旧版本迁移将是一个复杂的过程。对于新项目，可以直接使用 V3.0 结构。对于已有数据的旧项目，建议采用以下策略：

1.  **停机迁移 (推荐)**: 在维护窗口期间停止服务，执行完整的结构变更和数据迁移。
2.  **在线迁移 (复杂)**: 使用工具（如 `pt-online-schema-change`）或分步逻辑（创建新表、同步数据、切换）进行在线迁移，复杂度高，风险大。

**以下步骤基于停机迁移策略:**

### 1. 迁移准备

1.  **备份现有数据**: 极其重要！备份旧数据库。
2.  **创建 V3.0 结构脚本**: 编写完整的 V3.0 `CREATE TABLE` 脚本。
3.  **编写数据迁移脚本**: 这是核心难点。
    *   需要处理 `user` 表主键变更（生成新的 `BIGINT` ID，并将旧 ID 映射到 `openid`/`phone`）。
    *   需要将旧表名/字段名映射到新的 `snake_case` 规范。
    *   需要将旧 `user_setting.notification_settings` JSON 数据解析并插入到 `user_notification_setting` 表。
    *   需要将旧 `daily_content.related_content_ids` JSON 数据解析并插入到 `daily_content_relation` 表。
    *   为所有表添加 `deleted_at = NULL`。
    *   更新所有外键关联。
4.  **编写回滚脚本**: 定义回滚到旧版本的步骤（如果迁移失败）。
5.  **测试环境充分验证**: 在与生产环境相似的测试库中反复演练迁移过程，验证数据准确性和耗时。

### 2. 迁移执行顺序 (基于 V3.0 依赖关系)

顺序需要仔细规划，确保基础表和关联表先创建。

#### 第一阶段：基础定义表 (无或少依赖)
```sql
-- 1. level (等级定义)
CREATE TABLE level (...);

-- 2. theme (主题)
CREATE TABLE theme (...);

-- 3. tag_category (标签分类)
CREATE TABLE tag_category (...);

-- 4. tag (标签)
CREATE TABLE tag (...);

-- 5. achievement (成就定义)
CREATE TABLE achievement (...);

-- 6. badge (徽章定义)
CREATE TABLE badge (...);

-- 7. reward (奖励定义)
CREATE TABLE reward (...);

-- 8. system_config (系统配置)
CREATE TABLE system_config (...);

-- 9. feature_flag (功能开关)
CREATE TABLE feature_flag (...);

-- 10. user (用户表 - 核心基础)
CREATE TABLE user (...); -- 依赖 level
```

#### 第二阶段：用户相关与核心关联表
```sql
-- 1. user_setting (用户设置)
CREATE TABLE user_setting (...); -- 依赖 user

-- 2. user_notification_setting (用户通知设置 - 新)
CREATE TABLE user_notification_setting (...); -- 依赖 user

-- 3. tag_synonym (标签同义词)
CREATE TABLE tag_synonym (...); -- 依赖 tag

-- 4. learning_template (学习模板)
CREATE TABLE learning_template (...); -- 依赖 theme, user

-- 5. template_tag (模板标签关联)
CREATE TABLE template_tag (...); -- 依赖 learning_template, tag

-- 6. learning_plan (学习计划)
CREATE TABLE learning_plan (...); -- 依赖 user, learning_template, theme

-- 7. plan_tag (计划标签关联)
CREATE TABLE plan_tag (...); -- 依赖 learning_plan, tag
```

#### 第三阶段：内容表
```sql
-- 1. exercise (练习)
CREATE TABLE exercise (...); -- 依赖 tag, user

-- 2. insight (观点)
CREATE TABLE insight (...); -- 依赖 tag, user

-- 3. note (笔记)
CREATE TABLE note (...); -- 依赖 user, tag

-- 4. daily_content (每日内容)
CREATE TABLE daily_content (...); -- 依赖 learning_plan

-- 5. daily_content_relation (每日内容关联 - 新)
CREATE TABLE daily_content_relation (...); -- 依赖 daily_content

-- 6. bubble_content (泡泡内容)
CREATE TABLE bubble_content (...); -- 依赖 tag
```

#### 第四阶段：交互与关系表
```sql
-- 1. note_like (笔记点赞)
CREATE TABLE note_like (...); -- 依赖 user, note

-- 2. tag_like (标签点赞)
CREATE TABLE tag_like (...); -- 依赖 user, tag

-- 3. note_comment (笔记评论)
CREATE TABLE note_comment (...); -- 依赖 user, note

-- 4. comment_like (评论点赞)
CREATE TABLE comment_like (...); -- 依赖 user, note_comment

-- 5. user_follow (用户关注)
CREATE TABLE user_follow (...); -- 依赖 user

-- 6. tag_feedback (标签反馈)
CREATE TABLE tag_feedback (...); -- 依赖 user, tag

-- 7. bubble_session (泡泡会话)
CREATE TABLE bubble_session (...); -- 依赖 user, learning_plan

-- 8. bubble_interaction (泡泡交互)
CREATE TABLE bubble_interaction (...); -- 依赖 user, tag, bubble_session (逻辑关联)
```

#### 第五阶段：用户进度与统计表 (依赖较多)
```sql
-- 1. user_content_progress (用户内容进度 - 单一可信源)
CREATE TABLE user_content_progress (...); -- 依赖 user, learning_plan

-- 2. learning_activity (学习活动 - 大表，带分区)
CREATE TABLE learning_activity (...); -- 依赖 user, learning_plan

-- 3. daily_record (每日记录 - 聚合)
CREATE TABLE daily_record (...); -- 依赖 user

-- 4. user_learning_stats (用户学习统计 - 聚合)
CREATE TABLE user_learning_stats (...); -- 依赖 user

-- 5. user_achievement (用户成就)
CREATE TABLE user_achievement (...); -- 依赖 user, achievement

-- 6. user_badge (用户徽章)
CREATE TABLE user_badge (...); -- 依赖 user, badge

-- 7. user_reward (用户奖励)
CREATE TABLE user_reward (...); -- 依赖 user, reward

-- 8. notification (通知)
CREATE TABLE notification (...); -- 依赖 user

-- 9. user_feature_access (用户功能访问)
CREATE TABLE user_feature_access (...); -- 依赖 user, feature_flag
```

#### 第六阶段：模板市场表
```sql
-- 1. template_review (模板评价)
CREATE TABLE template_review (...); -- 依赖 user, learning_template

-- 2. template_transaction (模板交易)
CREATE TABLE template_transaction (...); -- 依赖 user, learning_template

-- 3. template_access (模板访问)
CREATE TABLE template_access (...); -- 依赖 user, learning_template, template_transaction
```

### 3. 数据迁移步骤 (示例性说明)

实际脚本会复杂得多，这里仅为示意。

1.  **创建 V3.0 表结构**。
2.  **迁移 `user` 数据**: (最复杂)
    *   查询旧 `User` 表。
    *   为每条记录生成新的 `BIGINT` ID。
    *   将旧 `id` (varchar) 映射到 `openid` 或 `phone` 列。
    *   插入到新 `user` 表，包括 `deleted_at = NULL`。
    *   需要维护新旧 ID 的映射关系 (临时表或内存)，供后续迁移使用。
3.  **迁移其他基础表** (`theme`, `tag_category`, `tag` 等)，注意 `snake_case` 转换和添加 `deleted_at`。
4.  **迁移关联表** (`learning_template`, `learning_plan` 等)，使用新旧 ID 映射关系更新外键 (`user_id`, `creator_id` 等)。
5.  **迁移 JSON 拆分数据**: 
    *   查询旧 `user_setting`，解析 `notification_settings` JSON，使用新 `user_id` 插入 `user_notification_setting`。
    *   查询旧 `daily_content`，解析 `related_content_ids` JSON，使用新 `daily_content_id` 插入 `daily_content_relation`。
6.  **迁移剩余表** (`exercise`, `insight`, `note_like` 等)，注意 `snake_case` 和 `deleted_at` (如果适用)，并使用新 ID 映射更新所有相关的 `user_id` 外键。
7.  **验证数据**: 抽样检查数据一致性，核对记录数。

### 4. 迁移后工作

1.  **更新应用程序代码**: 全面适配新的表名 (`snake_case`)、字段名 (`snake_case`)、主键类型 (`BIGINT` for `user`) 和新的关联表/逻辑 (如通知设置、每日内容关联)。
2.  **重建统计聚合数据**: 首次运行时，需要根据 `user_content_progress` 和 `learning_activity` 数据，通过后台批处理任务重新计算并填充 `daily_record` 和 `user_learning_stats` 表。
3.  **监控性能**: 密切观察新结构下的数据库性能，特别是分区表和索引的表现。


## 二、索引优化 (V3.0)

V3.0 在 V2.0 的基础上，遵循更严格的规范，继续保持了良好的索引策略，并增加了对软删除字段的考虑：

1.  **主键索引**: 统一使用 `BIGINT AUTO_INCREMENT` (除特殊情况外，如配置表)，性能稳定。
2.  **外键索引**: 所有外键均已添加索引，确保关联查询效率。
3.  **唯一索引**: 使用 `uk_` 前缀，确保业务逻辑唯一性（如 `uk_user_date`, `uk_user_content_plan`, `uk_config_key`）。
4.  **常用查询索引**: 对 `status`, `type`, `created_at` 等常用过滤和排序字段添加索引。
5.  **复合索引**: 针对常见组合查询场景设计复合索引，注意字段顺序。
6.  **软删除索引**: 为所有包含 `deleted_at` 的表添加了 `INDEX idx_deleted_at (deleted_at)`，以优化查询活跃记录 (`WHERE deleted_at IS NULL`) 的性能。
7.  **全文索引**: 在 `exercise`, `insight`, `note` 表的关键内容字段上添加了 `FULLTEXT` 索引，满足基本内容搜索需求。
8.  **分区表索引**: 对于分区表（如 `learning_activity`），MySQL 默认创建本地索引 (Local Index)，即每个分区有独立的索引结构。查询时若能有效利用分区键 (`created_at`) 进行分区裁剪，性能会显著提升。
9.  **高基数索引监控**: 对于基数可能很高的字段（如 `learning_activity.content_id`, `bubble_interaction.session_id` 等 `VARCHAR` 或 `UUID` 类型字段）上的索引，需要特别监控其大小、选择性和查询命中率，如果发现索引效率低下或占用空间过大，应考虑优化（如使用前缀索引、哈希索引或调整为组合索引）。

**持续优化建议**: 保持监控慢查询日志 (Slow Query Log)，**定期执行 `ANALYZE TABLE` 来更新统计信息**，根据实际业务负载动态调整和添加索引。对于复杂的全文搜索或大规模聚合分析需求，强烈建议早期规划并引入 ElasticSearch (用于搜索)、ClickHouse (用于 OLAP 分析) 等外部专用引擎，以减轻主数据库压力，提升查询性能和扩展性。


## 三、数据库维护建议 (V3.0)

在 V2.0 的基础上，针对 V3.0 的变化补充以下建议：

### 1. 定期备份

(策略与 V2.0 相同，保持每日全备 + 增量/日志备份，并确保存储安全和可恢复性测试)

### 2. 性能监控

(策略与 V2.0 相同，监控关键指标，如 QPS, TPS, 连接数, 慢查询, 锁等待, 磁盘 IO, CPU, 内存等)

### 3. 数据清理与归档策略

1.  **分区表维护**: 
    *   对于按年分区的表（如 `learning_activity`），需要在每年年底前**手动添加新年份的分区**（例如 `ALTER TABLE learning_activity ADD PARTITION (PARTITION p2027 VALUES LESS THAN (2028));`），否则新年数据会插入失败或进入默认分区（如果设置了 `MAXVALUE`）。强烈建议在生产环境中通过运维脚本或 MySQL Event Scheduler 实现此操作的自动化，以防遗漏。
    *   定期评估旧分区数据的使用频率，考虑对极少访问的旧分区进行归档（例如使用 `mysqldump --tab` 导出分区数据到文件，然后 `ALTER TABLE ... DROP PARTITION ...`）或直接删除 (如果业务允许)。
    *   **其他大表归档**: 除了 `learning_activity`，还应评估 `bubble_interaction`, `user_content_progress`, `notification` 等可能快速增长的表的归档策略，例如按时间将历史数据迁移到归档库或冷存储中。

2.  **软删除数据清理**: 
    *   制定策略定期物理删除 `deleted_at` 不为空且超过设定保留期限（例如 180 天或 1 年）的记录，以回收存储空间并可能提升查询性能（减少扫描行数）。
    *   清理脚本示例：`DELETE FROM note WHERE deleted_at IS NOT NULL AND deleted_at < DATE_SUB(NOW(), INTERVAL 180 DAY) LIMIT 1000;` (建议分批删除，避免长时间锁表)。
3.  **聚合数据维护**: 
    *   确保用于计算聚合数据（如 `daily_record`, `user_learning_stats`）的后台任务（如夜间批处理）的稳定性和准确性。
    *   建立监控机制，定期校验聚合数据与源数据的一致性，必要时提供手动触发重算的功能。
4.  **JSON 字段监控**: 对于保留的少量 JSON 字段 (`privacy_settings`, `details` 等)，应通过应用层日志或采样分析，持续监控其平均大小和内部结构复杂度，防止因滥用或结构混乱导致性能问题。
5.  **索引维护与监控**: **定期运行 `ANALYZE TABLE`** 优化统计信息。监控索引的使用情况（例如通过 `performance_schema` 或第三方工具），识别并**清理冗余或未使用的索引**。特别关注高基数字段索引的性能表现。

### 4. 扩展性考虑

(策略与 V2.0 相同，分库分表、读写分离、缓存、数据库集群等仍是应对未来大规模增长的主要手段)。**同时，将复杂的搜索和分析查询卸载到专用的外部系统（如 ElasticSearch, ClickHouse）也是关键的扩展策略，能有效保护核心事务数据库的性能和稳定性。**

## 四、总结 (V3.0)

AIBUBB 数据库设计 V3.0 在 V2.0 的基础上，采纳了结构化评估的建议，进行了全面的优化和规范：

*   **统一了命名规范** (`snake_case`) 和主键策略 (推荐 `BIGINT AUTO_INCREMENT`)，提升了代码和文档的一致性与可读性。
*   **审慎处理 JSON 字段**，将核心结构化数据拆分到独立表（如 `user_notification_setting`, `daily_content_relation`），改善了数据约束和查询性能。
*   **明确了单一可信源原则**，区分实时进度表 (`user_content_progress`) 与聚合统计表 (`daily_record`, `user_learning_stats`)，降低了数据一致性风险。
*   **为潜在的大表预设了分区策略** (`learning_activity`)，提高了海量数据的可管理性。
*   **完善了软删除机制**，通过 `deleted_at` 字段及相应索引，支持数据审计和更高效的查询。

这些改进显著提升了数据库设计的规范性、可维护性和性能潜力，为 AIBUBB 系统构建了一个更健壮、更面向未来的数据基础。虽然对于已有系统，V3.0 的迁移成本较高，但其带来的长期收益，尤其是在系统复杂度、团队协作效率和系统稳定性方面，将是巨大的。





# 第7部分：高级数据库特性示例与最佳实践

本部分提供了AIBUBB数据库的高级特性示例和最佳实践，包括触发器、审计日志、JSON结构定义及分区管理自动化脚本，为开发团队提供实现参考。

## 一、触发器与审计日志示例

### 1.1 聚合计数触发器示例

以下触发器用于自动维护聚合计数字段，确保数据一致性：

```sql
-- 1. 笔记点赞计数触发器
DELIMITER //
CREATE TRIGGER after_note_like_insert
AFTER INSERT ON note_like
FOR EACH ROW
BEGIN
    -- 更新笔记的点赞计数
    UPDATE note SET like_count = like_count + 1
    WHERE id = NEW.note_id;
END; //

CREATE TRIGGER after_note_like_delete
AFTER DELETE ON note_like
FOR EACH ROW
BEGIN
    -- 点赞取消时减少计数
    UPDATE note SET like_count = like_count - 1
    WHERE id = OLD.note_id AND like_count > 0;
END; //
DELIMITER ;

-- 2. 笔记评论计数触发器
DELIMITER //
CREATE TRIGGER after_note_comment_insert
AFTER INSERT ON note_comment
FOR EACH ROW
BEGIN
    -- 只有顶级评论才增加评论计数（非回复）
    IF NEW.parent_id IS NULL THEN
        UPDATE note SET comment_count = comment_count + 1
        WHERE id = NEW.note_id;
    END IF;
END; //

CREATE TRIGGER after_note_comment_delete
AFTER DELETE ON note_comment
FOR EACH ROW
BEGIN
    -- 只有顶级评论才减少评论计数（非回复）
    IF OLD.parent_id IS NULL THEN
        UPDATE note SET comment_count = comment_count - 1
        WHERE id = OLD.note_id AND comment_count > 0;
    END IF;
END; //
DELIMITER ;

-- 3. 学习计划进度更新触发器
DELIMITER //
CREATE TRIGGER after_user_content_progress_update
AFTER UPDATE ON user_content_progress
FOR EACH ROW
BEGIN
    DECLARE total_content INT;
    DECLARE completed_content INT;
    
    -- 当内容状态从非完成变为完成时
    IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
        -- 统计计划内容总数和已完成数
        SELECT COUNT(*) INTO total_content 
        FROM user_content_progress 
        WHERE plan_id = NEW.plan_id AND user_id = NEW.user_id;
        
        SELECT COUNT(*) INTO completed_content 
        FROM user_content_progress 
        WHERE plan_id = NEW.plan_id AND user_id = NEW.user_id AND status = 'completed';
        
        -- 更新计划进度百分比
        IF total_content > 0 THEN
            UPDATE learning_plan 
            SET progress = FLOOR((completed_content * 100) / total_content)
            WHERE id = NEW.plan_id AND user_id = NEW.user_id;
        END IF;
    END IF;
END; //
DELIMITER ;
```

### 1.2 标准审计日志表与触发器

以下是通用审计日志表及触发器示例，用于追踪关键数据变更：

```sql
-- 1. 审计日志表结构
CREATE TABLE audit_log (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  table_name VARCHAR(50) NOT NULL COMMENT '被修改的表名',
  record_id VARCHAR(50) NOT NULL COMMENT '记录ID',
  action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL COMMENT '操作类型',
  field_name VARCHAR(50) COMMENT '被修改的字段（仅UPDATE）',
  old_value TEXT COMMENT '原值',
  new_value TEXT COMMENT '新值',
  user_id BIGINT COMMENT '操作用户ID',
  ip_address VARCHAR(50) COMMENT '操作IP',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_table_record (table_name, record_id),
  INDEX idx_created_at (created_at),
  INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(created_at)) (
  PARTITION p_init VALUES LESS THAN (2024),
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026)
);

-- 2. 学习计划状态变更审计触发器
DELIMITER //
CREATE TRIGGER after_learning_plan_update
AFTER UPDATE ON learning_plan
FOR EACH ROW
BEGIN
    -- 记录状态变更
    IF OLD.status != NEW.status THEN
        INSERT INTO audit_log (table_name, record_id, action, field_name, old_value, new_value, user_id)
        VALUES ('learning_plan', NEW.id, 'UPDATE', 'status', OLD.status, NEW.status, 
                @current_user_id); -- 假设应用层设置了会话变量
    END IF;
    
    -- 记录进度变更
    IF OLD.progress != NEW.progress THEN
        INSERT INTO audit_log (table_name, record_id, action, field_name, old_value, new_value, user_id)
        VALUES ('learning_plan', NEW.id, 'UPDATE', 'progress', OLD.progress, NEW.progress, 
                @current_user_id);
    END IF;
END; //
DELIMITER ;

-- 3. 用户标签删除审计触发器
DELIMITER //
CREATE TRIGGER before_tag_delete
BEFORE DELETE ON tag
FOR EACH ROW
BEGIN
    -- 记录标签删除操作
    INSERT INTO audit_log (table_name, record_id, action, old_value, user_id)
    VALUES ('tag', OLD.id, 'DELETE', 
            CONCAT('{"name":"', OLD.name, '","category_id":', 
                  IFNULL(OLD.category_id, 'null'), '}'), 
            @current_user_id);
END; //
DELIMITER ;
```

### 1.3 应用触发器的最佳实践

在使用触发器维护聚合数据时，请遵循以下最佳实践：

1. **限制触发器复杂度**：触发器应简单直接，避免复杂的事务和逻辑。
2. **注意锁定影响**：触发器可能导致额外锁定，在高并发环境下需谨慎使用。
3. **提供替代方案**：为关键操作提供应用层替代逻辑，以防需要临时禁用触发器。
4. **记录触发器文档**：维护完整的触发器清单及其功能文档。
5. **定期审计**：确保触发器逻辑与业务需求一致，防止数据不一致。

对于高并发场景，可能需要考虑异步更新策略，例如通过消息队列和定时任务更新聚合计数，而非直接触发器。

## 二、关键JSON结构定义

以下是AIBUBB中主要JSON字段的结构定义和示例，以便前端和后端开发团队参考：

### 2.1 用户设置相关JSON

```json
-- user_setting.privacy_settings 结构示例
{
  "profile_visibility": "public", -- "public", "followers", "private"
  "show_learning_stats": true,     -- 是否展示学习统计
  "show_achievements": true,       -- 是否展示成就
  "show_notes": true,              -- 是否展示笔记
  "searchable": true               -- 是否可被搜索到
}

-- user_setting.learning_preferences 结构示例
{
  "preferred_study_time": "morning", -- "morning", "afternoon", "evening", "night"
  "daily_reminder": true,            -- 是否开启每日提醒
  "reminder_time": "08:00",          -- 提醒时间
  "notification_sound": "default",   -- 提醒声音
  "exercise_difficulty": "adaptive"  -- "beginner", "intermediate", "advanced", "adaptive"
}
```

### 2.2 学习活动相关JSON

```json
-- learning_activity.details 结构示例
{
  "duration_ms": 12500,             -- 详细毫秒级持续时间
  "completion_percentage": 80,      -- 完成百分比
  "interaction_count": 5,           -- 交互次数
  "source": "home_screen",          -- 来源页面
  "device_info": {                  -- 设备信息
    "type": "mobile",
    "os": "iOS",
    "version": "15.2"
  }
}
```

### 2.3 泡泡交互相关JSON

```json
-- bubble_content.visual_properties 结构示例
{
  "size": 60,                       -- 泡泡大小，单位px
  "color": "#3A86FF",               -- 主色
  "gradient": ["#3A86FF", "#8338EC"], -- 渐变色
  "animation": "bounce",            -- 动画效果
  "animation_duration": 1.5,        -- 动画持续时间(秒)
  "pulse_effect": true,             -- 是否有脉冲效果
  "border_width": 2,                -- 边框宽度
  "opacity": 0.9                    -- 透明度
}

-- bubble_interaction.position_data 结构示例
{
  "x": 120,                         -- x坐标
  "y": 240,                         -- y坐标
  "start_size": 50,                 -- 初始大小
  "end_size": 80,                   -- 结束大小
  "velocity": {                     -- 速度向量
    "x": 0.5,
    "y": -1.2
  },
  "screen_area": "center"           -- 屏幕区域
}

-- bubble_session.session_data 结构示例
{
  "tag_cloud": [
    {"id": 5, "weight": 0.8},
    {"id": 12, "weight": 0.5},
    {"id": 23, "weight": 0.3}
  ],
  "session_metrics": {
    "avg_interaction_time_ms": 850,
    "bubble_generation_count": 18,
    "merge_count": 3,
    "discovery_count": 2
  },
  "session_theme": "evening"
}
```

### 2.4 游戏化元素相关JSON

```json
-- achievement.condition_value 结构示例
{
  "type": "consecutive_days",       -- 条件类型
  "required_days": 7,               -- 所需天数
  "min_daily_minutes": 10,          -- 每日最低分钟数
  "valid_actions": ["exercise", "insight"], -- 有效活动类型
  "exempt_weekends": false          -- 是否排除周末
}

-- level.rewards 结构示例
{
  "badge_ids": [12, 15],            -- 解锁的徽章ID
  "feature_access": ["template_creation"], -- 解锁的功能
  "exp_bonus": 10,                  -- 经验值加成百分比
  "bubble_effects": ["golden_glow"] -- 解锁的泡泡特效
}

-- reward.value 结构示例
{
  "type": "badge",                  -- 奖励类型
  "badge_id": 25,                   -- 若为徽章，则徽章ID
  "exp_points": 0,                  -- 若为经验值，则数量
  "theme_id": 0,                    -- 若为主题，则主题ID
  "feature_id": 0,                  -- 若为功能，则功能ID
  "template_id": 0,                 -- 若为模板，则模板ID
  "duration_days": 0                -- 若为临时奖励，则持续天数
}
```

### 2.5 用户统计相关JSON

```json
-- user_learning_stats.favorite_tags 结构示例
{
  "by_frequency": [
    {"id": 45, "name": "编程基础", "count": 58},
    {"id": 23, "name": "Python", "count": 42},
    {"id": 78, "name": "算法", "count": 27}
  ],
  "by_time_spent": [
    {"id": 45, "name": "编程基础", "minutes": 240},
    {"id": 78, "name": "算法", "minutes": 180},
    {"id": 23, "name": "Python", "minutes": 120}
  ]
}

-- user_learning_stats.learning_patterns 结构示例
{
  "time_distribution": {
    "morning": 35,     -- 百分比
    "afternoon": 25,
    "evening": 30,
    "night": 10
  },
  "weekday_distribution": {
    "monday": 15,
    "tuesday": 20,
    "wednesday": 15,
    "thursday": 20,
    "friday": 15,
    "saturday": 5,
    "sunday": 10
  },
  "content_preference": {
    "exercise": 40,
    "insight": 35,
    "note": 25
  }
}
```

## 三、分区管理自动化脚本

以下脚本用于自动化管理表分区，包括添加新分区和归档旧分区：

### 3.1 MySQL事件调度器脚本

```sql
-- 1. 创建分区管理存储过程
DELIMITER //
CREATE PROCEDURE manage_partitions()
BEGIN
    DECLARE next_year INT;
    DECLARE partition_name VARCHAR(10);
    DECLARE archive_year INT;
    DECLARE archive_partition VARCHAR(10);
    
    -- 获取下一年
    SELECT YEAR(DATE_ADD(NOW(), INTERVAL 1 YEAR)) INTO next_year;
    
    -- 准备分区名
    SET partition_name = CONCAT('p', next_year);
    
    -- 为大表添加下一年分区（如果不存在）
    -- 1. learning_activity 表
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.partitions 
        WHERE table_name = 'learning_activity' 
        AND partition_name = partition_name
    ) THEN
        SET @sql = CONCAT('ALTER TABLE learning_activity ADD PARTITION (PARTITION ', 
                         partition_name, ' VALUES LESS THAN (', next_year + 1, '));');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
    
    -- 同样为 audit_log 表添加分区
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.partitions 
        WHERE table_name = 'audit_log' 
        AND partition_name = partition_name
    ) THEN
        SET @sql = CONCAT('ALTER TABLE audit_log ADD PARTITION (PARTITION ', 
                         partition_name, ' VALUES LESS THAN (', next_year + 1, '));');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
    
    -- 归档过旧分区数据（比如2年前的数据）
    SET archive_year = YEAR(DATE_SUB(NOW(), INTERVAL 2 YEAR));
    SET archive_partition = CONCAT('p', archive_year);
    
    -- 检查并归档 learning_activity 表的旧分区
    IF EXISTS (
        SELECT 1 FROM information_schema.partitions 
        WHERE table_name = 'learning_activity' 
        AND partition_name = archive_partition
    ) THEN
        -- 导出该分区数据到归档表
        SET @archive_sql = CONCAT('CREATE TABLE IF NOT EXISTS learning_activity_archive_', 
                                 archive_year, ' LIKE learning_activity;');
        PREPARE stmt FROM @archive_sql;
        EXECUTE stmt;
        
        SET @insert_sql = CONCAT('INSERT INTO learning_activity_archive_', 
                               archive_year, 
                               ' SELECT * FROM learning_activity PARTITION(', 
                               archive_partition, ');');
        PREPARE stmt FROM @insert_sql;
        EXECUTE stmt;
        
        -- 删除原分区
        SET @drop_sql = CONCAT('ALTER TABLE learning_activity DROP PARTITION ', 
                              archive_partition, ';');
        PREPARE stmt FROM @drop_sql;
        EXECUTE stmt;
        
        DEALLOCATE PREPARE stmt;
    END IF;
    
    -- 同样处理 audit_log 表
    IF EXISTS (
        SELECT 1 FROM information_schema.partitions 
        WHERE table_name = 'audit_log' 
        AND partition_name = archive_partition
    ) THEN
        -- 导出该分区数据到归档表
        SET @archive_sql = CONCAT('CREATE TABLE IF NOT EXISTS audit_log_archive_', 
                                 archive_year, ' LIKE audit_log;');
        PREPARE stmt FROM @archive_sql;
        EXECUTE stmt;
        
        SET @insert_sql = CONCAT('INSERT INTO audit_log_archive_', 
                               archive_year, 
                               ' SELECT * FROM audit_log PARTITION(', 
                               archive_partition, ');');
        PREPARE stmt FROM @insert_sql;
        EXECUTE stmt;
        
        -- 删除原分区
        SET @drop_sql = CONCAT('ALTER TABLE audit_log DROP PARTITION ', 
                              archive_partition, ';');
        PREPARE stmt FROM @drop_sql;
        EXECUTE stmt;
        
        DEALLOCATE PREPARE stmt;
    END IF;
END //
DELIMITER ;

-- 2. 创建每年12月自动执行的事件
CREATE EVENT IF NOT EXISTS yearly_partition_management
ON SCHEDULE EVERY 1 YEAR
STARTS '2024-12-01 01:00:00'
DO
    CALL manage_partitions();

-- 3. 确保Event Scheduler已启用
SET GLOBAL event_scheduler = ON;
```

### 3.2 季度分区管理脚本

以下是按季度管理分区的扩展版本，适用于需要更细粒度控制的场景：

```sql
-- 按季度管理分区的存储过程
DELIMITER //
CREATE PROCEDURE manage_quarterly_partitions()
BEGIN
    DECLARE next_quarter_start DATE;
    DECLARE next_quarter_year INT;
    DECLARE next_quarter_num INT;
    DECLARE partition_name VARCHAR(10);
    DECLARE archive_date DATE;
    DECLARE archive_partition VARCHAR(10);
    
    -- 获取下一个季度的开始日期
    SET next_quarter_start = DATE_ADD(
        MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) * 3 MONTH,
        INTERVAL 3 MONTH
    );
    SET next_quarter_year = YEAR(next_quarter_start);
    SET next_quarter_num = QUARTER(next_quarter_start);
    
    -- 准备分区名
    SET partition_name = CONCAT('p', next_quarter_year, 'Q', next_quarter_num);
    
    -- 为 bubble_interaction 表添加下一季度分区
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.partitions 
        WHERE table_name = 'bubble_interaction' 
        AND partition_name = partition_name
    ) THEN
        SET @sql = CONCAT(
            'ALTER TABLE bubble_interaction ADD PARTITION (PARTITION ', 
            partition_name, 
            ' VALUES LESS THAN (UNIX_TIMESTAMP(''',
            DATE_ADD(next_quarter_start, INTERVAL 3 MONTH),
            ''')))');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
    
    -- 归档两年前的同一季度的分区
    SET archive_date = DATE_SUB(next_quarter_start, INTERVAL 2 YEAR);
    SET archive_partition = CONCAT(
        'p', 
        YEAR(archive_date), 
        'Q', 
        QUARTER(archive_date)
    );
    
    -- 检查并归档旧分区
    IF EXISTS (
        SELECT 1 FROM information_schema.partitions 
        WHERE table_name = 'bubble_interaction' 
        AND partition_name = archive_partition
    ) THEN
        -- 归档流程，与年度版类似
        -- [归档代码略]
    END IF;
END //
DELIMITER ;

-- 创建每季度执行一次的事件
CREATE EVENT IF NOT EXISTS quarterly_partition_management
ON SCHEDULE EVERY 3 MONTH
STARTS '2024-03-15 01:00:00'
DO
    CALL manage_quarterly_partitions();
```

### 3.3 分区管理最佳实践

在使用分区时，请遵循以下最佳实践：

1. **提前规划分区策略**：根据数据增长速度确定合适的分区粒度（年、季度、月）。
2. **监控分区大小**：定期检查各分区大小，避免单个分区过大。
3. **测试归档流程**：在生产环境应用前，在测试环境验证归档流程。
4. **保留归档数据**：归档数据应保存在可查询但低成本的存储中。
5. **备份事件调度器**：确保在数据库备份中包含事件调度器的定义。
6. **监控执行日志**：为分区管理过程添加日志记录，并定期检查执行情况。

通过这些自动化脚本，可以大幅降低分区管理的人工操作，减少维护风险，提高系统的可靠性和可扩展性。
