# 数据库升级文档 (2023)

## 背景

AIBUBB项目经过多次迭代和升级，数据库结构也随之演变。本文档记录了最新的数据库升级过程，包括结构修复、外键约束完善和代码质量提升。

## 数据库结构现状

### 核心表

1. **User**: 用户信息表
2. **LearningPlan**: 学习计划表
3. **Tag**: 标签表
4. **PlanTag**: 学习计划和标签的关联表（多对多关系）
5. **Exercise**: 练习表
6. **Insight**: 观点表
7. **Note**: 笔记表

### 关系结构

- **LearningPlan和Tag**: 通过PlanTag表实现多对多关系
- **Tag和Exercise/Insight/Note**: 一对多关系
- **User和LearningPlan**: 一对多关系

### 预留功能表

以下表是为未来功能预留的，目前没有对应的模型：

1. **Achievement**: 成就系统表
2. **Level**: 用户等级表
3. **USettings**: 用户设置表
4. **UserAchievement**: 用户成就关联表
5. **UserContentProgress**: 用户内容进度表
6. **UserLearningStats**: 用户学习统计表
7. **UserReward**: 用户奖励表

## 升级内容

### 1. 数据库结构修复

#### 1.1 创建DailyContent表

DailyContent表在模型中定义，但实际不存在，我们创建了该表并设置了正确的外键约束：

```sql
CREATE TABLE DailyContent (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL COMMENT '所属学习计划ID',
  day_number INT NOT NULL COMMENT '天数（第几天）',
  title VARCHAR(100) NOT NULL COMMENT '日内容标题',
  content TEXT NOT NULL COMMENT '日内容详情',
  is_completed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已完成',
  completion_date DATETIME NULL COMMENT '完成日期',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX daily_content_plan_id_idx (plan_id),
  UNIQUE INDEX daily_content_plan_day_idx (plan_id, day_number),
  CONSTRAINT fk_daily_content_plan FOREIGN KEY (plan_id) REFERENCES LearningPlan(id) ON DELETE CASCADE
);
```

#### 1.2 保留预留功能表

我们保留了Achievement、Level、USettings等表，因为它们可能是为未来功能预留的。这些表构成了一个完整的用户成就和奖励系统，以及学习进度跟踪系统。

### 2. 代码质量提升

#### 2.1 修复DailyContent模型与表名不一致的问题

将DailyContent模型中的表名从"daily_contents"修改为"DailyContent"，保持一致性：

```javascript
// 修改前
tableName: 'daily_contents',

// 修改后
tableName: 'DailyContent',
```

#### 2.2 统一错误处理策略

创建了通用的错误处理工具（errorHandler.js），提供了以下功能：

- handleError: 处理通用错误
- handleApiError: 处理API错误
- handleValidationError: 处理验证错误
- handleDatabaseError: 处理数据库错误
- handleAIServiceError: 处理AI服务错误
- handleUnauthorizedError: 处理未授权错误
- handleNotFoundError: 处理资源不存在错误

## 升级脚本

我们创建了以下脚本来执行升级：

1. **create_daily_content_table.js**: 创建DailyContent表
2. **database_code_quality_upgrade.js**: 主脚本，执行所有升级任务

## 验证步骤

升级完成后，我们执行了以下验证：

1. 检查DailyContent表是否创建成功
2. 检查DailyContent模型是否已更新
3. 检查errorHandler.js文件是否存在
4. 测试相关功能是否正常工作

## 后续建议

1. **使用迁移脚本管理数据库变更**：
   - 所有数据库结构变更应通过迁移脚本管理
   - 迁移脚本应包含向前和向后迁移的逻辑

2. **完善文档**：
   - 及时更新DATABASE-DESIGN.md文档
   - 记录数据库结构的变更历史

3. **代码审查**：
   - 确保所有使用DailyContent模型的代码都能正确工作
   - 检查是否有其他地方使用了不存在的表或模型

4. **测试**：
   - 对涉及DailyContent的功能进行全面测试
   - 确保系统在升级后能正常工作
