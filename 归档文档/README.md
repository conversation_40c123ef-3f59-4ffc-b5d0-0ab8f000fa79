# AIBUBB 归档文档索引

本目录包含了AIBUBB项目中所有非前端相关的文档，这些文档已被归档以保持项目根目录的整洁。

## 📁 目录结构

### 🔧 后端代码 (`后端代码/`)
- `backend/` - 完整的后端代码目录
- `mysql/` 和 `mysql-init/` - MySQL配置和初始化脚本
- `redis/` - Redis配置文件
- `logs/` - 日志文件目录
- `backups/` - 数据库备份文件
- `mcp-mysql-server/` - MySQL MCP服务器

### 📋 后端文档 (`后端文档/`)
- `AIBUBB后端升级计划.md` - 后端系统升级计划

### 🗄️ 数据库文档 (`数据库文档/`)
- `DATABASE-DESIGN.md` - 数据库设计文档
- `DATABASE-DESIGN-UPDATE-2023.md` - 数据库设计更新
- `DATABASE-FIX-COMPLETE.md` - 数据库修复完成报告
- `DATABASE-FIX.md` - 数据库修复文档
- `DATABASE-UPGRADE-2023.md` - 数据库升级文档
- `DATABASE-UPGRADE-V3.md` - 数据库V3升级文档

### 🔌 API文档 (`API文档/`)
- `API-DESIGN.md` - API设计规范
- `API-ENDPOINTS.md` - API端点列表
- `API-STATISTICS.md` - API统计文档
- `API-First设计实施计划.md` - API优先设计实施计划
- `API-First设计工作进度报告.md` - API设计工作进度
- `API-First设计当前状况评估报告.md` - API设计评估报告
- `API变更管理流程.md` - API变更管理
- `API变更通知实施方案.md` - API变更通知方案
- `API文档自动生成实施方案.md` - API文档生成方案
- `API版本使用指南.md` - API版本使用指南
- `API版本差异文档.md` - API版本差异
- `API版本路由实施方案.md` - API版本路由方案
- `API设计优化方案.md` - API设计优化
- `API设计审查流程.md` - API设计审查
- `API设计工具选型评估.md` - API设计工具评估
- `API设计规范.md` - API设计规范
- `SWAGGER-EXAMPLES.md` - Swagger示例
- `Swagger注释模板.md` - Swagger注释模板
- `swagger-annotation-standards.md` - Swagger注释标准

### 🏗️ 架构文档 (`架构文档/`)
- `ARCHITECTURE-PRINCIPLES.md` - 架构本质原理
- `PROJECT-ARCHITECTURE.md` - 项目架构说明
- `architecture-diagrams.md` - 架构图文档
- `api-documentation-architecture-analysis.md` - API文档架构分析

### 🐳 容器化文档 (`容器化文档/`)
- `AIBUBB容器化升级计划.md` - 容器化升级计划
- `DOCKER-README.md` - Docker部署指南
- `DOCKER-DEVELOPMENT.md` - Docker开发指南
- `如何把本地容器迁移到服务器.md` - 容器迁移指南

### 🚀 容器化部署 (`容器化部署/`)
- `docker-compose.yml` - Docker Compose配置文件
- `docker-backup.sh` - 数据库备份脚本
- `docker-restore.sh` - 数据库恢复脚本
- `docker-start.sh` - Docker启动脚本
- `docker-stop.sh` - Docker停止脚本
- `start.sh` - 启动脚本
- `migrate-env.sh` - 环境变量迁移脚本
- `validate-env.sh` - 环境变量验证脚本

### 💾 数据库脚本 (`数据库脚本/`)
- `complete_migration.sql` - 数据库迁移脚本
- `complete_migration_v3.sql` - 数据库V3迁移脚本
- `models-index-fixed.js` - 数据库模型索引修复

### 🤖 AI模型文档 (`AI模型文档/`)
- `AI-MODEL-OPTIMIZATION.md` - AI模型优化
- `AI-MODEL-TESTING.md` - AI模型测试

### 💼 业务文档 (`业务文档/`)
- `AI互动泡泡-商业计划书.md` - 商业计划书
- `AIBUBB系统全面升级计划-重组.md` - 系统升级计划
- `AIBUBB项目瘦身计划.md` - 项目瘦身计划

### 🎯 领域驱动设计文档 (`领域驱动设计文档/`)
- `AIBUBB领域驱动设计实施指南.md` - 领域驱动设计指南
- `领域驱动设计-基础架构实现.md` - 基础架构实现
- `领域驱动设计-学习内容领域分析.md` - 学习内容领域分析
- `领域驱动设计-学习内容领域实现.md` - 学习内容领域实现
- `领域驱动设计-架构调整方案.md` - 架构调整方案
- `领域驱动设计-架构问题分析.md` - 架构问题分析
- `领域驱动设计-标签领域实现.md` - 标签领域实现
- `领域驱动设计-领域模型设计.md` - 领域模型设计
- `领域驱动设计-领域知识梳理.md` - 领域知识梳理

### 🧪 测试脚本 (`测试脚本/`)
- `ai-test-results/` - AI测试结果目录
- `run-ai-tests.sh` - AI测试脚本
- `test-ai-service.sh` - AI服务测试脚本
- `_backup/` - 后端备份脚本目录
- `controllers/` - 后端控制器测试文件
- `models.test.js` - 数据库模型测试

### 🔧 工具脚本 (`工具脚本/`)
- `archive-md.sh` - 文档归档脚本
- `cleanup-backend.sh` - 后端清理脚本
- `cleanup-docker.sh` - Docker清理脚本
- `run-create-square-notes.sh` - 创建广场笔记脚本
- `run-docker-script.sh` - Docker运行脚本
- `copy-default-plans.sh` - 复制默认计划脚本
- `copy-plan-to-user.sh` - 复制计划到用户脚本
- `copy-plans-to-users.sh` - 批量复制计划脚本
- `detect-api-changes.js` - API变更检测脚本
- `dev-container.sh` - 开发容器脚本
- `run-create-tag-content.sh` - 创建标签内容脚本
- `schedule-api-change-detection.js` - API变更检测调度脚本
- `send-api-notifications.js` - API通知发送脚本

### 🔨 开发工具 (`开发工具/`)
- `wechat_devtools_mac_arm64.dmg` - 微信开发者工具安装包
- `create_custom_icon.py` - 自定义图标创建脚本
- `generate_book_icon.py` - 书籍图标生成脚本
- `generate_icons.py` - 图标生成脚本
- `icon_preview.html` - 图标预览页面

### 🛠️ 开发工具文档 (`开发工具文档/`)
- `MYSQL-MCP-NODE-SETUP.md` - MySQL MCP Node设置
- `cursor-mcp-guide.md` - Cursor MCP指南

### ⚙️ 配置文件 (`配置文件/`)
- `config/` - 后端配置文件目录
- `examples/` - 示例文件目录

### 📊 统计监控文档 (`统计监控文档/`)
- `statistics-module-design.md` - 统计模块设计
- `statistics-monitoring-optimization-plan.md` - 统计监控优化计划

### 📚 学习模板API文档 (`学习模板API文档/`)
- `学习模板API使用示例.md` - 学习模板API使用示例
- `学习模板API版本迁移指南.md` - 学习模板API版本迁移指南
- `标签推荐功能API使用示例.md` - 标签推荐功能API使用示例
- `标签的练习和观点.md` - 标签的练习和观点

### 🔄 系统改进文档 (`系统改进文档/`)
- `system-improvement-plan.md` - 系统改进计划
- `refactoring-analysis.md` - 重构分析
- `phase2-summary.md` - Phase 2总结

## 📝 说明

这些文档已从项目根目录移动到此归档目录，以便：

1. **保持项目根目录整洁** - 专注于前端开发相关的文档
2. **分类管理** - 按功能模块组织文档，便于查找
3. **历史保存** - 保留重要的技术和业务文档供参考

## 🔍 如何查找文档

1. 根据文档类型选择相应的子目录
2. 使用文件名关键词搜索
3. 参考本索引文件快速定位

## ⚠️ 注意事项

- 这些归档文档仍然是项目的重要组成部分
- 在需要了解系统架构、API设计等信息时，请参考相应的归档文档
- 如需更新这些文档，请在归档目录中进行修改

---

*归档日期：2025年1月*
*归档原因：项目重新定位为纯前端项目*
