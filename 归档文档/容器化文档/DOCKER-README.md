# AIBUBB Docker容器化部署指南

本文档提供了使用Docker容器化部署AIBUBB后端服务的详细说明。

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB可用内存
- 至少10GB可用磁盘空间

## 容器架构

AIBUBB Docker部署包含以下容器：

1. **aibubb-backend**: Node.js后端API服务
2. **aibubb-mysql**: MySQL 8.0数据库服务
3. **aibubb-redis**: Redis 7.0缓存服务
4. **mcp-mysql-server**: 用于数据库管理工具连接的辅助服务 (根据 docker-compose.yml 添加)

## 快速开始

### 启动服务

```bash
./docker-start.sh
```

启动后，服务将在以下地址可用：
- API服务: http://localhost:9090
- API文档: http://localhost:9090/api-docs

### 停止服务

```bash
./docker-stop.sh
```

## 数据持久化

所有数据都通过Docker卷进行持久化存储：

- **aibubb-mysql-data**: MySQL数据库文件 (命名卷)
- **aibubb-redis-data**: Redis数据文件 (命名卷)
- **./backend/logs**: 应用日志文件 (绑定挂载)

## 数据备份与恢复

### 备份数据库

```bash
./docker-backup.sh
```

备份文件将保存在`./backups`目录中。

### 恢复数据库

```bash
./docker-restore.sh ./backups/aibubb_backup_20230101_120000.sql.gz
```

## 环境变量配置

Docker 环境的主要环境变量配置通过项目根目录下的 `.env` 文件管理。

```bash
# .env 文件示例

# 应用端口
PORT=9090

# 数据库连接 (会被 docker-compose 覆盖为容器内连接)
DB_HOST=localhost
DB_PORT=3306
DB_USER=aibubb_user
DB_PASSWORD=your_db_password
DB_NAME=aibubb_db
DB_ROOT_PASSWORD=your_db_root_password

# Redis 连接 (会被 docker-compose 覆盖为容器内连接)
REDIS_URL=redis://localhost:6379

# JWT 密钥
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=1d

# 外部服务 API Key (示例)
# DEEPSEEK_API_KEY=your_deepseek_api_key

# 其他应用配置...
```

**重要说明：**

1.  **`.env` 文件:** 所有容器都会加载根目录下的 `.env` 文件来获取环境变量。
2.  **`docker-compose.yml` 覆盖:** `docker-compose.yml` 文件中的 `environment` 部分为容器间通信定义了特定的环境变量（如 `DB_HOST=mysql`, `REDIS_URL=redis://redis:6379`），**这些设置会覆盖 `.env` 文件中的同名变量**。
3.  **优先级:** `docker-compose.yml` 中的 `environment` 设置 > `.env` 文件。
4.  **必需变量:** 请确保 `.env` 文件中包含所有必需的变量，特别是 `DB_ROOT_PASSWORD`, `DB_NAME`, `DB_USER`, `DB_PASSWORD`, `JWT_SECRET` 以及任何外部 API 密钥。

## 健康检查

所有容器都配置了健康检查，确保服务正常运行：

- **后端API**: 每30秒检查一次HTTP响应
- **MySQL**: 每10秒检查一次连接状态
- **Redis**: 每10秒检查一次连接状态

## 资源限制

为防止资源过度使用，容器配置了资源限制：

- **后端API**: 最大2个CPU核心，2GB内存

## 故障排除

### 查看容器日志

```bash
# 查看所有容器日志
docker-compose logs

# 查看特定容器日志
docker-compose logs backend
docker-compose logs mysql
docker-compose logs redis

# 实时查看日志
docker-compose logs -f backend
```

### 常见问题

1. **数据库连接失败**
   - 检查MySQL容器是否正常运行
   - 检查环境变量中的数据库配置

2. **Redis连接失败**
   - 检查Redis容器是否正常运行
   - 应用会在Redis不可用时降级运行（无缓存模式）

3. **容器无法启动**
   - 检查端口冲突（9090, 3306, 6379）
   - 检查磁盘空间是否充足

## 自定义配置

如需自定义配置，可以修改以下文件：

- **docker-compose.yml**: 容器配置、端口映射、卷挂载、环境变量覆盖等。
- **./.env**: 项目根目录的环境变量文件，是主要的配置来源。
- **backend/Dockerfile**: 后端应用构建过程。
- **mysql-init/**: MySQL 初始化脚本目录。
- **redis/redis.conf**: Redis 配置文件。
