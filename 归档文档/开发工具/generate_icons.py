#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from PIL import Image, ImageDraw, ImageFont
import random
import math

# 创建图标保存目录
ICON_DIR = "assets/icons/new"
os.makedirs(ICON_DIR, exist_ok=True)

# 图标尺寸
ICON_SIZE = 64
ICON_BG_COLOR = (255, 255, 255, 0)  # 透明背景

# 图标样式
COLORS = [
    "#3B82F6",  # 蓝色
    "#10B981",  # 绿色
    "#F59E0B",  # 黄色
    "#EF4444",  # 红色
    "#8B5CF6",  # 紫色
    "#EC4899",  # 粉色
    "#14B8A6",  # 青色
    "#F97316",  # 橙色
    "#6366F1",  # 靛蓝色
    "#A855F7",  # 亮紫色
]

# 图标名称
ICON_NAMES = [
    "home", "profile", "message", "notification", "settings",
    "search", "favorite", "share", "comment", "like",
    "camera", "gallery", "location", "calendar", "clock",
    "cart", "order", "wallet", "gift", "coupon",
    "edit", "delete", "add", "minus", "close",
    "menu", "list", "grid", "chart", "graph",
    "user", "users", "lock", "unlock", "key",
    "file", "folder", "download", "upload", "refresh",
    "play", "pause", "stop", "forward", "backward",
    "audio", "video", "wifi", "bluetooth", "battery"
]

# 十种不同风格的图标
def create_circle_icon(draw, color, size):
    """绘制圆形图标"""
    radius = size // 2 - 4
    center = size // 2
    draw.ellipse((center-radius, center-radius, center+radius, center+radius), fill=color)
    
def create_square_icon(draw, color, size):
    """绘制方形图标"""
    margin = 8
    draw.rectangle((margin, margin, size-margin, size-margin), fill=color)
    
def create_rounded_square_icon(draw, color, size):
    """绘制圆角方形图标"""
    margin = 8
    radius = 8
    draw.rounded_rectangle((margin, margin, size-margin, size-margin), radius=radius, fill=color)
    
def create_diamond_icon(draw, color, size):
    """绘制菱形图标"""
    center = size // 2
    half_side = size // 2 - 8
    draw.polygon([
        (center, center - half_side),
        (center + half_side, center),
        (center, center + half_side),
        (center - half_side, center)
    ], fill=color)
    
def create_triangle_icon(draw, color, size):
    """绘制三角形图标"""
    margin = 8
    draw.polygon([
        (size//2, margin),
        (size-margin, size-margin),
        (margin, size-margin)
    ], fill=color)
    
def create_hexagon_icon(draw, color, size):
    """绘制六边形图标"""
    center = size // 2
    radius = size // 2 - 8
    points = []
    for i in range(6):
        angle = math.pi / 3 * i + math.pi / 6
        points.append((
            center + int(radius * math.cos(angle)),
            center + int(radius * math.sin(angle))
        ))
    draw.polygon(points, fill=color)
    
def create_star_icon(draw, color, size):
    """绘制星形图标"""
    center = size // 2
    outer_radius = size // 2 - 8
    inner_radius = outer_radius // 2
    points = []
    for i in range(10):
        angle = math.pi / 5 * i - math.pi / 2
        radius = outer_radius if i % 2 == 0 else inner_radius
        points.append((
            center + int(radius * math.cos(angle)),
            center + int(radius * math.sin(angle))
        ))
    draw.polygon(points, fill=color)
    
def create_cross_icon(draw, color, size):
    """绘制十字形图标"""
    center = size // 2
    width = 12
    margin = 8
    draw.rectangle((center - width//2, margin, center + width//2, size - margin), fill=color)
    draw.rectangle((margin, center - width//2, size - margin, center + width//2), fill=color)
    
def create_heart_icon(draw, color, size):
    """绘制心形图标"""
    center_x = size // 2
    center_y = size // 2
    width = size // 2 - 10
    height = width
    
    # 创建心形路径
    left_center = (center_x - width//4, center_y - height//4)
    right_center = (center_x + width//4, center_y - height//4)
    
    # 绘制两个圆形
    draw.ellipse((left_center[0] - width//4, left_center[1] - height//4, 
                 left_center[0] + width//4, left_center[1] + height//4), fill=color)
    draw.ellipse((right_center[0] - width//4, right_center[1] - height//4, 
                 right_center[0] + width//4, right_center[1] + height//4), fill=color)
    
    # 绘制三角形底部
    points = [
        (center_x - width//2, center_y),
        (center_x, center_y + height//2),
        (center_x + width//2, center_y)
    ]
    draw.polygon(points, fill=color)
    
def create_donut_icon(draw, color, size):
    """绘制圆环图标"""
    center = size // 2
    outer_radius = size // 2 - 8
    inner_radius = outer_radius // 2
    
    # 绘制外圆
    draw.ellipse((center - outer_radius, center - outer_radius, 
                 center + outer_radius, center + outer_radius), fill=color)
    
    # 绘制内圆（透明）
    draw.ellipse((center - inner_radius, center - inner_radius, 
                 center + inner_radius, center + inner_radius), fill=ICON_BG_COLOR)

# 图标生成函数列表
ICON_STYLES = [
    create_circle_icon,
    create_square_icon,
    create_rounded_square_icon,
    create_diamond_icon,
    create_triangle_icon,
    create_hexagon_icon,
    create_star_icon,
    create_cross_icon,
    create_heart_icon,
    create_donut_icon
]

def generate_icons():
    """生成50个图标"""
    for i, name in enumerate(ICON_NAMES):
        # 创建基础图像
        img = Image.new('RGBA', (ICON_SIZE, ICON_SIZE), ICON_BG_COLOR)
        draw = ImageDraw.Draw(img)
        
        # 随机选择颜色和样式
        color = COLORS[i % len(COLORS)]
        style_index = (i // len(COLORS)) % len(ICON_STYLES)
        
        # 将十六进制颜色转换为RGB
        color_rgb = tuple(int(color.lstrip('#')[j:j+2], 16) for j in (0, 2, 4))
        
        # 应用样式
        ICON_STYLES[style_index](draw, color_rgb, ICON_SIZE)
        
        # 保存图标
        filename = f"{name}.png"
        filepath = os.path.join(ICON_DIR, filename)
        img.save(filepath)
        print(f"创建图标: {filepath}")
        
        # 创建激活状态的图标
        filename_active = f"{name}-active.png"
        filepath_active = os.path.join(ICON_DIR, filename_active)
        
        # 深色图标
        active_color_rgb = tuple(max(0, c - 40) for c in color_rgb)
        
        # 创建新图像
        img_active = Image.new('RGBA', (ICON_SIZE, ICON_SIZE), ICON_BG_COLOR)
        draw_active = ImageDraw.Draw(img_active)
        
        # 应用相同样式但使用深色
        ICON_STYLES[style_index](draw_active, active_color_rgb, ICON_SIZE)
        
        # 保存激活状态图标
        img_active.save(filepath_active)
        print(f"创建激活图标: {filepath_active}")

if __name__ == "__main__":
    print("开始生成图标...")
    generate_icons()
    print(f"完成! 生成了 {len(ICON_NAMES)*2} 个图标在 {ICON_DIR} 目录") 