/**
 * 更新认证中间件脚本
 * 将所有路由文件中的authenticateJWT和optionalAuthJWT替换为authMiddleware
 */
const fs = require('fs');
const path = require('path');

// 路由目录
const routesDir = path.join(__dirname, '../backend/routes');

// 读取路由目录中的所有文件
fs.readdir(routesDir, (err, files) => {
  if (err) {
    console.error('读取目录失败:', err);
    return;
  }

  // 过滤出.js文件
  const routeFiles = files.filter(file => file.endsWith('.js'));

  // 处理每个路由文件
  routeFiles.forEach(file => {
    const filePath = path.join(routesDir, file);

    // 读取文件内容
    fs.readFile(filePath, 'utf8', (err, data) => {
      if (err) {
        console.error(`读取文件 ${file} 失败:`, err);
        return;
      }

      // 替换导入语句
      let newContent = data.replace(
        /const \{ authenticateJWT, optionalAuthJWT \} = require\(['"]\.\.\/middlewares\/auth\.middleware['"]\);/g,
        'const { authMiddleware } = require(\'../middlewares/auth.middleware\');'
      );

      // 替换单独导入authenticateJWT的语句
      newContent = newContent.replace(
        /const \{ authenticateJWT \} = require\(['"]\.\.\/middlewares\/auth\.middleware['"]\);/g,
        'const { authMiddleware } = require(\'../middlewares/auth.middleware\');'
      );

      // 替换单独导入optionalAuthJWT的语句
      newContent = newContent.replace(
        /const \{ optionalAuthJWT \} = require\(['"]\.\.\/middlewares\/auth\.middleware['"]\);/g,
        'const { authMiddleware } = require(\'../middlewares/auth.middleware\');'
      );

      // 替换authenticateJWT中间件
      newContent = newContent.replace(/authenticateJWT,/g, 'authMiddleware,');

      // 替换optionalAuthJWT中间件
      newContent = newContent.replace(/optionalAuthJWT,/g, 'authMiddleware,');

      // 如果内容有变化，写入修改后的内容
      if (newContent !== data) {
        fs.writeFile(filePath, newContent, 'utf8', err => {
          if (err) {
            console.error(`写入文件 ${file} 失败:`, err);
            return;
          }
          console.log(`成功更新文件: ${file}`);
        });
      } else {
        console.log(`文件 ${file} 无需更新`);
      }
    });
  });
});
