/**
 * 运行测试脚本
 * 用于运行单元测试和集成测试
 */
const { execSync } = require('child_process');
const path = require('path');

// 测试目录
const TEST_DIR = path.join(__dirname, '../backend/__tests__');

// 测试类型
const TEST_TYPES = {
  UNIT: 'unit',
  INTEGRATION: 'integration',
  ALL: 'all'
};

// 获取命令行参数
const args = process.argv.slice(2);
const testType = args[0] || TEST_TYPES.ALL;
const testPattern = args[1] || '';

// 构建测试命令
let testCommand = 'jest';

// 添加测试类型
if (testType === TEST_TYPES.UNIT) {
  testCommand += ' --testPathPattern=unit';
} else if (testType === TEST_TYPES.INTEGRATION) {
  testCommand += ' --testPathPattern=integration';
}

// 添加测试模式
if (testPattern) {
  testCommand += ` --testPathPattern=${testPattern}`;
}

// 添加其他选项
testCommand += ' --verbose';

// 运行测试
try {
  console.log(`运行测试: ${testCommand}`);
  execSync(testCommand, { stdio: 'inherit' });
  console.log('测试完成');
} catch (error) {
  console.error('测试失败:', error.message);
  process.exit(1);
}
