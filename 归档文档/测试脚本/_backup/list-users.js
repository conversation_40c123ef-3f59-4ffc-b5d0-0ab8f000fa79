const mysql = require('mysql2/promise');

async function listUsers() {
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'aibubb_db'
  });

  try {
    // 查询所有用户
    const [rows] = await connection.execute('SELECT id, nickname, gender, level, study_days FROM User');

    if (rows.length === 0) {
      console.log('数据库中没有用户');
      return;
    }

    console.log(`找到 ${rows.length} 个用户：`);
    rows.forEach(user => {
      console.log(`ID: ${user.id}, 昵称: ${user.nickname || '无昵称'}, 性别: ${user.gender}, 等级: ${user.level}, 学习天数: ${user.study_days}`);
    });
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    // 关闭连接
    await connection.end();
  }
}

// 执行函数
listUsers();
