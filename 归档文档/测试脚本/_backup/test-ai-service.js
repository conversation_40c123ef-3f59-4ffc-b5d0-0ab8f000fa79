// 测试AI服务是否可用
const axios = require('axios');

// 配置
const apiKey = 'be6068ae-2122-47b1-abda-0b2f1394dc7a';
const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
const model = 'deepseek-r1-250120';

// 创建HTTP客户端
const client = axios.create({
  baseURL: apiUrl,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`
  },
  timeout: 30000 // 30秒超时
});

// 测试函数
async function testAIService() {
  try {
    console.log('正在测试AI服务...');

    // 构建请求体
    const requestBody = {
      model: model,
      messages: [
        {
          role: 'user',
          content: '你好，请生成5个关于\'人际沟通\'的标签'
        }
      ],
      temperature: 0.7,
      max_tokens: 500
    };

    console.log('发送请求到:', apiUrl);
    console.log('使用模型:', model);

    // 发送请求
    const response = await client.post('', requestBody);

    // 输出响应
    console.log('AI服务响应成功!');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));

    // 解析标签
    if (response.data.choices && response.data.choices[0]) {
      const content = response.data.choices[0].message.content;
      console.log('生成的内容:', content);
    }

    // 输出token使用情况
    if (response.data.usage) {
      console.log('Token使用情况:');
      console.log('- 提示词tokens:', response.data.usage.prompt_tokens);
      console.log('- 生成tokens:', response.data.usage.completion_tokens);
      console.log('- 总tokens:', response.data.usage.total_tokens);
    }

    return true;
  } catch (error) {
    console.error('AI服务测试失败!');

    if (error.response) {
      // 服务器返回了错误响应
      console.error('状态码:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('没有收到响应:', error.request);
    } else {
      // 设置请求时发生错误
      console.error('错误信息:', error.message);
    }

    return false;
  }
}

// 执行测试
testAIService()
  .then(success => {
    if (success) {
      console.log('AI服务测试成功，服务可用!');
    } else {
      console.log('AI服务测试失败，服务不可用!');
    }
  })
  .catch(err => {
    console.error('测试过程中发生未处理的错误:', err);
  });
