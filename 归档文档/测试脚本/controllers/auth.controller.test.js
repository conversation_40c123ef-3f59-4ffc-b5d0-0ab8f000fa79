const authController = require('../../../controllers/auth.controller');
const { User } = require('../../../models');
const wechatUtils = require('../../../utils/wechat');
const jwtUtils = require('../../../utils/jwt');
const apiResponse = require('../../../utils/apiResponse');

// 模拟依赖
jest.mock('../../../models', () => ({
  User: {
    findOne: jest.fn(),
    create: jest.fn()
  }
}));

jest.mock('../../../utils/wechat', () => ({
  getWechatSession: jest.fn()
}));

jest.mock('../../../utils/jwt', () => ({
  generateToken: jest.fn()
}));

jest.mock('../../../utils/apiResponse', () => ({
  success: jest.fn(),
  error: jest.fn(),
  badRequest: jest.fn(),
  unauthorized: jest.fn()
}));

describe('Auth Controller', () => {
  // 模拟请求和响应对象
  let req;
  let res;

  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();

    // 初始化请求和响应对象
    req = {
      body: {}
    };
    res = {};

    // 模拟响应方法
    apiResponse.success.mockImplementation(() => res);
    apiResponse.error.mockImplementation(() => res);
    apiResponse.badRequest.mockImplementation(() => res);
    apiResponse.unauthorized.mockImplementation(() => res);
  });

  describe('login', () => {
    it('should login user successfully with valid code and create new user if not exists', async () => {
      // 模拟请求数据
      req.body = {
        code: 'valid-code',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg',
          gender: 1
        }
      };

      // 模拟微信会话响应
      wechatUtils.getWechatSession.mockResolvedValue({
        openid: 'test-openid',
        session_key: 'test-session-key'
      });

      // 模拟用户查询 - 用户不存在
      User.findOne.mockResolvedValue(null);

      // 模拟用户创建
      const mockUser = {
        id: 'user-1',
        openid: 'test-openid',
        nickname: '测试用户',
        avatar_url: 'https://example.com/avatar.jpg',
        gender: 1,
        study_days: 0,
        level: 1,
        last_login_at: expect.any(Date)
      };
      User.create.mockResolvedValue(mockUser);

      // 模拟JWT令牌生成
      jwtUtils.generateToken.mockReturnValue('test-token');

      // 调用控制器方法
      await authController.login(req, res);

      // 验证微信API调用
      expect(wechatUtils.getWechatSession).toHaveBeenCalledWith('valid-code');

      // 验证用户查询
      expect(User.findOne).toHaveBeenCalledWith({
        where: { openid: 'test-openid' }
      });

      // 验证用户创建
      expect(User.create).toHaveBeenCalledWith(expect.objectContaining({
        openid: 'test-openid',
        nickname: '测试用户',
        avatar_url: 'https://example.com/avatar.jpg',
        gender: 1
      }));

      // 验证JWT令牌生成
      expect(jwtUtils.generateToken).toHaveBeenCalledWith({
        userId: 'user-1',
        nickname: '测试用户'
      });

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(
        res,
        {
          token: 'test-token',
          userId: 'user-1',
          expiresIn: expect.any(Number)
        }
      );
    });

    it('should login existing user successfully', async () => {
      // 模拟请求数据
      req.body = {
        code: 'valid-code',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg',
          gender: 1
        }
      };

      // 模拟微信会话响应
      wechatUtils.getWechatSession.mockResolvedValue({
        openid: 'test-openid',
        session_key: 'test-session-key'
      });

      // 模拟用户查询 - 用户已存在
      const mockUser = {
        id: 'user-1',
        openid: 'test-openid',
        nickname: '测试用户',
        avatar_url: 'https://example.com/avatar.jpg',
        gender: 1,
        study_days: 5,
        level: 2,
        last_login_at: new Date(),
        update: jest.fn().mockResolvedValue(true)
      };
      User.findOne.mockResolvedValue(mockUser);

      // 模拟JWT令牌生成
      jwtUtils.generateToken.mockReturnValue('test-token');

      // 调用控制器方法
      await authController.login(req, res);

      // 验证微信API调用
      expect(wechatUtils.getWechatSession).toHaveBeenCalledWith('valid-code');

      // 验证用户查询
      expect(User.findOne).toHaveBeenCalledWith({
        where: { openid: 'test-openid' }
      });

      // 验证用户未创建
      expect(User.create).not.toHaveBeenCalled();

      // 验证用户更新
      expect(mockUser.update).toHaveBeenCalledWith(
        expect.objectContaining({
          nickname: '测试用户',
          avatar_url: 'https://example.com/avatar.jpg',
          last_login_at: expect.any(Date)
        })
      );

      // 验证JWT令牌生成
      expect(jwtUtils.generateToken).toHaveBeenCalledWith({
        userId: 'user-1',
        nickname: '测试用户'
      });

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(
        res,
        {
          token: 'test-token',
          userId: 'user-1',
          expiresIn: expect.any(Number)
        }
      );
    });

    it('should handle missing code', async () => {
      // 模拟请求数据 - 缺少code
      req.body = {
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg',
          gender: 1
        }
      };

      // 调用控制器方法
      await authController.login(req, res);

      // 验证未调用微信API
      expect(wechatUtils.getWechatSession).not.toHaveBeenCalled();

      // 验证错误响应
      expect(apiResponse.badRequest).toHaveBeenCalledWith(
        res,
        '缺少微信登录code'
      );
    });

    it('should handle wechat API errors', async () => {
      // 模拟请求数据
      req.body = {
        code: 'invalid-code',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg',
          gender: 1
        }
      };

      // 模拟微信API错误
      const error = new Error('微信API错误');
      wechatUtils.getWechatSession.mockRejectedValue(error);

      // 调用控制器方法
      await authController.login(req, res);

      // 验证微信API调用
      expect(wechatUtils.getWechatSession).toHaveBeenCalledWith('invalid-code');

      // 验证错误响应
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '登录失败',
        'AUTH_ERROR',
        500
      );
    });

    it('should handle database errors', async () => {
      // 模拟请求数据
      req.body = {
        code: 'valid-code',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg',
          gender: 1
        }
      };

      // 模拟微信会话响应
      wechatUtils.getWechatSession.mockResolvedValue({
        openid: 'test-openid',
        session_key: 'test-session-key'
      });

      // 模拟数据库错误
      const error = new Error('数据库错误');
      User.findOne.mockRejectedValue(error);

      // 调用控制器方法
      await authController.login(req, res);

      // 验证微信API调用
      expect(wechatUtils.getWechatSession).toHaveBeenCalledWith('valid-code');

      // 验证用户查询
      expect(User.findOne).toHaveBeenCalledWith({
        where: { openid: 'test-openid' }
      });

      // 验证错误响应
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '登录失败',
        'AUTH_ERROR',
        500
      );
    });
  });

  describe('getUserProfile', () => {
    it('should return user profile successfully', async () => {
      // 模拟请求对象
      req.user = { userId: 'user-1' };

      // 模拟用户查询
      const mockUser = {
        id: 'user-1',
        nickname: '测试用户',
        avatar_url: 'https://example.com/avatar.jpg',
        gender: 1,
        study_days: 10,
        level: 3,
        created_at: new Date('2023-01-01'),
        last_login_at: new Date('2023-01-10')
      };
      User.findOne.mockResolvedValue(mockUser);

      // 调用控制器方法
      await authController.getUserProfile(req, res);

      // 验证用户查询
      expect(User.findOne).toHaveBeenCalledWith({
        where: { id: 'user-1' }
      });

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(
        res,
        {
          id: 'user-1',
          nickname: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg',
          gender: 1,
          studyDays: 10,
          level: 3,
          createdAt: expect.any(Date),
          lastLoginAt: expect.any(Date)
        }
      );
    });

    it('should handle user not found', async () => {
      // 模拟请求对象
      req.user = { userId: 'non-existent-user' };

      // 模拟用户查询 - 用户不存在
      User.findOne.mockResolvedValue(null);

      // 调用控制器方法
      await authController.getUserProfile(req, res);

      // 验证用户查询
      expect(User.findOne).toHaveBeenCalledWith({
        where: { id: 'non-existent-user' }
      });

      // 验证错误响应
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '用户不存在',
        'USER_NOT_FOUND',
        404
      );
    });

    it('should handle database errors', async () => {
      // 模拟请求对象
      req.user = { userId: 'user-1' };

      // 模拟数据库错误
      const error = new Error('数据库错误');
      User.findOne.mockRejectedValue(error);

      // 调用控制器方法
      await authController.getUserProfile(req, res);

      // 验证用户查询
      expect(User.findOne).toHaveBeenCalledWith({
        where: { id: 'user-1' }
      });

      // 验证错误响应
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '获取用户信息失败',
        'SERVER_ERROR',
        500
      );
    });
  });
});
