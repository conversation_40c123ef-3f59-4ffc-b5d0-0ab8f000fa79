#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}===== AI模型服务提供商测试 =====${NC}"

# 创建结果目录
mkdir -p ai-test-results

# 检查环境变量
if [ ! -f .env ]; then
    echo -e "${RED}错误: 未找到.env文件，请先创建.env文件${NC}"
    echo -e "${YELLOW}提示: 可以复制.env.example为.env，并填入实际值${NC}"
    exit 1
fi

# 检查是否安装了必要的依赖
if ! npm list openai | grep -q openai; then
    echo -e "${YELLOW}安装必要的依赖...${NC}"
    npm install openai
fi

# 解析命令行参数
PROVIDER="all"
TASK="all"

for arg in "$@"; do
    case $arg in
        --provider=*)
        PROVIDER="${arg#*=}"
        shift
        ;;
        --task=*)
        TASK="${arg#*=}"
        shift
        ;;
        --help)
        echo -e "使用方法: ./run-ai-tests.sh [--provider=all|bytedance|aliyun|hunyuan] [--task=all|connection|tags|plan|creative]"
        echo -e "参数:"
        echo -e "  --provider: 指定要测试的提供商，默认为all"
        echo -e "  --task: 指定要执行的任务类型，默认为all"
        exit 0
        ;;
    esac
done

echo -e "${GREEN}测试提供商: ${PROVIDER}${NC}"
echo -e "${GREEN}测试任务: ${TASK}${NC}"

# 运行综合测试
echo -e "\n${YELLOW}运行综合测试...${NC}"
node backend/scripts/test-ai-models.js --provider=${PROVIDER} --task=${TASK}

# 生成评估报告
echo -e "\n${YELLOW}生成评估报告...${NC}"
node backend/scripts/evaluate-ai-models.js

echo -e "\n${GREEN}测试完成!${NC}"
echo -e "${GREEN}测试结果保存在 ai-test-results 目录中${NC}"
