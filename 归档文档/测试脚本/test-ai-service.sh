#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}===== 开始测试AI服务 =====${NC}"

# 测试阿里云百炼
echo -e "${GREEN}测试阿里云百炼...${NC}"
export AI_PROVIDER=aliyun
node backend/scripts/test-ai-service.js

# 测试字节大模型
echo -e "\n${GREEN}测试字节大模型...${NC}"
export AI_PROVIDER=bytedance
node backend/scripts/test-ai-service.js

# 测试腾讯混元
echo -e "\n${GREEN}测试腾讯混元...${NC}"
export AI_PROVIDER=hunyuan
node backend/scripts/test-ai-service.js

echo -e "${YELLOW}===== 测试完成 =====${NC}"
